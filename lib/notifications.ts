import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import Constants from 'expo-constants';
import { Platform } from 'react-native';
import { api } from './api';
import { log } from './remote-logging-transport';

export async function registerForPushNotificationsAsync() {
  if (Platform.OS === 'web') {
    return null;
  }

  if (!Device.isDevice) {
    console.log('Must use physical device for Push Notifications');
    return null;
  }

  try {
    const { status: existingStatus } = await Notifications.getPermissionsAsync();
    let finalStatus = existingStatus;

    log.info('Existing status: ' + existingStatus);

    if (existingStatus !== 'granted') {
      const { status } = await Notifications.requestPermissionsAsync();
      log.info('Existing status 2: ' + existingStatus);
      finalStatus = status;
    }

    if (finalStatus !== 'granted') {
      console.log('Failed to get push token for push notification!');
      return null;
    }

    // Make sure we're using the correct project ID
    const projectId = Constants.expoConfig?.extra?.eas?.projectId;
    log.info('Project ID:', projectId);
    if (!projectId) {
      console.error('Project ID not found in app config');
      return null;
    }
    const { data } = await Notifications.getExpoPushTokenAsync();
    console.log('Expo push token raw:', data);
    
    let token = await Notifications.getExpoPushTokenAsync({
      projectId,
    });
    log.info('Token: ' + token);
    let extractedToken;
    if (!!token && token.data) {
      extractedToken = token.data.match(/\[(.*?)\]/)?.[1];
    }

    console.log('Push token:', token);

    return extractedToken;
  } catch (error) {
    console.error('Error in registerForPushNotificationsAsync:', error);
    return null;
  }
}

export async function registerTokenWithBackend(token: string, existingTokens: string[] = []) {
  try {
    // log.error('IN REGister FN')
    // console.log('in register fn')
    // Check if token already exists
    if (!token) {
      log.info("Empty notification token");
    }
    // console.log('existing tokens', existingTokens)
    if (!existingTokens.includes(token)) {
      const payload = { token };
      log.info("Tokens to be sent to be", payload)
      await api.post('/notification-tokens', payload, {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      log.info('Token registered successfully');
      return true;
    } else {
      log.info('Token already registered');
      return false;
    }
  } catch (error) {
    log.error('Error registering token:', error);
    throw error;
  }
}

export function configurePushNotifications() {
  Notifications.setNotificationHandler({
    handleNotification: async () => ({
      shouldShowAlert: true,
      shouldPlaySound: true,
      shouldSetBadge: true,
      priority: Notifications.AndroidNotificationPriority.MAX
    }),
  });
}

export async function scheduleLocalNotification(title: string, body: string, data?: any) {
  if (Platform.OS === 'web') {
    return;
  }

  try {
    const notificationId = await Notifications.scheduleNotificationAsync({
      content: {
        title,
        body,
        data: data || {},
        sound: true,
        priority: Notifications.AndroidNotificationPriority.MAX,
      },
      trigger: null,
    });
    console.log('Scheduled notification:', notificationId);
    return notificationId;
  } catch (error) {
    console.error('Error scheduling notification:', error);
    throw error;
  }
}