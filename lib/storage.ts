import * as SecureStore from 'expo-secure-store';
import { Session } from '@/types/auth';

// Use alphanumeric keys for SecureStore compatibility
const SESSION_KEY = 'authSession';
const TOKEN_KEY = 'authToken';
const CREDENTIALS_KEY = 'lastCredentials';

class SecureStorage {
  async setSecureItem(key: string, value: string): Promise<void> {
    await SecureStore.setItemAsync(key, value);
  }

  async getSecureItem(key: string): Promise<string | null> {
    return await SecureStore.getItemAsync(key);
  }

  async removeSecureItem(key: string): Promise<void> {
    await SecureStore.deleteItemAsync(key);
  }

  async setSession(session: Session): Promise<void> {
    await this.setSecureItem(SESSION_KEY, JSON.stringify(session));
    if (session.token) {
      await this.setSecureItem(TOKEN_KEY, session.token);
    }
  }

  async getSession(): Promise<Session | null> {
    const data = await this.getSecureItem(SESSION_KEY);
    if (!data) return null;
    try {
      return JSON.parse(data);
    } catch (error) {
      console.error('Error parsing session:', error);
      return null;
    }
  }

  async getToken(): Promise<string | null> {
    return await this.getSecureItem(TOKEN_KEY);
  }

  async saveCredentials(credentials: { username: string; password: string }): Promise<void> {
    await this.setSecureItem(CREDENTIALS_KEY, JSON.stringify(credentials));
  }

  async getCredentials(): Promise<{ username: string; password: string } | null> {
    const data = await this.getSecureItem(CREDENTIALS_KEY);
    if (!data) return null;
    try {
      return JSON.parse(data);
    } catch (error) {
      console.error('Error parsing credentials:', error);
      return null;
    }
  }

  async clearCredentials(): Promise<void> {
    await this.removeSecureItem(CREDENTIALS_KEY);
  }

  async clearAll(): Promise<void> {
    await Promise.all([
      this.removeSecureItem(SESSION_KEY),
      this.removeSecureItem(TOKEN_KEY),
      this.removeSecureItem(CREDENTIALS_KEY),
    ]);
  }

  async clearSession(): Promise<void> {
    await Promise.all([
      this.removeSecureItem(SESSION_KEY),
      this.removeSecureItem(TOKEN_KEY),
    ]);
  }
}

export const storage = new SecureStorage();
