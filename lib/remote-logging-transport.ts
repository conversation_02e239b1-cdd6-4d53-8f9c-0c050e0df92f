import { logger } from "react-native-logs";

// Batched remote transport updated to match backend format
const createBatchedTransport = (batchSize = 10, maxWaitTime = 30000) => {
  let logQueue = [];
  let lastSendTime = Date.now();
  let sendTimer = null;

  const sendLogs = async () => {
    if (logQueue.length === 0) return;

    const logsToSend = [...logQueue];
    logQueue = [];
    lastSendTime = Date.now();

    // console.log('logs to send', { logs: logsToSend });

    try {
      await fetch('https://www.fgrealty.qa/api/logs/batch', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          // 'Authorization': 'Bearer YOUR_API_KEY'
        },
        body: JSON.stringify({ logs: logsToSend })
      });
    } catch (err) {
      console.error('Failed to send batched logs:', err);
    }
  };

  return (props) => {
    // Build the log entry with keys matching the backend controller
    const logEntry = {
      level: props.level?.text ?? "info", // one of: debug, info, warn, error, fatal
      message: props.msg ?? 'No message',
      timestamp: new Date().toISOString(),
      // Optionally set extra properties; these can be provided via props or props.rawMsg
      app_version: props.app_version || (props.rawMsg && props.rawMsg.app_version) || null,
      device_info: props.device_info || (props.rawMsg && props.rawMsg.device_info) || null,
      user_id: props.user_id || (props.rawMsg && props.rawMsg.user_id) || null,
      // Ensure context is sent as an array if available; otherwise default to an empty array.
      context: props.context || (props.rawMsg && props.rawMsg.context) || []
    };

    // Add to queue
    logQueue.push(logEntry);

    // Send immediately if it's an error/fatal or if the queue is full
    if (props.level === 'error' || props.level === 'fatal' || logQueue.length >= batchSize) {
      sendLogs();
    }
    // Otherwise, set a timer to ensure logs are sent eventually
    else if (!sendTimer) {
      sendTimer = setTimeout(() => {
        sendTimer = null;
        if (Date.now() - lastSendTime >= maxWaitTime) {
          sendLogs();
        }
      }, maxWaitTime);
    }
  };
};

// Create logger with updated batched transport
export const log = logger.createLogger({
  transport: [createBatchedTransport(5, 20000)],
  severity: 'debug'
});
