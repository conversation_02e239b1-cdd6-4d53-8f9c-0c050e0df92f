import { formatDistanceToNowStrict, parseISO as dateFnsParseISO, format } from 'date-fns';

export function parseISO(dateString: string): Date {
  return dateFnsParseISO(dateString);
}

export function formatDistanceToNow(date: Date | string, options?: { addSuffix?: boolean }): string {
  try {
    const dateObj = typeof date === 'string' ? dateFnsParseISO(date) : date;
    return formatDistanceToNowStrict(dateObj, {
      addSuffix: options?.addSuffix,
    });
  } catch (error) {
    console.error('Error formatting date distance:', error);
    return format(new Date(date), 'MMM d, yyyy');
  }
}

export function formatDate(date: Date | string): string {
  try {
    const dateObj = typeof date === 'string' ? dateFnsParseISO(date) : date;
    return format(dateObj, 'MMM d, yyyy');
  } catch (error) {
    console.error('Error formatting date:', error);
    return new Date(date).toLocaleDateString();
  }
}
