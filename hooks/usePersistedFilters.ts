import { useState, useEffect, useCallback, useRef } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface FilterState<T> {
  filters: T;
  loading: boolean;
  setFilters: (filters: Partial<T> | ((prev: T) => T)) => Promise<void>;
}

export function usePersistedFilters<T extends Record<string, any>>(
  key: string,
  defaultFilters: T
): FilterState<T> {
  const [filters, setFiltersState] = useState<T>(defaultFilters);
  const [loading, setLoading] = useState(true);
  const mounted = useRef(true);

  useEffect(() => {
    mounted.current = true;
    loadFilters();
    return () => {
      mounted.current = false;
    };
  }, []);

  const loadFilters = useCallback(async () => {
    try {
      const savedFilters = await AsyncStorage.getItem(key);
      if (savedFilters && mounted.current) {
        const parsedFilters = JSON.parse(savedFilters);
        setFiltersState({ ...defaultFilters, ...parsedFilters });
      }
    } catch (error) {
      console.error('Error loading filters:', error);
    } finally {
      if (mounted.current) {
        setLoading(false);
      }
    }
  }, [key, defaultFilters]);

  const setFilters = useCallback(async (newFilters: Partial<T> | ((prev: T) => T)) => {
    try {
      let updatedFilters: T;
      
      if (typeof newFilters === 'function') {
        updatedFilters = (newFilters as ((prev: T) => T))(filters);
      } else {
        updatedFilters = { ...filters, ...newFilters };
      }

      const cleanedFilters = Object.fromEntries(
        Object.entries(updatedFilters).filter(([_, value]) => value !== null && value !== undefined)
      ) as T;

      const finalFilters = { ...defaultFilters, ...cleanedFilters };

      await AsyncStorage.setItem(key, JSON.stringify(finalFilters));
      if (mounted.current) {
        setFiltersState(finalFilters);
      }
    } catch (error) {
      console.error('Error saving filters:', error);
      throw error;
    }
  }, [filters, key, defaultFilters]);

  return { filters, loading, setFilters };
}