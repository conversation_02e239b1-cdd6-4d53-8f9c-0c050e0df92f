import { useQuery } from '@tanstack/react-query';
import * as api from '@/lib/api';
import { Country } from '@/types/global';

const DEFAULT_COUNTRY: Country = {
  id: 1,
  code: 'QA',
  name: 'Qatar',
  phone_prefix: '+974'
};

function useQueryWithLogging(key: string, queryFn: () => Promise<any>) {
  return useQuery({
    queryKey: [key],
    queryFn: async () => {
      try {
        return await queryFn();
      } catch (error) {
        console.error(`Error in ${key} query:`, error);
        throw error;
      }
    },
  });
}

export function useListingTypes() {
  return useQueryWithLogging('listingTypes', api.fetchListingTypes);
}

export function useBedrooms() {
  return useQueryWithLogging('bedrooms', api.fetchBedrooms);
}

export function useBathrooms() {
  return useQueryWithLogging('bathrooms', api.fetchBathrooms);
}

export function useKitchens() {
  return useQueryWithLogging('kitchens', api.fetchKitchens);
}

export function useMarketingPlatforms() {
  return useQueryWithLogging('marketingPlatforms', api.fetchMarketingPlatforms);
}

export function useAmenities() {
  return useQueryWithLogging('amenities', api.fetchAmenities);
}

export function useListingViews() {
  return useQueryWithLogging('listingViews', api.fetchListingViews);
}

export function useLeadStatuses() {
  return useQueryWithLogging('leadStatuses', api.fetchLeadStatuses);
}

export function usePriceRanges() {
  return useQueryWithLogging('priceRanges', api.fetchPriceRanges);
}

export function useAreaRanges() {
  return useQueryWithLogging('areaRanges', api.fetchAreaRanges);
}

export function useNationalities() {
  return useQueryWithLogging('nationalities', api.fetchNationalities);
}

// Helper hook to fetch all configuration data at once
export function useAllConfiguration() {
  const listingTypes = useListingTypes();
  const bedrooms = useBedrooms();
  const bathrooms = useBathrooms();
  const kitchens = useKitchens();
  const marketingPlatforms = useMarketingPlatforms();
  const amenities = useAmenities();
  const listingViews = useListingViews();
  const leadStatuses = useLeadStatuses();
  const priceRanges = usePriceRanges();
  const areaRanges = useAreaRanges();

  const queries = {
    listingTypes,
    bedrooms,
    bathrooms,
    kitchens,
    marketingPlatforms,
    amenities,
    listingViews,
    leadStatuses,
    priceRanges,
    areaRanges,
  };

  // Log which queries are failing
  Object.entries(queries).forEach(([name, query]) => {
    if (query.isError) {
      console.error(`${name} query failed:`, query.error);
    }
  });

  const isLoading = Object.values(queries).some(query => query.isLoading);
  const isError = Object.values(queries).some(query => query.isError);

  return {
    listingTypes: listingTypes.data,
    bedrooms: bedrooms.data,
    bathrooms: bathrooms.data,
    kitchens: kitchens.data,
    marketingPlatforms: marketingPlatforms.data,
    amenities: amenities.data,
    listingViews: listingViews.data,
    leadStatuses: leadStatuses.data,
    priceRanges: priceRanges.data,
    areaRanges: areaRanges.data,
    isLoading,
    isError,
    // Add error details for debugging
    errors: Object.fromEntries(
      Object.entries(queries)
        .filter(([_, query]) => query.isError)
        .map(([name, query]) => [name, query.error])
    ),
  };
}

export function useCountries() {
  const { data, isLoading, error } = useQuery({
    queryKey: ['countries'],
    queryFn: async () => {
      const countries = await api.fetchCountries();
      return countries;
    }
  });

  const countries = data || [];

  // Find Qatar or use first country or default
  const defaultCountry = countries.find(country => country.code === 'QA')
    || countries[0]
    || DEFAULT_COUNTRY;

  return {
    countries,
    defaultCountry,
    isLoading,
    error
  };
}
