import { useState, useEffect } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, ActivityIndicator, Platform } from 'react-native';
import { useAuth } from '@/contexts/AuthContext';
import { storage } from '@/lib/storage';
import Logo from '@/components/Logo';
import Blinktech<PERSON>ogo from '@/components/BlinktechLogo';
import Constants from 'expo-constants';

function LoginScreen() {
  const { login, loading, error } = useAuth();
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [validationError, setValidationError] = useState<string | null>(null);
  const commitHash = Constants.expoConfig?.extra?.commitHash;

  useEffect(() => {
    loadSavedCredentials();
  }, []);

  const loadSavedCredentials = async () => {
    try {
      const savedCredentials = await storage.getCredentials();
      if (savedCredentials) {
        setUsername(savedCredentials.username);
        setPassword(savedCredentials.password);
      }
    } catch (error) {
      console.error('Error loading credentials:', error);
    }
  };

  const handleLogin = async () => {
    setValidationError(null);
    
    if (!username || !password) {
      setValidationError('Please fill in all fields');
      return;
    }

    try {
      await storage.saveCredentials({ username, password });
      await login({ username, password });
    } catch (err) {
      // Error is handled by the context
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <Logo />
        
        <Text style={styles.welcomeText}>Back to business</Text>
        <Text style={styles.subtitle}>Sign in to manage your properties</Text>
        
        {(error || validationError) && (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>{error || validationError}</Text>
          </View>
        )}
        
        <TextInput
          style={styles.input}
          placeholder="Email"
          placeholderTextColor="#6B7280"
          value={username}
          onChangeText={setUsername}
          autoCapitalize="none"
          keyboardType="email-address"
          editable={!loading}
        />
        
        <TextInput
          style={styles.input}
          placeholder="Password"
          placeholderTextColor="#6B7280"
          value={password}
          onChangeText={setPassword}
          secureTextEntry
          editable={!loading}
        />
        
        <TouchableOpacity 
          style={[styles.button, loading && styles.buttonDisabled]}
          onPress={handleLogin}
          disabled={loading}
        >
          {loading ? (
            <ActivityIndicator color="#fff" />
          ) : (
            <Text style={styles.buttonText}>Sign In</Text>
          )}
        </TouchableOpacity>
      </View>

      <View style={styles.footer}>
        <BlinktechLogo width={120} height={20} />
        <Text style={styles.footerText}>© 2025 FGREALTY All rights reserved.</Text>
        {commitHash && (
          <Text style={styles.versionText}>Build: {commitHash}</Text>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  content: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: Platform.select({ web: 40, default: 24 }),
    width: '100%',
    maxWidth: 480,
    alignSelf: 'center',
  },
  welcomeText: {
    fontSize: 32,
    fontWeight: '600',
    color: '#1A1A1A',
    textAlign: 'center',
    marginBottom: 12,
  },
  subtitle: {
    fontSize: 18,
    color: '#6B7280',
    marginBottom: 48,
    textAlign: 'center',
  },
  errorContainer: {
    backgroundColor: '#FEF2F2',
    borderRadius: 8,
    padding: 12,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: '#FEE2E2',
    width: '100%',
  },
  errorText: {
    color: '#DC2626',
    fontSize: 14,
    textAlign: 'center',
  },
  input: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
    fontSize: 16,
    color: '#1F2937',
    width: '100%',
  },
  button: {
    backgroundColor: '#B89C4C',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    width: '100%',
    marginTop: 8,
  },
  buttonDisabled: {
    backgroundColor: '#E5E7EB',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  footer: {
    padding: 24,
    alignItems: 'center',
    gap: 16, // Adds space between logo and copyright text
  },
  footerText: {
    color: '#6B7280',
    fontSize: 14,
  },
  versionText: {
    color: '#9CA3AF',
    fontSize: 12,
    marginTop: 4,
  },
});

export default LoginScreen;
