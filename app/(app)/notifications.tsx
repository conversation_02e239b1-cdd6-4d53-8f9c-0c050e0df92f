import React, { useState } from 'react';
import { View, Text, StyleSheet, FlatList, RefreshControl, TouchableOpacity, ActivityIndicator, SafeAreaView } from 'react-native';
import { Stack, router } from 'expo-router';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Bell, BellOff, ArrowLeft } from 'lucide-react-native';
import { api } from '@/lib/api';
import { formatDistanceToNow } from '@/lib/date';
import NotificationFilters from '@/components/NotificationFilters';
import FloatingFilterButton from '@/components/FloatingFilterButton';
import FilterDialog from '@/components/FilterDialog';
import NotificationDetailsModal from '@/components/NotificationDetailsModal';
import { Notification } from '@/types/global';
import NotificationItem from '@/components/NotificationItem';
import Button from '@/components/Button';

interface NotificationsResponse {
    data: Notification[];
    count: number;
}

interface MarkAsReadResponse {
    message: string;
    data: {
        id: number;
        read_at: string;
    };
}

const ITEMS_PER_PAGE = 20;

function parseNotificationData(notification: Notification): Notification {
    if (!notification.payload) return notification;

    try {
        const parsedData = typeof notification.payload == "string" ? JSON.parse(notification.payload) : notification.payload;
        return {
            ...notification,
            parsedData,
        };
    } catch (error) {
        console.error('Error parsing notification data:', error);
        return notification;
    }
}

function NotificationSkeleton() {
    return (
        <View style={styles.skeletonContainer}>
            <View style={styles.skeletonDot} />
            <View style={styles.skeletonContent}>
                <View style={styles.skeletonTitle} />
                <View style={styles.skeletonText} />
                <View style={styles.skeletonDate} />
            </View>
        </View>
    );
}

export default function NotificationsScreen() {
    const queryClient = useQueryClient();
    const [page, setPage] = useState(1);
    const [isFilterVisible, setIsFilterVisible] = useState(false);
    const [readStatus, setReadStatus] = useState<'all' | 'read' | 'unread'>('all');
    const [dateRange, setDateRange] = useState<'all' | 'today' | 'week' | 'month'>('all');
    const [markAsReadError, setMarkAsReadError] = useState<string | null>(null);
    const [selectedNotification, setSelectedNotification] = useState<Notification | null>(null);

    const { data, isLoading, error, refetch, isFetching } = useQuery({
        queryKey: ['notifications', page, readStatus, dateRange],
        queryFn: async () => {
            const params = new URLSearchParams({
                page: page.toString(),
                limit: ITEMS_PER_PAGE.toString(),
            });

            if (readStatus !== 'all') {
                params.append('read', readStatus === 'read' ? '1' : '0');
            }

            if (dateRange !== 'all') {
                params.append('date_range', dateRange);
            }

            const { data } = await api.get<NotificationsResponse>(
                `/profile/notifications?${params.toString()}`
            );

            return {
                ...data,
                data: data.data.map(parseNotificationData),
            };
        }
    });

    const markAsReadMutation = useMutation({
        mutationFn: async (id: number) => {
            const { data } = await api.post<MarkAsReadResponse>(`/profile/notifications/${id}/read`);
            return data;
        },
        onSuccess: (data) => {
            queryClient.setQueryData<NotificationsResponse>(['notifications', page, readStatus, dateRange], (old) => {
                if (!old) return old;

                return {
                    ...old,
                    data: old.data.map(notification =>
                        notification.id === data.data.id
                            ? { ...notification, read_at: data.data.read_at }
                            : notification
                    )
                };
            });

            setMarkAsReadError(null);
        },
        onError: (error: any) => {
            setMarkAsReadError(
                error.response?.data?.message ||
                'Failed to mark notification as read. Please try again.'
            );

            setTimeout(() => setMarkAsReadError(null), 3000);
        }
    });

    const handleNotificationPress = (notification: Notification) => {
        setSelectedNotification(notification);
        if (!notification.read_at) {
            markAsReadMutation.mutate(notification.id);
            notification.read_at = (new Date()).toISOString();
        }
    };

    const renderNotification = ({ item }: { item: Notification }) => (
        <NotificationItem
            notification={item}
            onPress={handleNotificationPress}
        />
    );

    if (error) {
        return (
            <SafeAreaView style={styles.container}>
                <View style={styles.centerContainer}>
                    <Text style={styles.errorText}>Failed to load notifications</Text>
                    <TouchableOpacity style={styles.retryButton} onPress={() => refetch()}>
                        <Text style={styles.retryButtonText}>Try Again</Text>
                    </TouchableOpacity>
                </View>
            </SafeAreaView>
        );
    }

    const notifications = data?.data ?? [];
    const totalCount = data?.count ?? 0;
    const unreadCount = notifications.filter(n => !n.read_at).length;

    return (
        <>
            <Stack.Screen
                options={{
                    headerShown: true,
                    header: () => (
                        <View style={styles.header}>
                            <View style={styles.backButtonContainer}>
                                <Button
                                    variant="ghost"
                                    icon={<ArrowLeft size={24} color="#111827" />}
                                    onPress={() => router.back()}
                                />
                            </View>
                            <Text style={styles.headerTitle}>Notifications</Text>
                            <View style={styles.backButtonContainer} />
                        </View>
                    ),
                }}
            />

            <SafeAreaView style={styles.container}>
                {markAsReadError && (
                    <View style={styles.errorBanner}>
                        <Text style={styles.errorBannerText}>{markAsReadError}</Text>
                    </View>
                )}

            {isLoading ? (
                <FlatList
                    data={Array(5).fill(null)}
                    renderItem={() => <NotificationSkeleton />}
                    contentContainerStyle={styles.list}
                />
            ) : notifications.length === 0 ? (
                <View style={styles.emptyState}>
                    <BellOff size={48} color="#9CA3AF" />
                    <Text style={styles.emptyTitle}>No notifications</Text>
                    <Text style={styles.emptyMessage}>
                        You're all caught up! New notifications will appear here.
                    </Text>
                </View>
            ) : (
                <FlatList
                    data={notifications}
                    renderItem={renderNotification}
                    keyExtractor={(item) => `notification_${item.id}`}
                    contentContainerStyle={styles.list}
                    refreshControl={
                        <RefreshControl
                            refreshing={isFetching && !isLoading}
                            onRefresh={refetch}
                            tintColor="#B89C4C"
                        />
                    }
                    onEndReached={() => {
                        if (notifications.length < totalCount) {
                            setPage(prev => prev + 1);
                        }
                    }}
                    onEndReachedThreshold={0.5}
                    ListFooterComponent={
                        isFetching && !isLoading ? (
                            <View style={styles.loadingMore}>
                                <ActivityIndicator color="#B89C4C" />
                            </View>
                        ) : null
                    }
                />
            )}

            <FloatingFilterButton onPress={() => setIsFilterVisible(true)} />

            <FilterDialog
                visible={isFilterVisible}
                onClose={() => setIsFilterVisible(false)}
                title="Filter Notifications"
            >
                <NotificationFilters
                    readStatus={readStatus}
                    dateRange={dateRange}
                    onReadStatusChange={(status) => {
                        setReadStatus(status);
                        setPage(1);
                    }}
                    onDateRangeChange={(range) => {
                        setDateRange(range);
                        setPage(1);
                    }}
                />
            </FilterDialog>

            {selectedNotification && (
                <NotificationDetailsModal
                    visible={true}
                    onClose={() => setSelectedNotification(null)}
                    notification={{
                        ...selectedNotification,
                        type: selectedNotification.parsedData?.type,
                        parsedData: selectedNotification.parsedData,
                    }}
                />
            )}
        </SafeAreaView>
        </>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#F9FAFB',
    },
    centerContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        padding: 20,
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        paddingHorizontal: 20,
        paddingTop: 60,
        paddingBottom: 20,
        backgroundColor: '#fff',
        borderBottomWidth: 1,
        borderBottomColor: '#E5E7EB',
        position: 'relative',
    },
    headerTitle: {
        fontSize: 20,
        fontWeight: '600',
        color: '#111827',
        textAlign: 'center',
        flex: 1,
    },
    backButtonContainer: {
        width: 40,
        alignItems: 'flex-start',
    },
    headerContent: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 12,
    },
    badge: {
        backgroundColor: '#B89C4C',
        borderRadius: 12,
        paddingHorizontal: 8,
        paddingVertical: 2,
        marginLeft: 8,
    },
    badgeText: {
        color: '#fff',
        fontSize: 12,
        fontWeight: '600',
    },
    errorBanner: {
        backgroundColor: '#FEF2F2',
        padding: 12,
        borderBottomWidth: 1,
        borderBottomColor: '#FEE2E2',
    },
    errorBannerText: {
        color: '#DC2626',
        fontSize: 14,
        textAlign: 'center',
    },
    list: {
        padding: 20,
    },
    notificationItem: {
        flexDirection: 'row',
        backgroundColor: '#fff',
        borderRadius: 12,
        padding: 16,
        marginBottom: 12,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.05,
        shadowRadius: 2,
        elevation: 2,
    },
    unreadItem: {
        backgroundColor: '#F8FAFC',
        borderLeftWidth: 3,
        borderLeftColor: '#B89C4C',
    },
    unreadDot: {
        width: 8,
        height: 8,
        borderRadius: 4,
        backgroundColor: '#B89C4C',
        marginRight: 12,
        marginTop: 6,
    },
    notificationContent: {
        flex: 1,
    },
    title: {
        fontSize: 16,
        fontWeight: '500',
        color: '#1F2937',
        marginBottom: 4,
    },
    unreadTitle: {
        fontWeight: '600',
    },
    message: {
        fontSize: 14,
        color: '#4B5563',
        lineHeight: 20,
        marginBottom: 8,
    },
    timestamp: {
        fontSize: 12,
        color: '#6B7280',
    },
    emptyState: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        padding: 20,
    },
    emptyTitle: {
        fontSize: 20,
        fontWeight: '600',
        color: '#1F2937',
        marginTop: 16,
        marginBottom: 8,
    },
    emptyMessage: {
        fontSize: 16,
        color: '#6B7280',
        textAlign: 'center',
        maxWidth: 300,
    },
    errorText: {
        fontSize: 16,
        color: '#EF4444',
        marginBottom: 16,
        textAlign: 'center',
    },
    retryButton: {
        backgroundColor: '#B89C4C',
        paddingHorizontal: 16,
        paddingVertical: 8,
        borderRadius: 8,
    },
    retryButtonText: {
        color: '#fff',
        fontSize: 14,
        fontWeight: '500',
    },
    loadingMore: {
        padding: 20,
        alignItems: 'center',
    },
    skeletonContainer: {
        flexDirection: 'row',
        backgroundColor: '#fff',
        borderRadius: 12,
        padding: 16,
        marginBottom: 12,
    },
    skeletonDot: {
        width: 8,
        height: 8,
        borderRadius: 4,
        backgroundColor: '#E5E7EB',
        marginRight: 12,
        marginTop: 6,
    },
    skeletonContent: {
        flex: 1,
        gap: 8,
    },
    skeletonTitle: {
        height: 20,
        backgroundColor: '#E5E7EB',
        borderRadius: 4,
        width: '80%',
    },
    skeletonText: {
        height: 16,
        backgroundColor: '#E5E7EB',
        borderRadius: 4,
        width: '100%',
    },
    skeletonDate: {
        height: 12,
        backgroundColor: '#E5E7EB',
        borderRadius: 4,
        width: '30%',
        marginTop: 4,
    },
});
