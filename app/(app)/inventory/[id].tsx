import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Platform } from 'react-native';
import { useLocalSearchParams, Stack, router } from 'expo-router';
import { useQuery } from '@tanstack/react-query';
import { X, MapPin, Building2, Tag, Key } from 'lucide-react-native';
import ImageGallery from '@/components/ImageGallery';
import { fetchListing } from '@/lib/api';

export default function ListingDetails() {
  const { id } = useLocalSearchParams();
  const listingId = typeof id === 'string' ? parseInt(id, 10) : 0;

  const { data: listing, isLoading } = useQuery({
    queryKey: ['listing', listingId],
    queryFn: () => fetchListing(listingId),
  });

  const formatPrice = (price: string) => {
    const numericPrice = parseInt(price.replace(/[^0-9]/g, ''), 10);
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'QAR',
      maximumFractionDigits: 0,
    }).format(numericPrice);
  };

  if (isLoading || !listing) {
    return (
      <View style={styles.loadingContainer}>
        <Text>Loading...</Text>
      </View>
    );
  }

  return (
    <>
      <Stack.Screen
        options={{
          presentation: 'modal',
          headerShown: false,
        }}
      />
      
      <View style={styles.container}>
        <TouchableOpacity
          style={styles.closeButton}
          onPress={() => router.back()}
        >
          <X size={24} color="#fff" />
        </TouchableOpacity>

        <ScrollView style={styles.scrollView}>
          <ImageGallery images={listing.images} />

          <View style={styles.content}>
            <View style={styles.header}>
              <Text style={styles.title}>{listing.title}</Text>
              <Text style={styles.price}>{formatPrice(listing.price)}</Text>
            </View>

            <View style={styles.infoSection}>
              <View style={styles.infoRow}>
                <MapPin size={20} color="#6B7280" />
                <Text style={styles.infoText}>
                  {listing.location}
                  {listing.tower?.name ? ` • ${listing.tower.name}` : ''}
                </Text>
              </View>

              <View style={styles.infoRow}>
                <Building2 size={20} color="#6B7280" />
                <Text style={styles.infoText}>{listing.property_type}</Text>
              </View>

              <View style={styles.infoRow}>
                <Tag size={20} color="#6B7280" />
                <Text style={styles.infoText}>
                  For {listing.ad_type.charAt(0).toUpperCase() + listing.ad_type.slice(1)}
                </Text>
              </View>

              <View style={styles.infoRow}>
                <Key size={20} color="#6B7280" />
                <Text style={styles.infoText}>
                  Keys at: {listing.keys_place}
                </Text>
              </View>
            </View>

            {listing.amenities && listing.amenities.length > 0 && (
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Amenities</Text>
                <View style={styles.amenities}>
                  {listing.amenities.map((amenity, index) => (
                    <View key={index} style={styles.amenityTag}>
                      <Text style={styles.amenityText}>{amenity}</Text>
                    </View>
                  ))}
                </View>
              </View>
            )}

            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Property Details</Text>
              <View style={styles.detailsGrid}>
                {listing.bedrooms_no && (
                  <View style={styles.detailItem}>
                    <Text style={styles.detailLabel}>Bedrooms</Text>
                    <Text style={styles.detailValue}>{listing.bedrooms_no}</Text>
                  </View>
                )}
                {listing.bathrooms_no && (
                  <View style={styles.detailItem}>
                    <Text style={styles.detailLabel}>Bathrooms</Text>
                    <Text style={styles.detailValue}>{listing.bathrooms_no}</Text>
                  </View>
                )}
                {listing.completion_year && (
                  <View style={styles.detailItem}>
                    <Text style={styles.detailLabel}>Year Built</Text>
                    <Text style={styles.detailValue}>{listing.completion_year}</Text>
                  </View>
                )}
                {listing.service_charge > 0 && (
                  <View style={styles.detailItem}>
                    <Text style={styles.detailLabel}>Service Charge</Text>
                    <Text style={styles.detailValue}>{listing.service_charge}</Text>
                  </View>
                )}
              </View>
            </View>
          </View>
        </ScrollView>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButton: {
    position: 'absolute',
    top: Platform.OS === 'ios' ? 60 : 20,
    right: 20,
    zIndex: 10,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 20,
  },
  header: {
    marginBottom: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 8,
  },
  price: {
    fontSize: 20,
    fontWeight: '600',
    color: '#B89C4C',
  },
  infoSection: {
    backgroundColor: '#F9FAFB',
    padding: 16,
    borderRadius: 12,
    marginBottom: 24,
    gap: 12,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  infoText: {
    fontSize: 16,
    color: '#4B5563',
    flex: 1,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 16,
  },
  amenities: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  amenityTag: {
    backgroundColor: '#F3F4F6',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  amenityText: {
    fontSize: 14,
    color: '#4B5563',
  },
  detailsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
  },
  detailItem: {
    flex: 1,
    minWidth: 140,
    backgroundColor: '#F9FAFB',
    padding: 16,
    borderRadius: 12,
  },
  detailLabel: {
    fontSize: 12,
    color: '#6B7280',
    marginBottom: 4,
  },
  detailValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
  },
});