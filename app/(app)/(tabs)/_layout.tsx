import { Tabs } from 'expo-router';
import { ClipboardList, ListTodo, Building2, ActivityIcon } from 'lucide-react-native';
import TopHeader from '@/components/TopHeader';

export default function TabLayout() {
  return (
    <>
      <TopHeader />
      <Tabs
        screenOptions={{
          headerShown: false,
        }}
      >
        <Tabs.Screen
          name="dashboard"
          options={{
            title: 'Dashboard',
            tabBarIcon: ({ color, size }) => (
              <ActivityIcon size={size} color={color} />
            ),
          }}
        />
        <Tabs.Screen
          name="leads"
          options={{
            title: 'Leads',
            tabBarIcon: ({ color, size }) => (
              <ClipboardList size={size} color={color} />
            ),
          }}
        />
        <Tabs.Screen
          name="inventory"
          options={{
            title: 'Inventory',
            tabBarIcon: ({ color, size }) => (
              <Building2 size={size} color={color} />
            ),
          }}
        />
        <Tabs.Screen
          name="tasks"
          options={{
            title: 'Tasks',
            tabBarIcon: ({ color, size }) => (
              <ListTodo size={size} color={color} />
            ),
          }}
        />
      </Tabs>
    </>
  );
}