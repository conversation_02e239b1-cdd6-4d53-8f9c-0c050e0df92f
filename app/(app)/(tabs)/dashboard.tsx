
import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { useQuery } from '@tanstack/react-query';
import { <PERSON>, Handshake, ClipboardList, Building2, ArrowR<PERSON>, User, Calendar } from 'lucide-react-native';
import { api, fetchTodaysTasks } from '@/lib/api';
import { formatDistanceToNow } from '@/lib/date';
import { Link } from 'expo-router';
import { Notification, Task } from '@/types/global';
import { router } from 'expo-router';
import NotificationItem from '@/components/NotificationItem';

interface PerformanceMetrics {
  activeLeads: number;
  pendingDeals: number;
  closedDeals: number;
  commission: number;
}

interface SalesData {
  labels: string[];
  datasets: {
    data: number[];
  }[];
}

interface NotificationsResponse {
  data: Notification[];
}

interface PerformanceMetrics {
  listings: number;
  contacts: number;
  leads: number;
  deals: number;
}

const METRICS_LIMITS = {
  listings: 20,
  contacts: 30,
  leads: 30,
  deals: 7,
} as const;

const calculatePercentage = (value: number, total: number): number => {
  return Math.round((value / total) * 100);
};

const fetchNotifications = async (): Promise<NotificationsResponse> => {
  const { data } = await api.get('/profile/notifications?limit=3');
  return data;
};

const fetchSalesData = async (): Promise<SalesData> => {
  // const { data } = await api.get('/dashboard/sales');
  // return data;
  return {
    labels: ["Jan", "Feb", "Mar"],  // e.g. ['Jan', 'Feb', 'Mar', ...]
    datasets: [{
      data: [20, 45, 28]
    }]
  }
};

const fetchPerformanceMetrics = async (): Promise<PerformanceMetrics> => {
  const { data } = await api.get('/profile/dashboard/performance');
  return data;
};

const TaskItem = ({ task }: { task: Task }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return '#10B981';
      case 'overdue':
        return '#EF4444';
      case 'in_progress':
        return '#F59E0B';
      default:
        return '#6B7280';
    }
  };

  const getName = () => {
    if (task.object_type === 'lead' && task.lead?.name) {
      return task.lead.name;
    }
    return task.ct_contact_name || 'No name';
  };

  // const formatDueDate = (dateString: string) => {
  //   console.log('to format ', dateString)
  //   // If the dateString is already formatted (contains 'ago' or similar), return it as is
  //   if (typeof dateString === 'string' &&
  //     (dateString.includes('ago') ||
  //       dateString.includes('month') ||
  //       dateString.includes('day') ||
  //       dateString.includes('year'))) {
  //     return dateString;
  //   }

  //   try {
  //     return formatDistanceToNow(dateString, { addSuffix: true });
  //   } catch (error) {
  //     console.error('Error parsing date:', dateString, error);
  //     return dateString;
  //   }
  // };

  return (
    <TouchableOpacity
      style={styles.taskItem}
      onPress={() => {
        if (task.object_type === 'lead' && task.lead_id) {
          router.push(`/leads/${task.lead_id}`);
        }
      }}
    >
      <View style={styles.taskContent}>
        <View style={styles.taskHeader}>
          <Text style={styles.taskTitle} numberOfLines={1}>
            {task.subject}
          </Text>
          <Text style={[
            styles.taskStatus,
            { color: getStatusColor(task.status) }
          ]}>
            {task.status.replace('_', ' ').toUpperCase()}
          </Text>
        </View>

        <View style={styles.taskDetails}>
          <View style={styles.taskDetailRow}>
            <User size={14} color="#6B7280" />
            <Text style={styles.taskDetailText}>{getName()}</Text>
          </View>
          <View style={styles.taskDetailRow}>
            <Calendar size={14} color="#6B7280" />
            <Text style={styles.taskDetailText}>
              Due {task.due_date}
            </Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default function DashboardScreen() {
  const { data: metrics, isLoading } = useQuery({
    queryKey: ['dashboardPerformance'],
    queryFn: fetchPerformanceMetrics,
  });

  const {
    data: salesData,
    isLoading: isLoadingSales // Fix: destructure isLoading as isLoadingSales
  } = useQuery({
    queryKey: ['dashboardSales'],
    queryFn: fetchSalesData,
  });

  const {
    data: notificationsData,
    isLoading: isLoadingNotifications
  } = useQuery({
    queryKey: ['dashboardNotifications'],
    queryFn: fetchNotifications,
  });

  const {
    data: todaysTasks,
    isLoading: isLoadingTasks
  } = useQuery<Task[]>({
    queryKey: ['dashboardTodaysTasks'],
    queryFn: fetchTodaysTasks,
  });

  return (
    <ScrollView style={styles.container}>
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Performance Overview</Text>
        <View style={styles.metricsGrid}>
          <TouchableOpacity
            style={styles.metricCard}
            onPress={() => router.push('/(app)/(tabs)/inventory')}
          >
            <View style={styles.metricHeader}>
              <View style={[styles.iconContainer, { backgroundColor: '#EFF6FF' }]}>
                <Building2 size={20} color="#3B82F6" />
              </View>
              <Text style={styles.metricLabel}>Listings</Text>
            </View>
            <View style={styles.metricContent}>
              <Text style={styles.metricValue}>
                {isLoading ? '-' : `${metrics?.listings || 0}/${METRICS_LIMITS.listings}`}
              </Text>
              <Text style={styles.metricPercentage}>
                {isLoading ? '-' : `${calculatePercentage(metrics?.listings || 0, METRICS_LIMITS.listings)}%`}
              </Text>
            </View>
          </TouchableOpacity>

          <View style={styles.metricCard}>
            <View style={styles.metricHeader}>
              <View style={[styles.iconContainer, { backgroundColor: '#F0FDF4' }]}>
                <Users size={20} color="#10B981" />
              </View>
              <Text style={styles.metricLabel}>Contacts</Text>
            </View>
            <View style={styles.metricContent}>
              <Text style={styles.metricValue}>
                {isLoading ? '-' : `${metrics?.contacts || 0}/${METRICS_LIMITS.contacts}`}
              </Text>
              <Text style={styles.metricPercentage}>
                {isLoading ? '-' : `${calculatePercentage(metrics?.contacts || 0, METRICS_LIMITS.contacts)}%`}
              </Text>
            </View>
          </View>

          <TouchableOpacity
            style={styles.metricCard}
            onPress={() => router.push('/(app)/(tabs)/leads')}
          >
            <View style={styles.metricHeader}>
              <View style={[styles.iconContainer, { backgroundColor: '#FFFBEB' }]}>
                <ClipboardList size={20} color="#F59E0B" />
              </View>
              <Text style={styles.metricLabel}>Leads</Text>
            </View>
            <View style={styles.metricContent}>
              <Text style={styles.metricValue}>
                {isLoading ? '-' : `${metrics?.leads || 0}/${METRICS_LIMITS.leads}`}
              </Text>
              <Text style={styles.metricPercentage}>
                {isLoading ? '-' : `${calculatePercentage(metrics?.leads || 0, METRICS_LIMITS.leads)}%`}
              </Text>
            </View>
          </TouchableOpacity>

          <View style={styles.metricCard}>
            <View style={styles.metricHeader}>
              <View style={[styles.iconContainer, { backgroundColor: '#FEF2F2' }]}>
                <Handshake size={20} color="#EF4444" />
              </View>
              <Text style={styles.metricLabel}>Deals</Text>
            </View>
            <View style={styles.metricContent}>
              <Text style={styles.metricValue}>
                {isLoading ? '-' : `${metrics?.deals || 0}/${METRICS_LIMITS.deals}`}
              </Text>
              <Text style={styles.metricPercentage}>
                {isLoading ? '-' : `${calculatePercentage(metrics?.deals || 0, METRICS_LIMITS.deals)}%`}
              </Text>
            </View>
          </View>
        </View>
      </View>

      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Today's Tasks</Text>
          <TouchableOpacity
            style={styles.viewAllButton}
            onPress={() => router.push('/(app)/(tabs)/tasks')}
          >
            <Text style={styles.viewAllText}>View All</Text>
            <ArrowRight size={16} color="#6B7280" />
          </TouchableOpacity>
        </View>

        {isLoadingTasks ? (
          <View style={styles.loadingContainer}>
            <Text style={styles.loadingText}>Loading tasks...</Text>
          </View>
        ) : todaysTasks && (todaysTasks ?? []).length > 0 ? (
          <View style={styles.tasksContainer}>
            {(todaysTasks ?? []).map((task) => (
              <TaskItem key={task.id} task={task} />
            ))}
          </View>
        ) : (
          <View style={styles.emptyState}>
            <Text style={styles.emptyText}>No tasks due today</Text>
          </View>
        )}
      </View>

      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Recent Notifications</Text>
          <Link href="/notifications" asChild>
            <TouchableOpacity style={styles.viewAllButton}>
              <Text style={styles.viewAllText}>View All</Text>
              <ArrowRight size={16} color="#6B7280" />
            </TouchableOpacity>
          </Link>
        </View>

        {isLoadingNotifications ? (
          <View style={styles.loadingContainer}>
            <Text style={styles.loadingText}>Loading notifications...</Text>
          </View>
        ) : notificationsData?.data.length === 0 ? (
          <View style={styles.emptyState}>
            <Text style={styles.emptyText}>No new notifications</Text>
          </View>
        ) : (
          notificationsData?.data.slice(0, 5).map((notification) => (
            <NotificationItem
              key={notification.id}
              notification={notification}
              onPress={() => router.push('/notifications')}
              compact
            />
          ))
        )}
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  section: {
    padding: 16,
    backgroundColor: 'transparent',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 16,
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  metricCard: {
    flex: 1,
    minWidth: '48%',
    backgroundColor: '#FFFFFF',
    padding: 12,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  metricHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  iconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  metricLabel: {
    fontSize: 13,
    fontWeight: '500',
    color: '#6B7280',
    flex: 1,
  },
  metricContent: {
    flexDirection: 'row',
    alignItems: 'baseline',
    justifyContent: 'space-between',
  },
  metricValue: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1F2937',
  },
  metricPercentage: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6B7280',
  },
  chartContainer: {
    marginTop: 8,
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 8,
  },
  loadingContainer: {
    height: 220,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  loadingText: {
    color: '#6B7280',
    fontSize: 14,
  },
  chartLegend: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 16,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  legendDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  legendText: {
    fontSize: 12,
    color: '#6B7280',
  },
  emptyState: {
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyText: {
    fontSize: 14,
    color: '#6B7280',
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  viewAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  viewAllText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280',
  },
  tasksContainer: {
    gap: 12,
  },
  taskItem: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  taskContent: {
    gap: 8,
  },
  taskHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  taskTitle: {
    fontSize: 15,
    fontWeight: '500',
    color: '#1F2937',
    flex: 1,
    marginRight: 8,
  },
  taskStatus: {
    fontSize: 12,
    fontWeight: '500',
  },
  taskDetails: {
    gap: 6,
  },
  taskDetailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  taskDetailText: {
    fontSize: 13,
    color: '#6B7280',
  },
  notificationItem: {
    flexDirection: 'row',
    padding: 12,
    borderRadius: 8,
    backgroundColor: '#F9FAFB',
    marginBottom: 8,
  },
  notificationIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#E5E7EB',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  notificationContent: {
    flex: 1,
  },
  notificationTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 4,
  },
  notificationBody: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 4,
  },
  notificationTime: {
    fontSize: 12,
    color: '#9CA3AF',
  },
});
