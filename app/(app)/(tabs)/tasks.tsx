import React, { useState } from 'react';
import { View, Text, StyleSheet, FlatList, RefreshControl, TouchableOpacity } from 'react-native';
import { useQuery } from '@tanstack/react-query';
import { ListTodo } from 'lucide-react-native';
import { fetchTasks } from '@/lib/api';
import { TaskViewType } from '@/types/task';
import TaskCard from '@/components/TaskCard';
import TaskFilters from '@/components/TaskFilters';
import Pagination from '@/components/Pagination';
import { usePersistedFilters } from '@/hooks/usePersistedFilters';
import FloatingFilterButton from '@/components/FloatingFilterButton';
import FilterDialog from '@/components/FilterDialog';

const ITEMS_PER_PAGE = 10;

interface TaskFilters {
  viewType: TaskViewType;
  page: number;
}

const defaultFilters: TaskFilters = {
  viewType: 'all',
  page: 1,
};

export default function TasksScreen() {
  const [isFilterVisible, setIsFilterVisible] = useState(false);
  const { filters, setFilters } = usePersistedFilters<TaskFilters>('task-filters', defaultFilters);

  const {
    data,
    isLoading,
    isError,
    refetch,
    isFetching
  } = useQuery({
    queryKey: ['tasks', filters.viewType, filters.page],
    queryFn: () => fetchTasks(filters.viewType, filters.page, ITEMS_PER_PAGE),
  });

  const tasks = data?.data ?? [];
  const totalPages = Math.ceil((data?.meta?.total ?? 0) / ITEMS_PER_PAGE);

  const handleFilterChange = async (updates: Partial<TaskFilters>) => {
    const newFilters = { ...filters, ...updates };
    if (updates.viewType) {
      newFilters.page = 1; // Reset page when view type changes
    }
    await setFilters(newFilters);
  };

  if (isError) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <Text style={styles.errorText}>Failed to load tasks</Text>
        <TouchableOpacity style={styles.retryButton} onPress={() => refetch()}>
          <Text style={styles.retryButtonText}>Try Again</Text>
        </TouchableOpacity>
      </View>
    );
  }

  if (isLoading) {
    return (
      <View style={styles.container}>
        <FlatList
          data={Array(5).fill(null)}
          keyExtractor={(_, index) => `skeleton-${index}`}
          renderItem={() => <TaskCard isLoading={true} />}
          contentContainerStyle={styles.listContent}
        />
      </View>
    );
  }

  if (tasks.length === 0) {
    return (
      <View style={[styles.container, styles.emptyContainer]}>
        <View style={styles.emptyContent}>
          <View style={styles.emptyIconContainer}>
            <ListTodo size={48} color="#9CA3AF" />
          </View>
          <Text style={styles.emptyTitle}>No tasks found</Text>
          <Text style={styles.emptyDescription}>
            {filters.viewType !== 'all'
              ? "Try adjusting your filters to see more tasks"
              : "You don't have any tasks assigned to you yet"}
          </Text>
          {filters.viewType !== 'all' && (
            <TouchableOpacity
              style={styles.clearFiltersButton}
              onPress={() => handleFilterChange({
                viewType: 'all',
                page: 1
              })}
            >
              <Text style={styles.clearFiltersButtonText}>Clear Filters</Text>
            </TouchableOpacity>
          )}
        </View>
        <FloatingFilterButton onPress={() => setIsFilterVisible(true)} />
        <FilterDialog
          visible={isFilterVisible}
          onClose={() => setIsFilterVisible(false)}
          title="Filter Tasks"
        >
          <TaskFilters
            selectedViewType={filters.viewType}
            onViewTypeChange={(viewType) => handleFilterChange({ viewType })}
          />
        </FilterDialog>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <FlatList
        data={tasks}
        keyExtractor={(item, index) => 
          item?.object_type 
            ? `taskCard_${index}_${item.object_type}_${item.object_type === 'lead' ? item.lead_id : item.ct_contact_id}`
            : `task-${item.id}`
        }
        renderItem={({ item }) => (
          <TaskCard task={item} />
        )}
        contentContainerStyle={styles.listContent}
        refreshControl={
          <RefreshControl
            refreshing={isFetching && !isLoading}
            onRefresh={refetch}
            tintColor="#B89C4C"
          />
        }
      />

      {totalPages > 1 && (
        <View style={styles.paginationContainer}>
          <Pagination
            currentPage={filters.page}
            totalPages={totalPages}
            onPageChange={(page) => handleFilterChange({ page })}
          />
        </View>
      )}

      <FloatingFilterButton onPress={() => setIsFilterVisible(true)} />

      <FilterDialog
        visible={isFilterVisible}
        onClose={() => setIsFilterVisible(false)}
        title="Filter Tasks"
      >
        <TaskFilters
          selectedViewType={filters.viewType}
          onViewTypeChange={(viewType) => handleFilterChange({ viewType })}
        />
      </FilterDialog>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContent: {
    alignItems: 'center',
    padding: 20,
    maxWidth: 400,
  },
  emptyIconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  emptyTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 12,
    textAlign: 'center',
  },
  emptyDescription: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 24,
  },
  clearFiltersButton: {
    backgroundColor: '#B89C4C',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  clearFiltersButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  listContent: {
    padding: 20,
  },
  errorText: {
    color: '#EF4444',
    fontSize: 16,
    marginBottom: 16,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: '#B89C4C',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
  },
  paginationContainer: {
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
});