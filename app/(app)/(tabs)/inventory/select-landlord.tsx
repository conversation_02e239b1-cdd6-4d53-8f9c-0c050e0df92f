import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, FlatList, ActivityIndicator } from 'react-native';
import { Stack, router } from 'expo-router';
import { ArrowLeft, Search, Building2, Phone, Mail, UserPlus } from 'lucide-react-native';
import { useQuery } from '@tanstack/react-query';
import { api } from '@/lib/api';
import Button from '@/components/Button';
import SearchBar from '@/components/SearchBar';

interface Landlord {
  id: number;
  name: string;
  email_1: string;
  company_name: string | null;
  mobile_1: string | null;
  prefix_mobile_1: string | null;
}

export default function SelectLandlord() {
  const [searchQuery, setSearchQuery] = useState('');

  const handlePhoneChange = (text: string) => {
    // Remove any non-digit characters
    const cleaned = text.replace(/[^0-9]/g, '');

    // Limit to 8 digits (Qatar phone number format)
    const limited = cleaned.slice(0, 8);

    // Format the number as #### ####
    const formatted = limited.replace(/(\d{4})(?=\d)/g, '$1 ');
    setSearchQuery(formatted);
  };

  const { data, isLoading, error } = useQuery({
    queryKey: ['landlords', searchQuery],
    queryFn: async () => {
      // Remove spaces before sending to API
      const cleanedPhone = searchQuery.replace(/\s/g, '');
      const { data } = await api.get('/crm/landlord/search', {
        params: {
          phone: cleanedPhone,
          limit: 50
        },
      });
      console.log('resulted data', data);
      return data;
    },
    // Only enable query when we have at least 3 digits
    enabled: searchQuery.replace(/\D/g, '').length >= 3,
  });

  const landlords: Array<Landlord> = data ?? [];

  const handleSelect = (landlord: Landlord) => {
    router.back();
    router.setParams({
      landlord: JSON.stringify(landlord)
    });
  };

  const handleCreateContact = () => {
    router.push({
      pathname: '/leads/create',
      params: {
        phone: searchQuery.replace(/\s/g, '')
      }
    });
  };

  console.log('searchQuery', searchQuery);

  return (
    <>
      <Stack.Screen
        options={{
          headerShown: true,
          header: () => (
            <View style={styles.header}>
              <Button
                variant="ghost"
                icon={<ArrowLeft size={24} color="#111827" />}
                onPress={() => router.back()}
              />
              <Text style={styles.headerTitle}>Search Landlord</Text>
            </View>
          ),
        }}
      />

      <View style={styles.container}>
        <View style={styles.searchContainer}>
          <SearchBar
            value={searchQuery}
            onChangeText={handlePhoneChange}
            placeholder="Enter phone number (e.g., 5555 5555)"
            icon={<Search size={20} color="#6B7280" />}
            isLoading={isLoading}
            keyboardType="phone-pad"
            maxLength={9} // 8 digits + 1 space
          />
        </View>

        {error ? (
          <View style={styles.messageContainer}>
            <Text style={styles.errorText}>Error loading landlords</Text>
          </View>
        ) : searchQuery.replace(/\D/g, '').length < 3 ? (
          <View style={styles.messageContainer}>
            <Text style={styles.messageText}>
              Enter at least 3 digits to search
            </Text>
          </View>
        ) : isLoading ? (
          <View style={styles.messageContainer}>
            <ActivityIndicator size="large" color="#B89C4C" />
          </View>
        ) : landlords.length === 0 ? (
          <View style={styles.messageContainer}>
            <Text style={styles.messageText}>No landlords found</Text>
            <Button
              label="Create New Contact"
              icon={<UserPlus size={20} color="#fff" />}
              onPress={handleCreateContact}
              style={styles.createButton}
            />
          </View>
        ) : (
          <FlatList
            data={landlords}
            keyExtractor={(item) => item.id.toString()}
            renderItem={({ item }) => (
              <TouchableOpacity
                style={styles.landlordCard}
                onPress={() => handleSelect(item)}
              >
                <View style={styles.landlordInfo}>
                  <Text style={styles.landlordName}>{item.name}</Text>

                  {item.company_name && (
                    <View style={styles.detailRow}>
                      <Building2 size={16} color="#6B7280" />
                      <Text style={styles.landlordDetail}>
                        {item.company_name}
                      </Text>
                    </View>
                  )}

                  {item.mobile_1 && (
                    <View style={styles.detailRow}>
                      <Phone size={16} color="#6B7280" />
                      <Text style={styles.landlordDetail}>
                        {item.prefix_mobile_1} {item.mobile_1}
                      </Text>
                    </View>
                  )}

                  {item.email_1 && (
                    <View style={styles.detailRow}>
                      <Mail size={16} color="#6B7280" />
                      <Text style={styles.landlordDetail}>
                        {item.email_1}
                      </Text>
                    </View>
                  )}
                </View>
              </TouchableOpacity>
            )}
            contentContainerStyle={styles.listContent}
          />
        )}
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginLeft: 12,
  },
  searchContainer: {
    padding: 16,
  },
  messageContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
    gap: 16,
  },
  messageText: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
  },
  errorText: {
    fontSize: 16,
    color: '#DC2626',
    textAlign: 'center',
  },
  listContent: {
    padding: 16,
  },
  landlordCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  landlordInfo: {
    flex: 1,
  },
  landlordName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 12,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 8,
  },
  landlordDetail: {
    fontSize: 14,
    color: '#6B7280',
  },
  createButton: {
    marginTop: 16,
  },
});