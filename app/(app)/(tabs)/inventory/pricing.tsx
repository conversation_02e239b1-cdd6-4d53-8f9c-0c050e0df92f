import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { Stack, router } from 'expo-router';
import { <PERSON>Lef<PERSON>, ArrowRight, Check, Save } from 'lucide-react-native';
import Button from '@/components/Button';

export default function Pricing() {
  return (
    <>
      <Stack.Screen
        options={{
          headerShown: true,
          header: () => (
            <View style={styles.header}>
              <Button
                variant="ghost"
                icon={<ArrowLeft size={24} color="#111827" />}
                onPress={() => router.back()}
              />
              <Text style={styles.headerTitle}>Pricing</Text>
            </View>
          ),
        }}
      />

      <View style={styles.container}>
        <ScrollView style={styles.content}>
          {/* Add your content here */}
        </ScrollView>

        <View style={styles.footer}>
          <View style={styles.footerButtons}>
            <Button
              variant="secondary"
              icon={<ArrowLeft size={20} color="#4B5563" />}
              onPress={() => router.back()}
            />
            <Button
              variant="secondary"
              icon={<Check size={20} color="#4B5563" />}
              label="Complete"
              onPress={() => {}}
            />
            <Button
              variant="secondary"
              icon={<Save size={20} color="#4B5563" />}
              label="Save"
              onPress={() => {}}
            />
            <Button
              variant="primary"
              icon={<ArrowRight size={20} color="#fff" />}
              onPress={() => router.push('/inventory/marketing')}
            />
          </View>
        </View>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  content: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
    gap: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#111827',
  },
  footer: {
    padding: 20,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    backgroundColor: '#fff',
  },
  footerButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
});