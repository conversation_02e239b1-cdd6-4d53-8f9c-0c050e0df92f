import React, { useState, useCallback } from 'react';
import { View, Text, StyleSheet, FlatList, RefreshControl, TouchableOpacity, Platform } from 'react-native';
import { useQuery } from '@tanstack/react-query';
import { Building2, Plus } from 'lucide-react-native';
import { router } from 'expo-router';
import { fetchListings } from '@/lib/api';
import ListingCard from '@/components/ListingCard';
import InventoryFilters from '@/components/InventoryFilters';
import Pagination from '@/components/Pagination';
import { usePersistedFilters } from '@/hooks/usePersistedFilters';
import FloatingFilterButton from '@/components/FloatingFilterButton';
import FilterDialog from '@/components/FilterDialog';
import { Listing } from '@/types/inventory';
import EmptyState from '@/components/EmptyState';
import ErrorState from '@/components/ErrorState';
import { useListingTypes } from '@/hooks/useConfiguration';

const ITEMS_PER_PAGE = 10;

interface InventoryFilters {
  status: string;
  propertyType: number;
  adType: string;
  vt: string;
  page: number;
}

const defaultFilters: InventoryFilters = {
  status: 'All',
  propertyType: null,
  adType: 'All',
  page: 1,
  vt: 'master'
};

const CreateButton = () => (
  <TouchableOpacity
    style={styles.addButton}
    onPress={() => router.push('/inventory/create')}
  >
    <Plus size={24} color="#fff" />
  </TouchableOpacity>
);

export default function InventoryScreen() {
  const [isFilterVisible, setIsFilterVisible] = useState(false);
  const { filters, setFilters } = usePersistedFilters<InventoryFilters>('inventory-filters', defaultFilters);
  const { data: propertyTypes = [], isLoading: isLoadingTypes } = useListingTypes();

  const {
    data,
    isLoading,
    isError,
    error,
    refetch,
    isFetching
  } = useQuery({
    queryKey: ['listings', filters],
    queryFn: () => fetchListings(
      filters.page,
      {
        propertyType: filters.propertyType !== null ? filters.propertyType : undefined,
        adType: filters.adType !== 'All' ? filters.adType.toLowerCase() : undefined,
        vt: filters.vt
      }
    ),
  });

  const listings = data?.data ?? [];
  const totalPages = Math.ceil((data?.total ?? 0) / ITEMS_PER_PAGE);
  const totalListings = data?.total ?? 0;

  const handleFilterChange = useCallback(async (updates: Partial<InventoryFilters>) => {
    const newFilters = { ...filters, ...updates };
    if (updates.status || updates.hasOwnProperty('propertyType') || updates.adType || updates.vt) {
      newFilters.page = 1;
    }
    await setFilters(newFilters);
  }, [filters, setFilters]);

  const handleAddNew = () => {
    router.push('/inventory/inventory-form');
  };

  const keyExtractor = useCallback((item: Listing) => item.ref_no, []);

  const getItemLayout = useCallback((_: any, index: number) => ({
    length: 140, // Height of ListingCard
    offset: 140 * index,
    index,
  }), []);

  const renderItem = useCallback(({ item }: { item: Listing }) => (
    <ListingCard listing={item} />
  ), []);

  const ListHeader = useCallback(() => (
    <View style={styles.header}>
      <View style={styles.headerContent}>
        <Building2 size={24} color="#1F2937" />
        <Text style={styles.headerTitle}>Inventory</Text>
        {totalListings > 0 && (
          <View style={styles.badge}>
            <Text style={styles.badgeText}>{totalListings}</Text>
          </View>
        )}
      </View>

      <View style={styles.filterSummary}>
        {filters.status !== 'All' && (
          <View style={styles.filterTag}>
            <Text style={styles.filterTagText}>{filters.status}</Text>
          </View>
        )}
        {filters.propertyType !== null && (
          <View style={styles.filterTag}>
            <Text style={styles.filterTagText}>{filters.propertyType}</Text>
          </View>
        )}
        {filters.adType !== 'All' && (
          <View style={styles.filterTag}>
            <Text style={styles.filterTagText}>For {filters.adType}</Text>
          </View>
        )}
      </View>
    </View>
  ), [totalListings, filters]);

  if (isError) {
    return (
      <ErrorState
        message={`Error loading inventory: ${(error as Error).message}`}
        onRetry={refetch}
      />
    );
  }

  if (isLoading) {
    return (
      <View style={styles.container}>
        {/* <ListHeader /> */}
        <FlatList
          data={Array(5).fill(null)}
          renderItem={() => <ListingCard isLoading={true} />}
          contentContainerStyle={styles.listContent}
          keyExtractor={(_, index) => `skeleton-${index}`}
          getItemLayout={getItemLayout}
        />
      </View>
    );
  }

  if (listings.length === 0) {
    return (
      <View style={styles.container}>
        {/* <ListHeader /> */}
        <EmptyState
          icon={<Building2 size={48} color="#9CA3AF" />}
          title="No properties found"
          description={
            filters.status !== 'All' || filters.propertyType !== null || filters.adType !== 'All'
              ? "Try adjusting your filters to see more properties"
              : "There are no properties available at the moment"
          }
          action={
            (filters.status !== 'All' || filters.propertyType !== null || filters.adType !== 'All') && {
              label: "Clear Filters",
              onPress: () => handleFilterChange(defaultFilters)
            }
          }
        />
        <TouchableOpacity
          style={styles.addButton}
          onPress={handleAddNew}
        >
          <Plus size={24} color="#fff" />
        </TouchableOpacity>
        <FloatingFilterButton onPress={() => setIsFilterVisible(true)} />
        <FilterDialog
          visible={isFilterVisible}
          onClose={() => setIsFilterVisible(false)}
          title="Filter Properties"
        >
          <InventoryFilters
            selectedStatus={filters.status}
            selectedPropertyType={filters.propertyType}
            selectedAdType={filters.adType}
            selectedViewType={filters.vt}
            propertyTypes={propertyTypes}
            onPropertyTypeChange={(propertyType) => handleFilterChange({ propertyType })}
            onAdTypeChange={(adType) => handleFilterChange({ adType })}
            onViewTypeChange={(vt) => handleFilterChange({ vt })}
          />
        </FilterDialog>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* <ListHeader /> */}
      <View style={styles.content}>
        <FlatList
          data={listings}
          renderItem={renderItem}
          keyExtractor={keyExtractor}
          contentContainerStyle={styles.listContent}
          getItemLayout={getItemLayout}
          windowSize={5}
          maxToRenderPerBatch={5}
          initialNumToRender={5}
          removeClippedSubviews={true}
          refreshControl={
            <RefreshControl
              refreshing={isFetching && !isLoading}
              onRefresh={refetch}
              tintColor="#B89C4C"
            />
          }
        />

        {totalPages > 1 && (
          <View style={styles.paginationContainer}>
            <Pagination
              currentPage={filters.page}
              totalPages={totalPages}
              onPageChange={(page) => handleFilterChange({ page })}
            />
          </View>
        )}
      </View>
      <CreateButton />

      {/* <TouchableOpacity
        style={styles.addButton}
        onPress={handleAddNew}
      >
        <Plus size={24} color="#fff" />
      </TouchableOpacity> */}

      <FloatingFilterButton onPress={() => setIsFilterVisible(true)} />

      <FilterDialog
        visible={isFilterVisible}
        onClose={() => setIsFilterVisible(false)}
        title="Filter Properties"
      >
        <InventoryFilters
          selectedStatus={filters.status}
          selectedPropertyType={filters.propertyType}
          selectedAdType={filters.adType}
          selectedViewType={filters.vt}
          propertyTypes={propertyTypes}
          onPropertyTypeChange={(propertyType) => handleFilterChange({ propertyType })}
          onAdTypeChange={(adType) => handleFilterChange({ adType })}
          onViewTypeChange={(vt) => handleFilterChange({ vt })}
        />
      </FilterDialog>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  content: {
    flex: 1,
  },
  header: {
    padding: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    marginBottom: 16,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: '#1F2937',
  },
  badge: {
    backgroundColor: '#B89C4C',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 2,
    marginLeft: 8,
  },
  badgeText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  filterSummary: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  filterTag: {
    backgroundColor: '#F3F4F6',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  filterTagText: {
    color: '#4B5563',
    fontSize: 12,
    fontWeight: '500',
  },
  listContent: {
    padding: 20,
  },
  paginationContainer: {
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  addButton: {
    position: 'absolute',
    right: 20,
    bottom: Platform.select({
      ios: 100,
      android: 86,
      web: 86,
    }),
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#B89C4C',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  createButton: {
    position: 'absolute',
    right: 16,
    bottom: 16,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#10B981', // or your theme color
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
});