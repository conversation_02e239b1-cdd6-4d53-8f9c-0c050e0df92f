import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, TextInput, ScrollView, Modal, FlatList, TouchableOpacity, ActivityIndicator, Alert } from 'react-native';
import { Stack, router, useLocalSearchParams } from 'expo-router';
import { User, Building2, Phone, Mail, ChevronDown, Search, MapPin, Home, DollarSign, CheckCircle2, Edit2, ArrowLeft, Plus, AlertCircle, Eye, EyeOff } from 'lucide-react-native';
import Button from '@/components/Button';
import PhoneInput from '@/components/PhoneInput';
import { useNationalities } from '@/hooks/useConfiguration';
import SearchBar from '@/components/SearchBar';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { api } from '@/lib/api';
import { Country } from '@/types/global';
import PropertyTypeSelector from '@/components/PropertyTypeSelector';
import { OperationType } from '@/types/inventory';

const MODAL_CONFIG = {
  width: '90%',
  height: '70%',
  maxWidth: 500,
  borderRadius: 12,
};

interface Contact {
  id: number;
  name: string;
  email_1: string;
  company_name: string | null;
  mobile_1: string | null;
  prefix_mobile_1: string | null;
}

interface RepresentativeForm {
  name: string;
  email: string;
  phone: string;
  qatar_id_no: string;
  nationality_id: string | null;
}

interface ListingCreateResponse {
  id: number;
  refNo: string;
  publishingStatus: string;
  agent: {
    id: number;
    name: string;
  };
}

export default function CreateInventory() {
  const queryClient = useQueryClient();
  const { data: nationalities = [] } = useNationalities();
  const [isNationalityModalVisible, setIsNationalityModalVisible] = useState(false);
  const [nationalitySearch, setNationalitySearch] = useState('');
  const [isCountryModalVisible, setIsCountryModalVisible] = useState(false);
  const [countrySearch, setCountrySearch] = useState('');
  const [selectedLocation, setSelectedLocation] = useState(null);
  const [isLocationModalVisible, setIsLocationModalVisible] = useState(false);
  const [locationSearch, setLocationSearch] = useState('');
  const [operationType, setOperationType] = useState<OperationType>('rent');
  const [selectedPropertyType, setSelectedPropertyType] = useState<number | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [showSuccessOptions, setShowSuccessOptions] = useState(false);
  const [savedListing, setSavedListing] = useState<ListingCreateResponse | null>(null);
  const [validationError, setValidationError] = useState<string | null>(null);

  const { data: countries = [] } = useQuery({
    queryKey: ['countries'],
    queryFn: async () => {
      const { data } = await api.get('/countries');
      return data;
    },
  });

  // Add filtered nationalities
  const filteredNationalities = React.useMemo(() => {
    if (!nationalitySearch.trim()) return nationalities;
    const searchTerm = nationalitySearch.toLowerCase();
    return nationalities.filter((nationality) =>
      nationality.value.toLowerCase().includes(searchTerm)
    );
  }, [nationalities, nationalitySearch]);

  // Add filtered countries
  const filteredCountries = React.useMemo(() => {
    if (!countrySearch.trim()) return countries ?? [];
    const searchTerm = countrySearch.toLowerCase();
    return countries.filter((country) =>
      country.name.toLowerCase().includes(searchTerm)
    );
  }, [countries, countrySearch]);

  const params = useLocalSearchParams();
  const [selectedLandlord, setSelectedLandlord] = useState<Contact | null>(null);
  const [representativeForm, setRepresentativeForm] = useState<RepresentativeForm>({
    name: '',
    email: '',
    phone: '',
    qatar_id_no: '',
    nationality_id: null,
  });

  const selectedNationality = React.useMemo(() => {
    return nationalities.find(n => n.key === representativeForm.nationality_id);
  }, [nationalities, representativeForm.nationality_id]);

  // Find Qatar in countries and set as default
  const defaultCountry = React.useMemo(() => {
    return (countries ?? []).find((country: Country) => country.name === 'Qatar');
  }, [countries]);


  const [selectedCountry, setSelectedCountry] = useState(defaultCountry || null);
  const { data: locations = [], isLoading: isLoadingLocations } = useQuery({
    queryKey: ['locations', selectedCountry?.id],
    queryFn: async () => {
      console.log('url to call', `/country/${selectedCountry?.id}/locations`);
      const { data } = await api.get(`/country/${selectedCountry?.id}/locations`);
      return data;
    },
    enabled: !!selectedCountry?.id,
  });

  // Add filtered locations
  const filteredLocations = React.useMemo(() => {
    if (!locationSearch.trim()) return locations;
    const searchTerm = locationSearch.toLowerCase();
    return locations.filter((location) =>
      location.name.toLowerCase().includes(searchTerm)
    );
  }, [locations, locationSearch]);

  // Add these new state variables and query
  const [selectedTower, setSelectedTower] = useState({ id: 'any', name: 'Any' });
  const [isTowerModalVisible, setIsTowerModalVisible] = useState(false);
  const [towerSearch, setTowerSearch] = useState('');

  const { data: towers = [], isLoading: isLoadingTowers } = useQuery({
    queryKey: ['towers', selectedLocation?.id],
    queryFn: async () => {
      const { data } = await api.get(`/geography/${selectedLocation?.id}/towers`);
      return [{ id: 'any', name: 'Any' }, ...data];
    },
    enabled: !!selectedLocation?.id,
  });

  // Add this memoized filtered towers
  const filteredTowers = React.useMemo(() => {
    if (!towerSearch.trim()) return towers;
    const searchTerm = towerSearch.toLowerCase();
    return towers.filter(tower =>
      tower.name.toLowerCase().includes(searchTerm)
    );
  }, [towers, towerSearch]);

  // Reset tower selection when location changes
  useEffect(() => {
    setSelectedTower({ id: 'any', name: 'Any' });
  }, [selectedLocation]);

  useEffect(() => {
    if (params.landlord) {
      try {
        const landlord = JSON.parse(params.landlord as string);
        setSelectedLandlord(landlord);
      } catch (error) {
        console.error('Error parsing landlord:', error);
      }
    }
  }, [params.landlord]);

  const handleSelectLandlord = () => {
    router.push('/inventory/select-landlord');
  };

  const handleRepresentativeChange = (field: keyof RepresentativeForm, value: string) => {
    setRepresentativeForm(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const renderContactCard = (contact: Contact | null, onSelect: () => void, title: string) => {
    if (!contact) {
      return (
        <Button
          label={`Select ${title}`}
          icon={<User size={20} color="#fff" />}
          onPress={onSelect}
          fullWidth
        />
      );
    }

    return (
      <View style={styles.selectedContact}>
        <View style={styles.contactInfo}>
          <Text style={styles.contactName}>{contact.name}</Text>
          {contact.company_name && (
            <View style={styles.detailRow}>
              <Building2 size={16} color="#6B7280" />
              <Text style={styles.contactDetail}>
                {contact.company_name}
              </Text>
            </View>
          )}
          {contact.mobile_1 && (
            <View style={styles.detailRow}>
              <Phone size={16} color="#6B7280" />
              <Text style={styles.contactDetail}>
                {contact.prefix_mobile_1} {contact.mobile_1}
              </Text>
            </View>
          )}
          {contact.email_1 && (
            <View style={styles.detailRow}>
              <Mail size={16} color="#6B7280" />
              <Text style={styles.contactDetail}>
                {contact.email_1}
              </Text>
            </View>
          )}
        </View>
        <Button
          variant="secondary"
          label="Change"
          onPress={onSelect}
        />
      </View>
    );
  };

  // Add this validation function
  const isFormValid = () => {
    return !!(
      selectedLandlord &&
      selectedPropertyType &&
      operationType &&
      selectedLocation
    );
  };

  // Add the save handler
  const handleSave = async () => {
    if (!isFormValid()) return;
    
    setIsSaving(true);
    try {
      const payload: any = {
        representative: {
          fullname: representativeForm.name,
          email: representativeForm.email,
          mobile_no: representativeForm.phone,
          prefix_mobile_no: '+974', // Assuming Qatar prefix is default
          nationality_id: representativeForm.nationality_id,
          qatar_id_no: representativeForm.qatar_id_no
        },
        contact_id: selectedLandlord.id,
        ad_type: operationType,
        country_id: selectedCountry?.id,
        location_id: selectedLocation?.id,
        tower_id: selectedTower?.id ?? null,
        property_type_id: selectedPropertyType,
        developer: '',
        unit_no: '',
        contact_to_view: '',
        prorated_rata: '',
        payment_plan_info: '',
        payment_closing_requiring_documents: '',
        rented_for: '',
        embeed_youtube: '',
        attributes: {
          parking_info: '',
        },
        status: 'available',
        images: []
      };

      const { data } = await api.post<ListingCreateResponse>('/crm/listing', payload);
      setSavedListing(data);
      setShowSuccessOptions(true);
      // Invalidate and refetch listings query
      queryClient.invalidateQueries(['listings']);
    } catch (error: any) {
      if (error.response?.status === 422) {
        setValidationError(error.response.data.message);
      } else {
        Alert.alert(
          'Error',
          'Failed to create listing. Please try again.',
          [{ text: 'OK' }]
        );
      }
      console.error('Error saving inventory:', error);
    } finally {
      setIsSaving(false);
    }
  };

  // Add the success options handler functions
  const handleEditListing = () => {
    setShowSuccessOptions(false);
    router.push({
      pathname: '/inventory/inventory-form',
      params: { id: savedListing?.id }
    });
  };

  const handleBackToInventory = () => {
    setShowSuccessOptions(false);
    queryClient.invalidateQueries(['listings']); // Also refresh when going back
    router.push('/inventory');
  };

  const handleCreateNew = () => {
    setShowSuccessOptions(false);
    // Reset form
    setSelectedLandlord(null);
    setRepresentativeForm({
      name: '',
      email: '',
      phone: '',
      qatar_id_no: '',
      nationality_id: null,
    });
    setSelectedPropertyType(null);
    setOperationType('rent');
    setSelectedLocation(null);
    setSelectedTower({ id: 'any', name: 'Any' });
  };

  return (
    <>
      <Stack.Screen
        options={{
          headerShown: true,
          title: 'Add Listing'
        }}
      />
      <View style={styles.wrapper}>
        <ScrollView
          style={styles.container}
          contentContainerStyle={styles.contentContainer}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Landlord Information</Text>
            <Text style={styles.sectionDescription}>
              Select a landlord for this property
            </Text>
            {renderContactCard(selectedLandlord, handleSelectLandlord, 'Landlord')}
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Representative Information</Text>
            <Text style={styles.sectionDescription}>
              Enter representative details for this property
            </Text>
            <View style={styles.formContainer}>
              <View style={styles.inputGroup}>
                <Text style={styles.label}>Name</Text>
                <TextInput
                  style={styles.input}
                  value={representativeForm.name}
                  onChangeText={(text) => handleRepresentativeChange('name', text)}
                  placeholder="Enter representative name"
                />
              </View>
              <View style={styles.inputGroup}>
                <Text style={styles.label}>Email</Text>
                <TextInput
                  style={styles.input}
                  value={representativeForm.email}
                  onChangeText={(text) => handleRepresentativeChange('email', text)}
                  placeholder="Enter email address"
                  keyboardType="email-address"
                  autoCapitalize="none"
                />
              </View>
              <View style={styles.inputGroup}>
                <PhoneInput
                  label="Phone"
                  value={representativeForm.phone}
                  onChangeText={(text) => handleRepresentativeChange('phone', text)}
                  placeholder="Enter phone number"
                />
              </View>
              <View style={styles.inputGroup}>
                <Text style={styles.label}>Qatar ID</Text>
                <TextInput
                  style={styles.input}
                  value={representativeForm.qatar_id_no}
                  onChangeText={(text) => handleRepresentativeChange('qatar_id_no', text)}
                  placeholder="Enter Qatar ID number"
                />
              </View>
              <View style={styles.inputGroup}>
                <Text style={styles.label}>Nationality</Text>
                <TouchableOpacity
                  style={styles.nationalitySelector}
                  onPress={() => setIsNationalityModalVisible(true)}
                >
                  <Text style={[
                    styles.nationalitySelectorText,
                    !selectedNationality && { color: '#9CA3AF' }
                  ]}>
                    {selectedNationality?.value || 'Select nationality'}
                  </Text>
                  <ChevronDown size={16} color="#6B7280" />
                </TouchableOpacity>
              </View>
            </View>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Location & Tower</Text>
            <Text style={styles.sectionDescription}>
              Select the property location details
            </Text>
            <View style={styles.formContainer}>
              <View style={styles.inputGroup}>
                <Text style={styles.label}>Country</Text>
                <TouchableOpacity
                  style={styles.selector}
                  onPress={() => setIsCountryModalVisible(true)}
                >
                  <View style={styles.selectorContent}>
                    <MapPin size={20} color="#6B7280" />
                    <Text style={styles.selectorText}>
                      {(selectedCountry ?? defaultCountry)?.name || 'Select country'}
                    </Text>
                  </View>
                  <ChevronDown size={16} color="#6B7280" />
                </TouchableOpacity>
              </View>
              <View style={styles.inputGroup}>
                <Text style={styles.label}>Location</Text>
                <TouchableOpacity
                  style={[
                    styles.selector,
                    !selectedCountry && styles.selectorDisabled
                  ]}
                  onPress={() => {
                    if (selectedCountry) setIsLocationModalVisible(true);
                  }}
                  disabled={!selectedCountry}
                >
                  <View style={styles.selectorContent}>
                    <MapPin size={20} color="#6B7280" />
                    <Text style={[
                      styles.selectorText,
                      !selectedCountry && styles.selectorTextDisabled
                    ]}>
                      {selectedLocation?.name || 'Select location'}
                    </Text>
                  </View>
                  <ChevronDown size={16} color="#6B7280" />
                </TouchableOpacity>
                {!selectedCountry && (
                  <Text style={styles.helperText}>Please select a country first</Text>
                )}
              </View>
              <View style={styles.inputGroup}>
                <Text style={styles.label}>Tower</Text>
                <TouchableOpacity
                  style={[
                    styles.selector,
                    !selectedLocation && styles.selectorDisabled
                  ]}
                  onPress={() => {
                    if (selectedLocation) setIsTowerModalVisible(true);
                  }}
                  disabled={!selectedLocation}
                >
                  <View style={styles.selectorContent}>
                    <Building2 size={20} color="#6B7280" />
                    <Text style={[
                      styles.selectorText,
                      !selectedLocation && styles.selectorTextDisabled
                    ]}>
                      {selectedTower?.name || 'Select tower'}
                    </Text>
                  </View>
                  <ChevronDown size={16} color="#6B7280" />
                </TouchableOpacity>
                {!selectedLocation && (
                  <Text style={styles.helperText}>Please select a location first</Text>
                )}
              </View>
            </View>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Operation Type</Text>
            <Text style={styles.sectionDescription}>
              Select whether this property is for rent or sale
            </Text>
            <View style={styles.operationTypeContainer}>
              <TouchableOpacity
                style={[
                  styles.operationTypeButton,
                  operationType === 'rent' && styles.operationTypeButtonSelected
                ]}
                onPress={() => setOperationType('rent')}
              >
                <View style={[
                  styles.operationTypeIconContainer,
                  operationType === 'rent' && styles.operationTypeIconContainerSelected
                ]}>
                  <Home
                    size={24}
                    color={operationType === 'rent' ? '#B89C4C' : '#6B7280'}
                  />
                </View>
                <Text style={[
                  styles.operationTypeTitle,
                  operationType === 'rent' && styles.operationTypeTitleSelected
                ]}>
                  For Rent
                </Text>
                <Text style={styles.operationTypeDescription}>
                  Property available for rental
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.operationTypeButton,
                  operationType === 'sale' && styles.operationTypeButtonSelected
                ]}
                onPress={() => setOperationType('sale')}
              >
                <View style={[
                  styles.operationTypeIconContainer,
                  operationType === 'sale' && styles.operationTypeIconContainerSelected
                ]}>
                  <DollarSign
                    size={24}
                    color={operationType === 'sale' ? '#B89C4C' : '#6B7280'}
                  />
                </View>
                <Text style={[
                  styles.operationTypeTitle,
                  operationType === 'sale' && styles.operationTypeTitleSelected
                ]}>
                  For Sale
                </Text>
                <Text style={styles.operationTypeDescription}>
                  Property available for purchase
                </Text>
              </TouchableOpacity>
            </View>
          </View>


          <View style={styles.section}>
            <PropertyTypeSelector
              selectedType={selectedPropertyType}
              onSelectType={setSelectedPropertyType}
            />
          </View>

        </ScrollView>

        <View style={styles.footer}>
          <Button
            label="Save Listing"
            onPress={handleSave}
            disabled={!isFormValid() || isSaving}
            loading={isSaving}
            fullWidth
          />
        </View>
      </View>

      <Modal
        visible={isNationalityModalVisible}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setIsNationalityModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Nationality</Text>
              <TouchableOpacity
                onPress={() => {
                  setIsNationalityModalVisible(false);
                  setNationalitySearch(''); // Reset search when closing
                }}
              >
                <Text style={styles.closeButton}>Close</Text>
              </TouchableOpacity>
            </View>

            <View style={styles.searchContainer}>
              <SearchBar
                value={nationalitySearch}
                onChangeText={setNationalitySearch}
                placeholder="Search nationalities..."
                icon={<Search size={20} color="#6B7280" />}
              />
            </View>

            <FlatList
              data={filteredNationalities}
              keyExtractor={(item) => item.key?.toString()}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={[
                    styles.nationalityOption,
                    representativeForm.nationality_id === item.key && styles.selectedNationality
                  ]}
                  onPress={() => {
                    handleRepresentativeChange('nationality_id', item.key);
                    setIsNationalityModalVisible(false);
                    setNationalitySearch(''); // Reset search when selecting
                  }}
                >
                  <Text style={styles.nationalityName}>{item.value}</Text>
                </TouchableOpacity>
              )}
              style={styles.nationalityList}
              ListEmptyComponent={() => (
                <View style={styles.emptyContainer}>
                  <Text style={styles.emptyText}>No nationalities found</Text>
                </View>
              )}
            />
          </View>
        </View>
      </Modal>

      <Modal
        visible={isCountryModalVisible}
        transparent={true}
        animationType="slide"
        onRequestClose={() => {
          setIsCountryModalVisible(false);
          setCountrySearch('');
        }}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContainer, { width: MODAL_CONFIG.width, maxWidth: MODAL_CONFIG.maxWidth }]}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Country</Text>
              <TouchableOpacity
                onPress={() => {
                  setIsCountryModalVisible(false);
                  setCountrySearch('');
                }}
              >
                <Text style={styles.closeButton}>Close</Text>
              </TouchableOpacity>
            </View>

            <View style={styles.searchContainer}>
              <SearchBar
                value={countrySearch}
                onChangeText={setCountrySearch}
                placeholder="Search countries..."
                icon={<Search size={20} color="#6B7280" />}
              />
            </View>

            <FlatList
              data={filteredCountries}
              keyExtractor={(item) => item.id.toString()}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={[
                    styles.countryOption,
                    selectedCountry?.id === item.id && styles.selectedOption
                  ]}
                  onPress={() => {
                    setSelectedCountry(item);
                    setIsCountryModalVisible(false);
                    setCountrySearch('');
                  }}
                >
                  <Text style={styles.countryName}>{item.name}</Text>
                </TouchableOpacity>
              )}
              style={styles.countryList}
              ListEmptyComponent={() => (
                <View style={styles.emptyContainer}>
                  <Text style={styles.emptyText}>No countries found</Text>
                </View>
              )}
            />
          </View>
        </View>
      </Modal>

      <Modal
        visible={isLocationModalVisible}
        transparent={true}
        animationType="slide"
        onRequestClose={() => {
          setIsLocationModalVisible(false);
          setLocationSearch('');
        }}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContainer, { width: MODAL_CONFIG.width, maxWidth: MODAL_CONFIG.maxWidth }]}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Location</Text>
              <TouchableOpacity
                onPress={() => {
                  setIsLocationModalVisible(false);
                  setLocationSearch('');
                }}
              >
                <Text style={styles.closeButton}>Close</Text>
              </TouchableOpacity>
            </View>

            <View style={styles.searchContainer}>
              <SearchBar
                value={locationSearch}
                onChangeText={setLocationSearch}
                placeholder="Search locations..."
                icon={<Search size={20} color="#6B7280" />}
              />
            </View>

            {isLoadingLocations ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color="#B89C4C" />
                <Text style={styles.loadingText}>Loading locations...</Text>
              </View>
            ) : (
              <FlatList
                data={filteredLocations}
                keyExtractor={(item) => item.id.toString()}
                renderItem={({ item }) => (
                  <TouchableOpacity
                    style={[
                      styles.locationOption,
                      selectedLocation?.id === item.id && styles.selectedOption
                    ]}
                    onPress={() => {
                      setSelectedLocation(item);
                      setIsLocationModalVisible(false);
                      setLocationSearch('');
                    }}
                  >
                    <Text style={styles.locationName}>{item.name}</Text>
                  </TouchableOpacity>
                )}
                style={styles.locationList}
                ListEmptyComponent={() => (
                  <View style={styles.emptyContainer}>
                    <Text style={styles.emptyText}>
                      {locationSearch.trim() ? 'No matching locations found' : 'No locations available'}
                    </Text>
                  </View>
                )}
              />
            )}
          </View>
        </View>
      </Modal>

      <Modal
        visible={isTowerModalVisible}
        transparent={true}
        animationType="slide"
        onRequestClose={() => {
          setIsTowerModalVisible(false);
          setTowerSearch('');
        }}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContainer, { width: MODAL_CONFIG.width, maxWidth: MODAL_CONFIG.maxWidth }]}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Tower</Text>
              <TouchableOpacity
                onPress={() => {
                  setIsTowerModalVisible(false);
                  setTowerSearch('');
                }}
              >
                <Text style={styles.closeButton}>Close</Text>
              </TouchableOpacity>
            </View>

            <View style={styles.searchContainer}>
              <SearchBar
                value={towerSearch}
                onChangeText={setTowerSearch}
                placeholder="Search towers..."
                icon={<Search size={20} color="#6B7280" />}
              />
            </View>

            {isLoadingTowers ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color="#B89C4C" />
                <Text style={styles.loadingText}>Loading towers...</Text>
              </View>
            ) : (
              <FlatList
                data={filteredTowers}
                keyExtractor={(item) => item.id.toString()}
                renderItem={({ item }) => (
                  <TouchableOpacity
                    style={[
                      styles.towerOption,
                      selectedTower?.id === item.id && styles.selectedOption
                    ]}
                    onPress={() => {
                      setSelectedTower(item);
                      setIsTowerModalVisible(false);
                      setTowerSearch('');
                    }}
                  >
                    <Text style={[
                      styles.towerName,
                      item.id === 'any' && styles.defaultOptionText
                    ]}>
                      {item.name}
                    </Text>
                  </TouchableOpacity>
                )}
                style={styles.towerList}
                ListEmptyComponent={() => (
                  <View style={styles.emptyContainer}>
                    <Text style={styles.emptyText}>
                      {towerSearch.trim() ? 'No matching towers found' : 'No towers available'}
                    </Text>
                  </View>
                )}
              />
            )}
          </View>
        </View>
      </Modal>

      <Modal
        visible={showSuccessOptions}
        transparent
        animationType="fade"
        onRequestClose={() => setShowSuccessOptions(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <CheckCircle2 size={48} color="#10B981" />
              <Text style={styles.modalTitle}>Listing Created</Text>
              <View style={styles.listingInfo}>
                <View style={styles.refNoContainer}>
                  <Text style={styles.refNoLabel}>Ref:</Text>
                  <Text style={styles.refNoValue}>{savedListing?.refNo}</Text>
                </View>
                <View style={[
                  styles.statusBadge,
                  { backgroundColor: savedListing?.publishingStatus === 'published' ? '#10B981' : '#6B7280' }
                ]}>
                  {savedListing?.publishingStatus === 'published' ? (
                    <Eye size={16} color="#fff" />
                  ) : (
                    <EyeOff size={16} color="#fff" />
                  )}
                  <Text style={styles.statusText}>
                    {savedListing?.publishingStatus || 'draft'}
                  </Text>
                </View>
              </View>
            </View>
            
            <View style={styles.modalButtons}>
              <Button
                label="Edit Listing"
                onPress={handleEditListing}
                icon={<Edit2 size={20} color="#fff" />}
                fullWidth
              />
              <Button
                label="Back to Inventory"
                onPress={handleBackToInventory}
                variant="secondary"
                icon={<ArrowLeft size={20} color="#111827" />}
                fullWidth
              />
              <Button
                label="Create New Listing"
                onPress={handleCreateNew}
                variant="secondary"
                icon={<Plus size={20} color="#111827" />}
                fullWidth
              />
            </View>
          </View>
        </View>
      </Modal>

      <Modal
        visible={!!validationError}
        transparent
        animationType="fade"
        onRequestClose={() => setValidationError(null)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <AlertCircle size={48} color="#DC2626" />
              <Text style={styles.modalTitle}>Validation Error</Text>
            </View>
            
            <Text style={styles.errorMessage}>{validationError}</Text>
            
            <View style={styles.modalButtons}>
              <Button
                label="OK"
                onPress={() => setValidationError(null)}
                fullWidth
              />
            </View>
          </View>
        </View>
      </Modal>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  contentContainer: {
    paddingBottom: 32,
  },
  section: {
    margin: 20,
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 16,
  },
  selectedContact: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  contactInfo: {
    flex: 1,
    marginRight: 16,
  },
  contactName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 12,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 8,
  },
  contactDetail: {
    fontSize: 14,
    color: '#6B7280',
  },
  formContainer: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 8,
  },
  input: {
    backgroundColor: '#F9FAFB',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: '#111827',
  },
  nationalitySelector: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#F9FAFB',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    padding: 12,
  },
  nationalitySelectorText: {
    fontSize: 16,
    color: '#111827',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  modalContainer: {
    backgroundColor: '#fff',
    borderRadius: MODAL_CONFIG.borderRadius,
    width: MODAL_CONFIG.width,
    height: MODAL_CONFIG.height,
    maxWidth: MODAL_CONFIG.maxWidth,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
  },
  closeButton: {
    fontSize: 16,
    color: '#6B7280',
  },
  searchContainer: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  nationalityList: {
    flex: 1, // Change to flex: 1 to fill remaining space
  },
  nationalityOption: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  selectedNationality: {
    backgroundColor: '#F3F4F6',
  },
  nationalityName: {
    fontSize: 16,
    color: '#111827',
  },
  emptyContainer: {
    padding: 20,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    color: '#6B7280',
  },
  selector: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#F9FAFB',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    padding: 12,
  },
  selectorContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  selectorText: {
    fontSize: 16,
    color: '#111827',
  },
  countryOption: {
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  selectedOption: {
    backgroundColor: '#F3F4F6',
  },
  countryName: {
    fontSize: 16,
    color: '#111827',
  },
  countryList: {
    flex: 1,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
  },
  selectorDisabled: {
    backgroundColor: '#E5E7EB',
    borderColor: '#D1D5DB',
  },
  selectorTextDisabled: {
    color: '#9CA3AF',
  },
  helperText: {
    fontSize: 12,
    color: '#6B7280',
    marginTop: 4,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 8,
    fontSize: 16,
    color: '#6B7280',
  },
  locationOption: {
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  locationName: {
    fontSize: 16,
    color: '#111827',
  },
  locationList: {
    flex: 1,
  },
  towerOption: {
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  towerName: {
    fontSize: 16,
    color: '#111827',
  },
  towerList: {
    flex: 1,
  },
  defaultOptionText: {
    color: '#6B7280',
    fontStyle: 'italic',
  },
  operationTypeContainer: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 8,
  },
  operationTypeButton: {
    flex: 1,
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    backgroundColor: '#FFFFFF',
  },
  operationTypeButtonSelected: {
    borderColor: '#B89C4C',
    backgroundColor: '#FDFBF7',
  },
  operationTypeIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F9FAFB',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  operationTypeIconContainerSelected: {
    backgroundColor: '#F5EFE0',
  },
  operationTypeTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 4,
  },
  operationTypeTitleSelected: {
    color: '#B89C4C',
  },
  operationTypeDescription: {
    fontSize: 14,
    color: '#6B7280',
  },
  propertyTypeContainer: {
    marginTop: 16,
  },
  wrapper: {
    flex: 1,
  },
  footer: {
    padding: 16,
    paddingBottom: 24,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    backgroundColor: '#fff',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 24,
    width: '100%',
    maxWidth: 400,
  },
  modalHeader: {
    alignItems: 'center',
    marginBottom: 24,
  },
  modalTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: '#111827',
    marginTop: 16,
    marginBottom: 8,
  },
  modalSubtitle: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
  },
  modalButtons: {
    gap: 12,
  },
  errorMessage: {
    fontSize: 16,
    color: '#DC2626',
    marginBottom: 16,
  },
  listingInfo: {
    marginTop: 16,
    gap: 8,
    alignItems: 'center',
  },
  refNoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  refNoLabel: {
    fontSize: 14,
    color: '#6B7280',
  },
  refNoValue: {
    fontSize: 14,
    color: '#111827',
    fontWeight: '600',
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    gap: 6,
  },
  statusText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
    textTransform: 'capitalize',
  },
});
