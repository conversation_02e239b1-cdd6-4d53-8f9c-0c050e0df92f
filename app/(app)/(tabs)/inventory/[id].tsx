import { useLocal<PERSON>earch<PERSON>ara<PERSON>, router, Stack } from 'expo-router';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  ActivityIndicator,
  Dimensions,
  Pressable,
  Platform,
  RefreshControl,
  TouchableOpacity,
} from 'react-native';
import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { getListingDetails } from '@/lib/api';
import {
  CircleAlert as AlertCircle,
  MapPin,
  Bed,
  Bath,
  Maximize2,
  Car,
  Key,
  Tag,
  Building2,
  Eye,
  CircleCheck as CheckCircle2,
  House as Home,
  Star,
  Pencil,
  ArrowLeft
} from 'lucide-react-native';

const { width } = Dimensions.get('window');
const imageHeight = width * 0.75;

const FALLBACK_IMAGE = 'https://images.unsplash.com/photo-1600585154340-be6161a56a0c?w=800&q=80';

const COLORS = {
  primary: '#F9FAFB',
  primaryLight: '#F3F4F6',
  accent: '#B89C4C',
  accentLight: '#D4B96D',
  success: '#10B981',
  warning: '#EF4444',
  background: '#FFFFFF',
  backgroundAlt: '#F8FAFC',
  text: '#111827',
  textMuted: '#6B7280',
  border: '#E5E7EB',
  divider: '#F1F5F9',
  card: '#FFFFFF',
  overlay: 'rgba(17, 24, 39, 0.5)',
  gold: {
    light: '#D4B96D',
    medium: '#B89C4C',
    dark: '#8B7355',
    muted: 'rgba(184, 156, 76, 0.1)',
  },
};


export default function InventoryDetailsScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const { data: listing, isLoading, isError, error, refetch, isRefetching } = useQuery({
    queryKey: ['listing', id],
    queryFn: () => getListingDetails(parseInt(id, 10)),
  });

  const getImageUrl = (url?: string) => {
    if (!url) return FALLBACK_IMAGE;
    return url;
  };

  const formatPrice = (price?: number) => {
    if (!price) return 'Price on request';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'QAR',
      maximumFractionDigits: 0,
    }).format(price);
  };

  const getAttributeValue = (key: string) => {
    return listing?.attributes.find((attr) => attr.key === key)?.value;
  };

  const handleImageTap = (index: number) => {
    setCurrentImageIndex(index);
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={COLORS.accent} />
      </View>
    );
  }

  if (isError || !listing) {
    return (
      <View style={styles.loadingContainer}>
        <AlertCircle size={48} color={COLORS.warning} />
        <Text style={styles.errorText}>
          {error instanceof Error ? error.message : 'Failed to load property details'}
        </Text>
        <Pressable style={styles.retryButton} onPress={() => refetch()}>
          <Text style={styles.retryButtonText}>Try Again</Text>
        </Pressable>
      </View>
    );
  }

  return (
    <>
      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />

      <View style={styles.container}>
      <ScrollView 
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={isRefetching}
            onRefresh={refetch}
            tintColor="#B89C4C"
            colors={['#B89C4C']} // Android
            progressBackgroundColor="#ffffff" // Android
          />
        }
      >
        <View style={styles.heroSection}>
          <Image
            source={{ uri: getImageUrl(listing.images?.[currentImageIndex]?.img_url) }}
            style={styles.heroImage}
            resizeMode="cover"
          />
          <TouchableOpacity
            style={styles.floatingBackButton}
            onPress={() => router.back()}
          >
            <ArrowLeft size={22} color="#FFFFFF" />
          </TouchableOpacity>
          <View style={styles.heroOverlay}>
            <View style={styles.heroContent}>
              <Text style={styles.heroPrice}>{formatPrice(listing.price)}</Text>
              {listing.best_price && listing.best_price < listing.price && (
                <Text style={styles.heroBestPrice}>Best Price: {formatPrice(listing.best_price)}</Text>
              )}
              <View style={styles.heroLocation}>
                <MapPin size={16} color={COLORS.gold.light} />
                <Text style={styles.heroLocationText}>
                  {listing.location?.path || 'Location not specified'}
                </Text>
              </View>
            </View>
          </View>
        </View>

        {listing.images && listing.images.length > 1 && (
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.gallery}
            contentContainerStyle={styles.galleryContent}>
            {listing.images.map((image, index) => (
              <Pressable 
                key={index} 
                style={[
                  styles.galleryItem,
                  currentImageIndex === index && styles.galleryItemActive
                ]}
                onPress={() => handleImageTap(index)}>
                <Image
                  source={{ uri: getImageUrl(image.img_url) }}
                  style={styles.galleryImage}
                  resizeMode="cover"
                />
              </Pressable>
            ))}
          </ScrollView>
        )}

        <View style={styles.content}>
          <View style={styles.contentHeader}>
            <View style={[
              styles.statusBadge,
              listing.status?.toLowerCase() === 'occupied' ? styles.statusOccupied : styles.statusAvailable
            ]}>
              <Text style={[
                styles.statusText,
                { color: listing.status?.toLowerCase() === 'occupied' ? COLORS.warning : COLORS.success }
              ]}>
                {listing.status?.toUpperCase() || 'STATUS N/A'}
              </Text>
            </View>
            <Text style={styles.refNo}>Ref: {listing.ref_no || 'N/A'}</Text>
          </View>

          <Text style={styles.propertyTitle}>{listing.title || 'Untitled Property'}</Text>

          <View style={styles.statsGrid}>
            <View style={styles.statCard}>
              <Bed size={24} color={COLORS.gold.dark} />
              <Text style={styles.statValue}>{getAttributeValue('bedrooms') || '0'}</Text>
              <Text style={styles.statLabel}>Bedrooms</Text>
            </View>
            <View style={styles.statCard}>
              <Bath size={24} color={COLORS.gold.dark} />
              <Text style={styles.statValue}>{getAttributeValue('bathrooms') || '0'}</Text>
              <Text style={styles.statLabel}>Bathrooms</Text>
            </View>
            <View style={styles.statCard}>
              <Maximize2 size={24} color={COLORS.gold.dark} />
              <Text style={styles.statValue}>{getAttributeValue('build_up_area') || '0'}</Text>
              <Text style={styles.statLabel}>Sq Ft</Text>
            </View>
            <View style={styles.statCard}>
              <Car size={24} color={COLORS.gold.dark} />
              <Text style={styles.statValue}>{getAttributeValue('parking_info') || '0'}</Text>
              <Text style={styles.statLabel}>Parking</Text>
            </View>
          </View>

          <View style={styles.propertySection}>
            <Text style={styles.propertySectionTitle}>Property Details</Text>
            <View style={styles.detailsGrid}>
              {[
                { icon: Key, label: 'Key Access', value: listing.key_access },
                { icon: Tag, label: 'Type', value: listing.property_type },
                { icon: Building2, label: 'Unit', value: listing.unit_no },
                { icon: Home, label: 'Kitchen', value: getAttributeValue('kitchen') },
                { icon: Star, label: 'Furnishing', value: getAttributeValue('furnishings') },
              ].map((detail, index) => (
                <View key={index} style={styles.detailCard}>
                  <detail.icon size={20} color={COLORS.gold.dark} />
                  <View style={styles.detailContent}>
                    <Text style={styles.detailLabel}>{detail.label}</Text>
                    <Text style={styles.detailValue}>{detail.value || 'Not specified'}</Text>
                  </View>
                </View>
              ))}
            </View>
          </View>

          {listing.views?.length > 0 && (
            <View style={styles.propertySection}>
              <Text style={styles.propertySectionTitle}>Views</Text>
              <View style={styles.tagsContainer}>
                {listing.views.map((view) => (
                  <View key={view.id} style={styles.tag}>
                    <Eye size={14} color={COLORS.gold.dark} />
                    <Text style={styles.tagText}>{view.name}</Text>
                  </View>
                ))}
              </View>
            </View>
          )}

          {listing.amenities?.length > 0 && (
            <View style={styles.propertySection}>
              <Text style={styles.propertySectionTitle}>Amenities</Text>
              <View style={styles.tagsContainer}>
                {listing.amenities.map((amenity) => (
                  <View key={amenity.id} style={styles.tag}>
                    <CheckCircle2 size={14} color={COLORS.gold.dark} />
                    <Text style={styles.tagText}>{amenity.name}</Text>
                  </View>
                ))}
              </View>
            </View>
          )}

          {listing.description?.length > 0 && (
            <View style={styles.propertySection}>
              <Text style={styles.propertySectionTitle}>Description</Text>
              {listing.description.map((block, index) => (
                <Text key={index} style={styles.descriptionText}>
                  {block.content}
                </Text>
              ))}
            </View>
          )}

          {listing.agent && (
            <View style={styles.agentCard}>
              <View style={styles.agentHeader}>
                <View style={styles.agentAvatar}>
                  <Text style={styles.agentInitials}>
                    {listing.agent.name?.split(' ').map(n => n[0]).join('') || 'NA'}
                  </Text>
                </View>
                <View style={styles.agentInfo}>
                  <Text style={styles.agentName}>{listing.agent.name || 'N/A'}</Text>
                  <Text style={styles.agentPosition}>{listing.agent.position || 'N/A'}</Text>
                  <Text style={styles.agentEmail}>{listing.agent.email || 'N/A'}</Text>
                </View>
              </View>
            </View>
          )}
        </View>
      </ScrollView>

      <Pressable
        style={styles.editButton}
        onPress={() => router.push(`/inventory/inventory-form/${id}`)}>
        <Pencil size={20} color={COLORS.background} />
      </Pressable>
    </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },

  scrollView: {
    flex: 1,
  },
  contentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  propertyTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 8,
  },
  price: {
    fontSize: 20,
    fontWeight: '600',
    color: COLORS.gold.medium,
  },
  propertySection: {
    margin: 20,
    marginBottom: 32,
  },
  propertySectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 16,
  },
  infoSection: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    gap: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  infoText: {
    fontSize: 16,
    color: '#4B5563',
    flex: 1,
  },
  amenities: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  amenityTag: {
    backgroundColor: '#F3F4F6',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  amenityText: {
    fontSize: 14,
    color: '#4B5563',
  },
  detailsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
  },
  detailItem: {
    flex: 1,
    minWidth: 140,
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  detailLabel: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 4,
  },
  detailValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
  },
  editButton: {
    position: 'absolute',
    right: 16,
    bottom: 16,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: COLORS.gold.medium,
    justifyContent: 'center',
    alignItems: 'center',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
      },
      android: {
        elevation: 5,
      },
      web: {
        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.15)',
      },
    }),
  },
  heroSection: {
    height: imageHeight,
    backgroundColor: COLORS.primaryLight,
  },
  heroImage: {
    width: '100%',
    height: '100%',
  },
  floatingBackButton: {
    position: 'absolute',
    top: Platform.OS === 'ios' ? 40 : 20,
    left: 20,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
  },
  heroOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: COLORS.overlay,
    justifyContent: 'flex-end',
  },
  heroContent: {
    padding: 20,
  },
  heroPrice: {
    fontSize: 32,
    fontWeight: 'bold',
    color: COLORS.background,
    marginBottom: 4,
  },
  heroBestPrice: {
    fontSize: 18,
    color: COLORS.gold.light,
    marginBottom: 12,
  },
  heroLocation: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  heroLocationText: {
    fontSize: 16,
    color: COLORS.background,
    marginLeft: 8,
  },
  gallery: {
    height: 80,
    backgroundColor: COLORS.background,
  },
  galleryContent: {
    padding: 10,
    gap: 10,
  },
  galleryItem: {
    width: 100,
    height: 60,
    borderRadius: 8,
    overflow: 'hidden',
    borderWidth: 2,
    borderColor: COLORS.border,
  },
  galleryItemActive: {
    borderColor: COLORS.gold.medium,
  },
  galleryImage: {
    width: '100%',
    height: '100%',
  },
  content: {
    padding: 20,
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
  },
  statusOccupied: {
    backgroundColor: 'rgba(239, 68, 68, 0.1)',
  },
  statusAvailable: {
    backgroundColor: 'rgba(16, 185, 129, 0.1)',
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
  },
  refNo: {
    fontSize: 14,
    color: COLORS.textMuted,
  },

  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 10,
    marginBottom: 24,
  },
  statCard: {
    flex: 1,
    minWidth: '45%',
    backgroundColor: COLORS.primaryLight,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: COLORS.text,
    marginTop: 8,
  },
  statLabel: {
    fontSize: 12,
    color: COLORS.textMuted,
    marginTop: 4,
  },

  detailCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.primaryLight,
    padding: 16,
    borderRadius: 12,
  },
  detailContent: {
    marginLeft: 12,
    flex: 1,
  },

  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  tag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.primaryLight,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
  },
  tagText: {
    fontSize: 13,
    color: COLORS.text,
    marginLeft: 6,
  },
  descriptionText: {
    fontSize: 15,
    color: COLORS.textMuted,
    lineHeight: 24,
    marginBottom: 12,
  },
  agentCard: {
    backgroundColor: COLORS.primaryLight,
    borderRadius: 16,
    padding: 20,
    marginBottom: 100,
  },
  agentHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  agentAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: COLORS.accent,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  agentInitials: {
    color: COLORS.background,
    fontSize: 18,
    fontWeight: 'bold',
  },
  agentInfo: {
    flex: 1,
  },
  agentName: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: 4,
  },
  agentPosition: {
    fontSize: 14,
    color: COLORS.textMuted,
    marginBottom: 2,
  },
  agentEmail: {
    fontSize: 14,
    color: COLORS.accent,
  },

  errorText: {
    marginTop: 16,
    fontSize: 16,
    color: COLORS.textMuted,
    textAlign: 'center',
  },
  retryButton: {
    marginTop: 16,
    backgroundColor: COLORS.accent,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: COLORS.background,
    fontSize: 16,
    fontWeight: '600',
  },
});
