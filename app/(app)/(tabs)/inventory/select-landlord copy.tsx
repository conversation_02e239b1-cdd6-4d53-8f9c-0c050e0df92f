import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, FlatList, ActivityIndicator } from 'react-native';
import { Stack, router } from 'expo-router';
import { ArrowLeft, Search, Building2, Phone, Mail } from 'lucide-react-native';
import { useQuery } from '@tanstack/react-query';
import { api } from '@/lib/api';
import Button from '@/components/Button';
import SearchBar from '@/components/SearchBar';

// interface Landlord {
//   id: number;
//   name: string;
//   email_1: string | null;
//   prefix_mobile_1: string | null;
//   mobile_1: string | null;
//   company_name: string | null;
// }

interface Landlord {
  company_name: string,
  developer_name: string,
  email_1: string;
  fullname: string;
  id: number;
  name: string;
  record_no: string;
}

export default function SelectLandlord() {
  const [searchQuery, setSearchQuery] = useState('');

  const { data, isLoading, error } = useQuery({
    queryKey: ['landlords', searchQuery],
    queryFn: async () => {
      const { data } = await api.get('/crm/landlord/search', {
        params: {
          phone: searchQuery,
          limit: 50
        },
      });
      return data;
    },
    enabled: searchQuery.length >= 2,
  });

  const landlords: Array<Landlord> = data ?? [];

  const handleSelect = (landlord: Landlord) => {
    router.push({
      pathname: '/inventory/inventory-form',
      params: {
        landlord: JSON.stringify(landlord)
      }
    });
  };

  return (
    <>
      <Stack.Screen
        options={{
          headerShown: true,
          header: () => (
            <View style={styles.header}>
              <Button
                variant="ghost"
                icon={<ArrowLeft size={24} color="#111827" />}
                onPress={() => router.back()}
              />
              <Text style={styles.headerTitle}>Select Landlord</Text>
            </View>
          ),
        }}
      />

      <View style={styles.container}>
        <View style={styles.searchContainer}>
          <SearchBar
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholder="Search landlords..."
            icon={<Search size={20} color="#6B7280" />}
            isLoading={isLoading}
          />
        </View>

        {error ? (
          <View style={styles.messageContainer}>
            <Text style={styles.errorText}>Error loading landlords</Text>
          </View>
        ) : searchQuery.length < 2 ? (
          <View style={styles.messageContainer}>
            <Text style={styles.messageText}>
              Type at least 2 characters to search landlords
            </Text>
          </View>
        ) : isLoading ? (
          <View style={styles.messageContainer}>
            <ActivityIndicator size="large" color="#B89C4C" />
          </View>
        ) : landlords.length === 0 ? (
          <View style={styles.messageContainer}>
            <Text style={styles.messageText}>No landlords found</Text>
          </View>
        ) : (
          <FlatList
            data={landlords}
            keyExtractor={(item) => item.id.toString()}
            renderItem={({ item }) => (
              <TouchableOpacity
                style={styles.landlordCard}
                onPress={() => handleSelect(item)}
              >
                <View style={styles.landlordInfo}>
                  <Text style={styles.landlordName}>{item.fullname}</Text>

                  {item.company_name && (
                    <View style={styles.detailRow}>
                      <Building2 size={16} color="#6B7280" />
                      <Text style={styles.landlordDetail}>
                        {item.company_name}
                      </Text>
                    </View>
                  )}

                  {/* {item.mobile_1 && (
                    <View style={styles.detailRow}>
                      <Phone size={16} color="#6B7280" />
                      <Text style={styles.landlordDetail}>
                        {item.prefix_mobile_1} {item.mobile_1}
                      </Text>
                    </View>
                  )} */}

                  {item.email_1 && (
                    <View style={styles.detailRow}>
                      <Mail size={16} color="#6B7280" />
                      <Text style={styles.landlordDetail}>
                        {item.email_1}
                      </Text>
                    </View>
                  )}
                </View>
              </TouchableOpacity>
            )}
            contentContainerStyle={styles.listContent}
          />
        )}
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
    gap: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#111827',
  },
  searchContainer: {
    padding: 20,
  },
  messageContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  messageText: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
  },
  errorText: {
    fontSize: 16,
    color: '#EF4444',
    textAlign: 'center',
  },
  listContent: {
    padding: 20,
  },
  landlordCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  landlordInfo: {
    flex: 1,
  },
  landlordName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 12,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 8,
  },
  landlordDetail: {
    fontSize: 14,
    color: '#6B7280',
  },
});