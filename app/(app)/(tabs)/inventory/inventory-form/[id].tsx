import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { Stack, router, useLocalSearchParams } from 'expo-router';
import { ArrowLeft, ArrowRight, Check, Save, CircleAlert as AlertCircle, Eye, EyeOff } from 'lucide-react-native';
import { useMutation, useQuery } from '@tanstack/react-query';
import { api, getListingDetails } from '@/lib/api';
import Button from '@/components/Button';
import BasicDetailsSection from '@/components/inventory/BasicDetailsSection';
import PropertyDetailsSection from '@/components/inventory/PropertyDetailsSection';
import PropertyFeaturesSection from '@/components/inventory/PropertyFeaturesSection';
import PricingSection from '@/components/inventory/PricingSection';
import MarketingSection from '@/components/inventory/MarketingSection';
import ReferralSelector from '@/components/ReferralSelector';
import DescriptionSection from '@/components/inventory/DescriptionSection';
import UserAvatar from '@/components/UserAvatar';
import { Bill, FormData, Furnishing, Image, ListingDetails, ListingStatus, OperationType, PaymentMethod, PropertyPayload, PropertyResponse, ValidationErrors } from '@/types/inventory';
import { useNationalities } from '@/hooks/useConfiguration';
import Toast from 'react-native-toast-message';
import ImageUploadModal from '@/components/inventory/ImageUploadModal';

type Step = 'basic-details' | 'property-details' | 'property-features' | 'pricing' | 'marketing' | 'description';


const steps: { id: Step; title: string }[] = [
  { id: 'basic-details', title: 'Basic Details' },
  { id: 'property-details', title: 'Property Details' },
  { id: 'property-features', title: 'Property Features' },
  { id: 'pricing', title: 'Pricing' },
  { id: 'marketing', title: 'Marketing' },
  { id: 'description', title: 'Description' },
];

export function transformListingToFormData(listing: ListingDetails): FormData {
  const getAttributeValue = (key: string): string => {
    const attribute = listing.attributes.find(attr => attr.key === key);
    return attribute ? String(attribute.value) : '';
  };

  const getDescriptionContent = (language: 'en' | 'ar', tagName?: string): string => {
    if (language === 'en' && !tagName) {
      return listing.description_raw || '';
    }

    const block = (listing.description ?? []).find(desc =>
      (tagName ? desc.tag_name === tagName : true) &&
      desc.content.includes(language === 'ar' ? 'العربية' : 'English')
    );

    return block ? block.content : '';
  };

  return {
    basicDetails: {
      landlord: {
        id: listing.contact_id,
        name: listing.contact_name
      },
      representative: {
        fullname: listing.representative?.fullname || '',
        email: listing.representative?.email || '',
        prefix_mobile_no: listing.representative?.prefix_mobile_no || '',
        mobile_no: listing.representative?.mobile_no || '',
        qatar_id_no: listing.representative?.qatar_id_no || '',
        nationality_id: listing.representative?.nationality_id || null
      },
      country: {
        id: listing.country_id,
        name: listing.country_name
      },
      location: listing.location,
      tower: listing.tower,
      operationType: listing.ad_type as OperationType,
      propertyType: listing.property_type_id
    },
    propertyDetails: {
      isExclusive: Boolean(listing.is_exclusive),
      isHomepagePromoted: Boolean(listing.is_homepage_promoted),
      isInvestmentOpportunity: Boolean(listing.is_investment_opportunity),
      developerName: listing.developer || '',
      unitNo: listing.unit_no || '',
      keyAccess: listing.key_access || '',
      status: listing.status || null,
      completionYear: getAttributeValue('construction_year'),
      titleDeed: listing.title_deed ? '1' : '0',
      referral: listing.reffered_by_id ? {
        id: listing.reffered_by_id,
        name: listing.reffered_by_name || ''
      } : null
    },
    propertyFeatures: {
      size: getAttributeValue('build_up_area'),
      parkingPlaces: getAttributeValue('parking_info'),
      bedrooms: getAttributeValue('bedrooms'),
      bathrooms: getAttributeValue('bathrooms'),
      kitchen: getAttributeValue('kitchen'),
      hasBalcony: getAttributeValue('balcony') === '1',
      furnishing: (getAttributeValue('furnishings') || 'unfurnished') as any,
      selectedAmenities: listing.amenities.map(amenity => amenity.id),
      location: {
        latitude: listing.geo_lat,
        longitude: listing.geo_lon
      }
    },
    pricing: {
      listedPrice: listing.price?.toString() ?? null,
      bestPrice: listing.best_price?.toString() ?? null,
      offers: listing.offers || '',
      paymentMethod: (listing.payment_method || 'cash') as PaymentMethod,
      proratedAmount: listing.prorated_rata || '',
      numberOfCheques: listing.payment_cheques_no?.toString() ?? null,
      commissionTenant: listing.commission_tenant || '',
      commissionLandlord: listing.commission_ll?.toString() ?? null,
      minimumContract: parseInt(listing.minimum_contract || '1'),
      commissionBuyer: listing.commission_buyer?.toString() ?? null,
      commissionSeller: listing.commission_seller || ''
    },
    marketing: {
      propertyFinder: false, // Default values since these aren't in the original data
      qatarLiving: false,
      propertyoryx: false,
      jamesEdition: false,
      youtubeUrl: listing.embeed_youtube || '',
      tourUrl: listing.tour_360 || ''
    },
    description: {
      title: {
        en: listing.title || '',
        ar: ''  // Default empty since not available in the original data
      },
      propertyFinderTitle: {
        en: listing.propertyfinder_title || '',
        ar: ''  // Default empty since not available in the original data
      },
      description: {
        en: getDescriptionContent('en'),
        ar: getDescriptionContent('ar')
      },
      images: listing.images ?? []
    }
  };
}


export default function InventoryForm() {
  const params = useLocalSearchParams();
  const [currentStep, setCurrentStep] = useState<Step>('basic-details');
  const [isReferralSelectorVisible, setIsReferralSelectorVisible] = useState(false);
  const [validationErrors, setValidationErrors] = useState<ValidationErrors | null>(null);
  const [apiError, setApiError] = useState<string | null>(null);
  const [propertyData, setPropertyData] = useState<PropertyResponse | null>(null);
  const [isCompleting, setIsCompleting] = useState(false);
  const [isImageModalVisible, setIsImageModalVisible] = useState(false);
  const { data: nationalities = [], isLoading: isLoadingNationalities } = useNationalities();

  const [formData, setFormData] = useState<FormData>({
    basicDetails: {
      landlord: null,
      representative: {
        fullname: '',
        email: '',
        prefix_mobile_no: '',
        mobile_no: '',
        qatar_id_no: '',
        nationality_id: null,
      },
      country: null,
      location: null,
      tower: null,
      operationType: null,
      propertyType: null,
    },
    propertyDetails: {
      isExclusive: false,
      isHomepagePromoted: false,
      isInvestmentOpportunity: false,
      developerName: '',
      unitNo: '',
      keyAccess: '',
      status: 'available',
      completionYear: '',
      titleDeed: '',
      referral: null,
    },
    propertyFeatures: {
      size: '',
      parkingPlaces: '',
      bedrooms: '',
      bathrooms: '',
      kitchen: '',
      hasBalcony: false,
      furnishing: 'unfurnished',
      selectedAmenities: [],
      location: {
        latitude: 25.2854,
        longitude: 51.5310,
      },
    },
    pricing: {
      listedPrice: '',
      bestPrice: '',
      offers: '',
      paymentMethod: 'cash',
      proratedAmount: '',
      numberOfCheques: '',
      commissionTenant: '',
      commissionLandlord: '',
      minimumContract: 1,
      commissionBuyer: '',
      commissionSeller: '',
    },
    marketing: {
      propertyFinder: false,
      qatarLiving: false,
      propertyoryx: false,
      jamesEdition: false,
      tourUrl: '',
      youtubeUrl: '',
    },
    description: {
      title: {
        en: '',
        ar: '',
      },
      propertyFinderTitle: {
        en: '',
        ar: '',
      },
      description: {
        en: '',
        ar: '',
      },
      images: []
    },
  });

  const scrollViewRef = React.useRef<ScrollView>(null);
  const currentStepIndex = steps.findIndex(step => step.id === currentStep);

  const { id } = useLocalSearchParams<{ id: string }>();
  const { data: listing, isLoading, isError, refetch } = useQuery({
    queryKey: ['listing', id],
    queryFn: () => getListingDetails(parseInt(id!, 10)),
    enabled: !!id,
    cacheTime: 0,
    staleTime: 0,
    refetchOnMount: true,
    refetchOnWindowFocus: true,
  });

  useEffect(() => {
    if (listing) {
      const transformedData = transformListingToFormData(listing as ListingDetails);
      setFormData(transformedData);
    }
  }, [listing]);

  const createPropertyPayload = (): PropertyPayload => {
    const price = parseInt(formData.pricing.listedPrice?.replace(/[^0-9]/g, ''), 10);
    const best_price = parseInt(formData.pricing.bestPrice?.replace(/[^0-9]/g, ''), 10);
    const offers = parseInt(formData.pricing.offers, 10);
    const construction_year = parseInt(formData.propertyDetails.completionYear, 10);
    const build_up_area = parseInt(formData.propertyFeatures.size, 10);
    const nationalityId = formData.basicDetails.representative.nationality_id;
    const commission_ll = parseFloat(formData.pricing.commissionLandlord);
    const commission_tenant = parseFloat(formData.pricing.commissionTenant);
    const commission_buyer = parseFloat(formData.pricing.commissionBuyer);
    const payment_cheques_no = parseInt(formData.pricing.numberOfCheques, 10);

    return {
      contact_id: formData.basicDetails.landlord.id,
      ad_type: formData.basicDetails.operationType!,
      property_type_id: formData.basicDetails.propertyType,
      country_id: formData.basicDetails.country?.id ?? null,
      location_id: formData.basicDetails.location.id,
      developer: formData.propertyDetails.developerName,
      keys_place: formData.propertyDetails.keyAccess,
      status: formData.propertyDetails.status,
      is_exclusive: formData.propertyDetails.isExclusive ? 1 : 0,
      is_homepage_promoted: formData.propertyDetails.isHomepagePromoted ? 1 : 0,
      is_investment_opportunity: formData.propertyDetails.isInvestmentOpportunity ? 1 : 0,

      unit_no: formData.propertyDetails.unitNo,
      tower_id: formData.basicDetails.tower?.id,
      title: formData.description.title.en,
      title_deed: formData.propertyDetails.titleDeed === '1' ? 1 : 0,
      price: isNaN(price) ? null : price,
      best_price: isNaN(best_price) ? null : best_price,
      offers: isNaN(offers) ? null : offers,
      commission_ll: isNaN(commission_ll) ? null : commission_ll,
      commission_tenant: isNaN(commission_tenant) ? null : commission_tenant,
      commission_buyer: isNaN(commission_buyer) ? null : commission_buyer,
      prorated_rata: formData.pricing.proratedAmount,
      payment_method: formData.pricing.paymentMethod,
      payment_cheques_no: isNaN(payment_cheques_no) ? null : payment_cheques_no,
      geo_lat: formData.propertyFeatures.location?.latitude?.toString() ?? null,
      geo_lon: formData.propertyFeatures.location?.longitude?.toString() ?? null,
      embeed_youtube: formData.marketing.youtubeUrl,
      tour_360: formData.marketing.tourUrl,
      attributes: {
        description: formData.description.description.en,
        construction_year: isNaN(construction_year) ? null : construction_year,
        build_up_area: isNaN(build_up_area) ? null : build_up_area,
        bedrooms: formData.propertyFeatures.bedrooms as any,
        bathrooms: formData.propertyFeatures.bathrooms as any,
        balcony: formData.propertyFeatures.hasBalcony ? 1 : 0,
        kitchen: formData.propertyFeatures.kitchen as any,
        amenities: formData.propertyFeatures.selectedAmenities,
        furnishings: formData.propertyFeatures.furnishing,
        parking_info: formData.propertyFeatures.parkingPlaces,
      },
      representative: {
        fullname: formData.basicDetails.representative.fullname,
        email: formData.basicDetails.representative.email,
        prefix_mobile_no: formData.basicDetails.representative.prefix_mobile_no,
        mobile_no: formData.basicDetails.representative.mobile_no,
        qatar_id_no: formData.basicDetails.representative.qatar_id_no,
        nationality_id: isNaN(nationalityId) ? null : nationalityId,
      },
    };
  };

  const saveMutation = useMutation({
    mutationFn: async () => {
      const payload = createPropertyPayload();
      if (id) {
        const response = await api.patch(`/crm/listing/${id}`, payload);
        return response.data as PropertyResponse;
      } else {
        const response = await api.post('/crm/listing', payload);
        return response.data as PropertyResponse;
      }
    },
    onSuccess: (data) => {
      setValidationErrors(null);
      setApiError(null);
      setPropertyData(data);

      Toast.show({
        type: 'success',
        text1: 'Success',
        text2: `Listing ${listing.ref_no} has been saved successfully`,
        position: 'bottom',
        visibilityTime: 3000,
      });

      if (isCompleting) {
        router.push('/inventory');
      }
    },
    onError: (error: any) => {
      if (error.response?.data?.errors) {
        setValidationErrors(error.response.data.errors);

        Toast.show({
          type: 'error',
          text1: 'Validation Error',
          text2: 'Please check the form for errors',
          position: 'bottom',
          visibilityTime: 4000,
        });
      } else {
        const errorMessage = error.response?.data?.message || 'An error occurred while saving the property';
        setApiError(errorMessage);

        Toast.show({
          type: 'error',
          text1: 'Error',
          text2: errorMessage,
          position: 'bottom',
          visibilityTime: 4000,
        });
      }

      if (scrollViewRef.current) {
        scrollViewRef.current.scrollTo({ y: 0, animated: true });
      }
    },
    onSettled: () => {
      setIsCompleting(false);
    }
  });

  const handleFormDataChange = (updates: Partial<FormData>) => {
    // console.log('updates', updates);
    setFormData(prev => ({
      ...prev,
      ...updates,
    }));
  };

  const formatErrorField = (field: string): string => {
    return field
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  const renderErrors = () => {
    if (!validationErrors && !apiError) return null;

    return (
      <View style={styles.errorContainer}>
        <View style={styles.errorHeader}>
          <AlertCircle size={20} color="#DC2626" />
          <Text style={styles.errorTitle}>
            {apiError || "Please correct the following errors:"}
          </Text>
        </View>

        {validationErrors && Object.entries(validationErrors).map(([field, errors]) => (
          <View key={field} style={styles.errorItem}>
            <Text style={styles.errorField}>{formatErrorField(field)}:</Text>
            {errors.map((error, index) => (
              <Text key={index} style={styles.errorText}>• {error}</Text>
            ))}
          </View>
        ))}
      </View>
    );
  };

  const { data: countries = [] } = useQuery({
    queryKey: ['countries'],
    queryFn: async () => {
      const { data } = await api.get('/countries');
      return data;
    },
  });

  const { data: locations = [], isLoading: isLoadingLocations } = useQuery({
    queryKey: ['locations', formData.basicDetails.country?.id],
    queryFn: async () => {
      const { data } = await api.get(`/country/${formData.basicDetails.country?.id}/locations`);
      return data;
    },
    enabled: !!formData.basicDetails.country?.id,
  });

  const { data: towers = [], isLoading: isLoadingTowers } = useQuery({
    queryKey: ['towers', formData.basicDetails.location?.id],
    queryFn: async () => {
      const { data } = await api.get(`/geography/${formData.basicDetails.location?.id}/towers`);
      return data;
    },
    enabled: !!formData.basicDetails.location?.id,
  });

  useEffect(() => {
    if (params.landlord) {
      try {
        const landlord = JSON.parse(params.landlord as string);
        setFormData(prev => ({ ...prev, basicDetails: { ...prev.basicDetails, landlord } }));
      } catch (error) {
        console.error('Error parsing landlord:', error);
      }
    }
  }, [params.landlord]);

  const isBasicDetailsValid = () => {
    return !!(
      formData.basicDetails.landlord &&
      formData.basicDetails.country &&
      formData.basicDetails.location &&
      formData.basicDetails.operationType &&
      formData.basicDetails.propertyType
    );
  };

  const isCompleteValid = () => {
    const basicValid = isBasicDetailsValid();
    const hasLocation = formData.propertyFeatures?.location?.latitude && formData.propertyFeatures?.location?.longitude;
    const hasListedPrice = formData.pricing?.listedPrice;
    const hasTitles = formData.description?.title?.en && formData.description?.title?.ar;
    const hasDescriptions = formData.description?.description?.en && formData.description?.description?.ar;

    return basicValid && hasLocation && hasListedPrice && hasTitles && hasDescriptions;
  };

  const canSave = isBasicDetailsValid();
  const canComplete = isCompleteValid();

  const handleNext = () => {
    if (currentStepIndex < steps.length - 1) {
      setCurrentStep(steps[currentStepIndex + 1].id);
    } else {
      router.push('/inventory');
    }
  };

  const handleBack = () => {
    if (currentStepIndex > 0) {
      setCurrentStep(steps[currentStepIndex - 1].id);
    } else {
      router.push('/inventory');
    }
  };

  const handleComplete = () => {
    if (!canComplete) return;
    setIsCompleting(true);
    saveMutation.mutate();
  };

  const handleImageSectionPress = () => {
    setIsImageModalVisible(true);
  };

  const handleImageModalClose = () => {
    setIsImageModalVisible(false);
  };

  const handleImageSuccess = () => {
    refetch();
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 'basic-details':
        return (
          <BasicDetailsSection
            formData={formData}
          />
        );
      case 'property-details':
        return (
          <PropertyDetailsSection
            formData={formData}
            onFormDataChange={handleFormDataChange}
            onSelectReferral={() => setIsReferralSelectorVisible(true)}
          />
        );
      case 'property-features':
        return (
          <PropertyFeaturesSection
            formData={formData}
            onFormDataChange={handleFormDataChange}
            onOpenMap={() => { }}
          />
        );
      case 'pricing':
        return (
          <PricingSection
            formData={formData}
            onFormDataChange={handleFormDataChange}
          />
        );
      case 'marketing':
        return (
          <MarketingSection
            formData={formData}
            onFormDataChange={handleFormDataChange}
          />
        );
      case 'description':
        return (
          <DescriptionSection
            formData={formData}
            onFormDataChange={handleFormDataChange}
            onImagePress={handleImageSectionPress}
          />
        );
      default:
        return null;
    }
  };

  return (
    <>
      <Stack.Screen
        options={{
          headerShown: false,
          header: () => (
            <View style={[styles.header, propertyData && styles.headerSaved]}>
              <View style={styles.headerLeft}>
                <Button
                  variant="ghost"
                  icon={<ArrowLeft size={24} color="#111827" />}
                  onPress={handleBack}
                />
                <Text style={styles.headerTitle}>{steps[currentStepIndex].title}</Text>
              </View>

              {propertyData && (
                <View style={styles.headerRight}>
                  <View style={styles.refNo}>
                    <Text style={styles.refNoLabel}>Ref:</Text>
                    <Text style={styles.refNoText}>{propertyData.refNo}</Text>
                  </View>
                  <View style={[
                    styles.publishingStatus,
                    { backgroundColor: propertyData.publishingStatus === 'published' ? '#10B981' : '#6B7280' }
                  ]}>
                    {propertyData.publishingStatus === 'published' ? (
                      <Eye size={16} color="#fff" />
                    ) : (
                      <EyeOff size={16} color="#fff" />
                    )}
                    <Text style={styles.publishingStatusText}>
                      {propertyData.publishingStatus.charAt(0).toUpperCase() +
                        propertyData.publishingStatus.slice(1)}
                    </Text>
                  </View>
                  <View style={styles.ownerInfo}>
                    <UserAvatar
                      name={propertyData.agent.name}
                      size={32}
                      fontSize={14}
                    />
                    <Text style={styles.ownerName}>{propertyData.agent.name}</Text>
                  </View>
                </View>
              )}
            </View>
          ),
        }}
      />

      <View style={styles.container}>
        <View style={styles.progress}>
          {steps.map((step, index) => (
            <React.Fragment key={step.id}>
              <View
                style={[
                  styles.progressStep,
                  index <= currentStepIndex && styles.progressStepActive,
                ]}
              >
                <Text
                  style={[
                    styles.progressStepText,
                    index <= currentStepIndex && styles.progressStepTextActive,
                  ]}
                >
                  {index + 1}
                </Text>
              </View>
              {index < steps.length - 1 && (
                <View
                  style={[
                    styles.progressLine,
                    index < currentStepIndex && styles.progressLineActive,
                  ]}
                />
              )}
            </React.Fragment>
          ))}
        </View>

        <ScrollView
          ref={scrollViewRef}
          style={styles.content}
          keyboardShouldPersistTaps="handled"
        >
          {renderErrors()}
          {renderStepContent()}
        </ScrollView>

        <View style={styles.footer}>
          <View style={styles.footerButtons}>
            <Button
              variant="secondary"
              icon={<ArrowLeft size={20} color="#4B5563" />}
              onPress={handleBack}
              disabled={currentStepIndex === 0 || saveMutation.isPending}
            />
            <Button
              variant="secondary"
              icon={<Check size={20} color="#4B5563" />}
              label="Complete"
              onPress={handleComplete}
              disabled={!canComplete || saveMutation.isPending}
              loading={isCompleting && saveMutation.isPending}
            />
            <Button
              variant="secondary"
              icon={<Save size={20} color="#4B5563" />}
              label="Save"
              onPress={() => saveMutation.mutate()}
              disabled={!canSave || saveMutation.isPending}
              loading={!isCompleting && saveMutation.isPending}
            />
            <Button
              variant="primary"
              icon={<ArrowRight size={20} color="#fff" />}
              onPress={handleNext}
              disabled={!canSave || currentStepIndex === steps.length - 1 || saveMutation.isPending}
            />
          </View>
        </View>
      </View>

      <ReferralSelector
        visible={isReferralSelectorVisible}
        onClose={() => setIsReferralSelectorVisible(false)}
        onSelect={(referral) => {
          handleFormDataChange({
            propertyDetails: {
              ...formData.propertyDetails,
              referral: {
                id: referral.id,
                name: referral.name,
              },
            },
          });
          setIsReferralSelectorVisible(false);
        }}
      />
      <ImageUploadModal
        visible={isImageModalVisible}
        onClose={handleImageModalClose}
        onSuccess={handleImageSuccess}
        listingId={id}
        images={listing?.images || []}
      />
      <Toast />
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  headerSaved: {
    backgroundColor: '#F0FDF4',
    borderBottomColor: '#86EFAC',
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#111827',
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  refNo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  refNoLabel: {
    fontSize: 14,
    color: '#6B7280',
    fontWeight: '500',
  },
  refNoText: {
    fontSize: 14,
    color: '#111827',
    fontWeight: '600',
  },
  publishingStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    gap: 6,
  },
  publishingStatusText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
    textTransform: 'capitalize',
  },
  ownerInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  ownerName: {
    fontSize: 14,
    fontWeight: '500',
    color: '#4B5563',
  },
  progress: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  progressStep: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
  },
  progressStepActive: {
    backgroundColor: '#B89C4C',
  },
  progressStepText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6B7280',
  },
  progressStepTextActive: {
    color: '#fff',
  },
  progressLine: {
    flex: 1,
    height: 2,
    backgroundColor: '#F3F4F6',
    marginHorizontal: 8,
  },
  progressLineActive: {
    backgroundColor: '#B89C4C',
  },
  content: {
    flex: 1,
  },
  footer: {
    padding: 20,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    backgroundColor: '#fff',
  },
  footerButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  errorContainer: {
    backgroundColor: '#FEF2F2',
    borderRadius: 8,
    padding: 16,
    margin: 20,
    marginTop: 0,
    marginBottom: 24,
  },
  errorHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 12,
  },
  errorTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#DC2626',
    flex: 1,
  },
  errorItem: {
    marginTop: 8,
  },
  errorField: {
    fontSize: 14,
    fontWeight: '600',
    color: '#991B1B',
    marginBottom: 4,
  },
  errorText: {
    fontSize: 14,
    color: '#DC2626',
    marginLeft: 8,
    marginBottom: 2,
  },
});
