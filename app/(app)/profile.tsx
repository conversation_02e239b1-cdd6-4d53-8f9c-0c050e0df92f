import React, { useState } from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity, ScrollView, Animated, SafeAreaView } from 'react-native';
import { Stack, router } from 'expo-router';
import { useQuery } from '@tanstack/react-query';
import { useAuth } from '@/contexts/AuthContext';
import { Settings, Mail, Phone, Building2, Languages as Language, Shield, LogOut, ArrowLeft } from 'lucide-react-native';
import { api } from '@/lib/api';
import EditProfileModal from '@/components/EditProfileModal';
import BlinktechLogo from '@/components/BlinktechLogo';
import Button from '@/components/Button';

interface ProfileData {
    id: number;
    email: string;
    name: string;
    phone: string;
    rating: string | null;
    position: string;
    languages: string;
    short_bio: string;
    profile_image_url: string | null;
}

const fetchProfile = async (): Promise<ProfileData> => {
    const { data } = await api.get('/profile');
    return data;
};

function ProfileSkeleton() {
    const pulseAnim = new Animated.Value(0);

    React.useEffect(() => {
        const pulse = Animated.sequence([
            Animated.timing(pulseAnim, {
                toValue: 1,
                duration: 1000,
                useNativeDriver: true,
            }),
            Animated.timing(pulseAnim, {
                toValue: 0,
                duration: 1000,
                useNativeDriver: true,
            }),
        ]);

        Animated.loop(pulse).start();
    }, []);

    const opacity = pulseAnim.interpolate({
        inputRange: [0, 1],
        outputRange: [0.3, 0.7],
    });

    const Placeholder = ({ width, height, style }: { width: number, height: number, style?: any }) => (
        <SafeAreaView style={styles.safeAreaContainer}>
            <ScrollView style={styles.container}>
                <Animated.View
                    style={[{
                        width,
                        height,
                        backgroundColor: '#E5E7EB',
                        borderRadius: 8,
                        opacity,
                    }, style]}
                />
            </ScrollView>
        </SafeAreaView>
    );

    return (
        <ScrollView style={styles.container}>
            <View style={styles.header}>
                <View style={styles.avatarContainer}>
                    <Placeholder
                        width={120}
                        height={120}
                        style={{ borderRadius: 60, marginBottom: 8 }}
                    />
                    <Placeholder width={60} height={32} style={{ borderRadius: 16 }} />
                </View>
                <Placeholder width={180} height={28} style={{ marginBottom: 8 }} />
                <Placeholder width={120} height={20} style={{ marginBottom: 12 }} />
                <Placeholder width={240} height={60} />
            </View>

            <View style={styles.section}>
                <Placeholder width={160} height={24} style={{ marginBottom: 20 }} />
                <View style={{ gap: 16 }}>
                    {[1, 2, 3].map((i) => (
                        <View key={i} style={styles.infoItem}>
                            <Placeholder width={20} height={20} />
                            <Placeholder width={200} height={20} />
                        </View>
                    ))}
                </View>
            </View>

            <View style={styles.section}>
                <Placeholder width={160} height={24} style={{ marginBottom: 20 }} />
                <View style={{ gap: 16 }}>
                    {[1, 2, 3].map((i) => (
                        <View key={i} style={styles.settingItem}>
                            <Placeholder width={20} height={20} />
                            <Placeholder width={160} height={20} />
                        </View>
                    ))}
                </View>
            </View>

            <View style={styles.footer}>
                <BlinktechLogo width={120} height={20} />
            </View>
        </ScrollView>
    );
}

export default function ProfileScreen() {
    const { logout } = useAuth();
    const [isEditing, setIsEditing] = useState(false);

    const { data: profile, isLoading, error } = useQuery({
        queryKey: ['profile'],
        queryFn: fetchProfile,
    });

    const handleLogout = async () => {
        try {
            await logout();
        } catch (error) {
            console.error('Error logging out:', error);
        }
    };

    if (isLoading) {
        return <ProfileSkeleton />;
    }

    if (error) {
        return (
            <SafeAreaView style={styles.safeAreaContainer}>
                <View style={[styles.container, styles.errorContainer]}>
                    <Text style={styles.errorText}>Error loading profile</Text>
                    <TouchableOpacity style={styles.retryButton}>
                        <Text style={styles.retryButtonText}>Retry</Text>
                    </TouchableOpacity>
                </View>
            </SafeAreaView>
        );
    }

    return (
        <>
            <Stack.Screen
                options={{
                    headerShown: true,
                    header: () => (
                        <View style={styles.headerContainer}>
                            <View style={styles.backButtonContainer}>
                                <Button
                                    variant="ghost"
                                    icon={<ArrowLeft size={24} color="#111827" />}
                                    onPress={() => router.back()}
                                />
                            </View>
                            <Text style={styles.headerTitle}>Profile</Text>
                            <View style={styles.backButtonContainer} />
                        </View>
                    ),
                }}
            />

            <SafeAreaView style={styles.safeAreaContainer}>
                <ScrollView style={styles.container}>
                <View style={styles.header}>
                    <View style={styles.avatarContainer}>
                        <Image
                            source={
                                profile?.profile_image_url
                                    ? { uri: profile.profile_image_url }
                                    : { uri: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80' }
                            }
                            style={styles.avatar}
                        />
                        <TouchableOpacity
                            style={styles.editButton}
                            onPress={() => setIsEditing(true)}
                        >
                            <Text style={styles.editButtonText}>Edit</Text>
                        </TouchableOpacity>
                    </View>
                    <Text style={styles.name}>{profile?.name}</Text>
                    <Text style={styles.role}>{profile?.position}</Text>
                    {profile?.short_bio && (
                        <Text style={styles.bio}>{profile.short_bio}</Text>
                    )}
                </View>

                <View style={styles.section}>
                    <Text style={styles.sectionTitle}>Contact Information</Text>
                    <View style={styles.infoItem}>
                        <Mail size={20} color="#6B7280" />
                        <Text style={styles.infoText}>{profile?.email}</Text>
                    </View>
                    {profile?.phone && (
                        <View style={styles.infoItem}>
                            <Phone size={20} color="#6B7280" />
                            <Text style={styles.infoText}>{profile.phone}</Text>
                        </View>
                    )}
                    <View style={styles.infoItem}>
                        <Language size={20} color="#6B7280" />
                        <Text style={styles.infoText}>{profile?.languages || 'Not specified'}</Text>
                    </View>
                    {profile?.rating && (
                        <View style={styles.infoItem}>
                            <Building2 size={20} color="#6B7280" />
                            <Text style={styles.infoText}>Rating: {profile.rating}</Text>
                        </View>
                    )}
                </View>

                <View style={styles.section}>
                    <Text style={styles.sectionTitle}>Account Settings</Text>
                    <TouchableOpacity style={styles.settingItem}>
                        <Settings size={20} color="#6B7280" />
                        <Text style={styles.settingText}>Preferences</Text>
                    </TouchableOpacity>
                    <TouchableOpacity style={styles.settingItem}>
                        <Shield size={20} color="#6B7280" />
                        <Text style={styles.settingText}>Privacy & Security</Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                        style={[styles.settingItem, styles.logoutButton]}
                        onPress={handleLogout}
                    >
                        <LogOut size={20} color="#EF4444" />
                        <Text style={styles.logoutText}>Log Out</Text>
                    </TouchableOpacity>
                </View>

                {/* Add Footer */}
                <View style={styles.footer}>
                    <BlinktechLogo width={120} height={20} />
                </View>
            </ScrollView>

            {isEditing && profile && (
                <EditProfileModal
                    profile={profile}
                    onClose={() => setIsEditing(false)}
                />
            )}
        </SafeAreaView>
        </>
    );
}

const styles = StyleSheet.create({
    headerContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        paddingHorizontal: 20,
        paddingTop: 60,
        paddingBottom: 20,
        backgroundColor: '#fff',
        borderBottomWidth: 1,
        borderBottomColor: '#E5E7EB',
        position: 'relative',
    },
    headerTitle: {
        fontSize: 20,
        fontWeight: '600',
        color: '#111827',
        textAlign: 'center',
        flex: 1,
    },
    backButtonContainer: {
        width: 40,
        alignItems: 'flex-start',
    },
    safeAreaContainer: {
        flex: 1,
        backgroundColor: '#F9FAFB',
    },
    container: {
        flex: 1,
        backgroundColor: '#F9FAFB',
    },
    loadingContainer: {
        justifyContent: 'center',
        alignItems: 'center',
    },
    errorContainer: {
        justifyContent: 'center',
        alignItems: 'center',
    },
    errorText: {
        color: '#EF4444',
        fontSize: 16,
        marginBottom: 12,
    },
    retryButton: {
        backgroundColor: '#3B82F6',
        paddingHorizontal: 16,
        paddingVertical: 8,
        borderRadius: 8,
    },
    retryButtonText: {
        color: '#fff',
        fontSize: 14,
        fontWeight: '500',
    },
    header: {
        backgroundColor: '#fff',
        padding: 20,
        alignItems: 'center',
        borderBottomWidth: 1,
        borderBottomColor: '#E5E7EB',
    },
    avatarContainer: {
        marginBottom: 16,
        alignItems: 'center',
    },
    avatar: {
        width: 120,
        height: 120,
        borderRadius: 60,
        marginBottom: 8,
    },
    editButton: {
        position: 'absolute',
        right: -20,
        bottom: 8,
        backgroundColor: '#3B82F6',
        paddingHorizontal: 12,
        paddingVertical: 6,
        borderRadius: 16,
    },
    editButtonText: {
        color: '#fff',
        fontSize: 14,
        fontWeight: '500',
    },
    name: {
        fontSize: 24,
        fontWeight: '600',
        color: '#1F2937',
        marginBottom: 4,
    },
    role: {
        fontSize: 16,
        color: '#6B7280',
        marginBottom: 8,
    },
    bio: {
        fontSize: 14,
        color: '#4B5563',
        textAlign: 'center',
        paddingHorizontal: 20,
        lineHeight: 20,
    },
    section: {
        backgroundColor: '#fff',
        padding: 20,
        marginTop: 12,
    },
    sectionTitle: {
        fontSize: 18,
        fontWeight: '600',
        color: '#1F2937',
        marginBottom: 16,
    },
    infoItem: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 16,
        gap: 12,
    },
    infoText: {
        fontSize: 16,
        color: '#4B5563',
    },
    settingItem: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 12,
        gap: 12,
    },
    settingText: {
        fontSize: 16,
        color: '#4B5563',
    },
    logoutButton: {
        marginTop: 8,
        borderTopWidth: 1,
        borderTopColor: '#E5E7EB',
    },
    logoutText: {
        color: '#EF4444',
        fontSize: 16,
        fontWeight: '500',
    },
    footer: {
        paddingVertical: 32,
        alignItems: 'center',
        justifyContent: 'center',
        borderTopWidth: 1,
        borderTopColor: '#E5E7EB',
        marginTop: 20,
    },
});
