import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Switch,
  Platform,
  FlatList,
  Dimensions,
} from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import DateTimePicker from '@react-native-community/datetimepicker';
import { ArrowLeft, Calendar, Save } from 'lucide-react-native';
import {
  fetchListingTypes,
  fetchGeographies,
  fetchBedrooms,
  fetchListings,
  api
} from '@/lib/api';
import Dropdown from '@/components/Dropdown';
import MultiSelectDropdown from '@/components/MultiSelectDropdown';
import ListingCard from '@/components/ListingCard';
import Pagination from '@/components/Pagination';
import {
  RENT_SALE_OPTIONS,
  MIN_AREA_OPTIONS,
  MAX_AREA_OPTIONS,
  PRICE_MIN_OPTIONS,
  PRICE_MAX_OPTIONS,
  transformApiDataToDropdownOptions
} from '@/constants/dropdownOptions';

interface StatusOption {
  id: number;
  name: string;
  background_color: string;
  is_disabled: number;
}

interface MeetingConfig {
  title: string;
  content: string;
  dueDate: string;
  priority: string;
  sendEmail: boolean;
  remarks: string;
}

interface FollowUpConfig {
  title: string;
  content: string;
  dueDate: string;
  priority: string;
  sendEmail: boolean;
  remarks: string;
}

interface OfferNegotiationConfig {
  search: string;
  rentSale: string;
  towerBuilding: string;
  bedrooms: string[];
  minArea: string;
  maxArea: string;
  priceMin: string;
  priceMax: string;
}

interface ViewingConfig {
  search: string;
  rentSale: string;
  towerBuilding: string;
  bedrooms: string[];
  minArea: string;
  maxArea: string;
  priceMin: string;
  priceMax: string;
  selectedProperties: string[];
}

const { width: screenWidth } = Dimensions.get('window');

export default function EditStatusScreen() {
  const router = useRouter();
  const params = useLocalSearchParams();
  const leadId = params.id as string;
  const currentStatusId = params.statusId as string;
  const statuses = JSON.parse(params.statuses as string) as StatusOption[];
  
  const [selectedStatus, setSelectedStatus] = useState(currentStatusId);
  const [showMeetingConfig, setShowMeetingConfig] = useState(false);
  const [showViewingConfig, setShowViewingConfig] = useState(false);
  const [showFollowUpConfig, setShowFollowUpConfig] = useState(false);
  const [showOfferNegotiationConfig, setShowOfferNegotiationConfig] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [datePickerMode, setDatePickerMode] = useState<'date' | 'time'>('date');
  const [currentConfigType, setCurrentConfigType] = useState<'meeting' | 'followUp'>('meeting');

  // Fetch dropdown data
  const { data: geographies = [] } = useQuery({
    queryKey: ['geographies'],
    queryFn: fetchGeographies,
  });

  const { data: listingTypes = [] } = useQuery({
    queryKey: ['listingTypes'],
    queryFn: fetchListingTypes,
  });

  const { data: bedrooms = [] } = useQuery({
    queryKey: ['bedrooms'],
    queryFn: fetchBedrooms,
  });

  // Transform API data to dropdown format
  const locationOptions = transformApiDataToDropdownOptions(geographies);
  const bedroomOptions = transformApiDataToDropdownOptions(bedrooms);

  const [meetingConfig, setMeetingConfig] = useState<MeetingConfig>({
    title: 'Meeting appointment',
    content: 'Meeting appointment reminder.',
    dueDate: '',
    priority: 'Low',
    sendEmail: false,
    remarks: '',
  });

  const [viewingConfig, setViewingConfig] = useState<ViewingConfig>({
    search: '',
    rentSale: '',
    towerBuilding: '',
    bedrooms: [],
    minArea: '',
    maxArea: '',
    priceMin: '',
    priceMax: '',
    selectedProperties: [],
  });

  const [followUpConfig, setFollowUpConfig] = useState<FollowUpConfig>({
    title: 'Follow up',
    content: 'Follow up reminder.',
    dueDate: '',
    priority: 'Low',
    sendEmail: false,
    remarks: '',
  });

  const [offerNegotiationConfig, setOfferNegotiationConfig] = useState<OfferNegotiationConfig>({
    search: '',
    rentSale: '',
    towerBuilding: '',
    bedrooms: [],
    minArea: '',
    maxArea: '',
    priceMin: '',
    priceMax: '',
  });

  // Pagination state
  const ITEMS_PER_PAGE = 10;
  const queryClient = useQueryClient();
  const [currentViewingPage, setCurrentViewingPage] = useState(1);
  const [currentOfferPage, setCurrentOfferPage] = useState(1);

  const enabledStatuses = statuses.filter(status => status.is_disabled === 0);

  // Get selected location object for towers query
  const selectedLocation = geographies.find((geo: any) => geo.id?.toString() === viewingConfig.search);

  const { data: towers = [{ id: 'any', name: 'Any' }] } = useQuery({
    queryKey: ['towers', selectedLocation?.id],
    queryFn: async () => {
      if (!selectedLocation?.id) {
        return [{ id: 'any', name: 'Any' }];
      }
      try {
        const { data } = await api.get(`/geography/${selectedLocation.id}/towers`);
        return [{ id: 'any', name: 'Any' }, ...data];
      } catch (error) {
        console.error('Error fetching towers:', error);
        return [{ id: 'any', name: 'Any' }];
      }
    },
    enabled: true,
  });

  // Transform towers to dropdown format
  const towerBuildingOptions = transformApiDataToDropdownOptions(towers);
  const finalTowerOptions = towerBuildingOptions.length > 0 ? towerBuildingOptions : [
    { id: 'any', label: 'Any' },
    { id: 'test', label: 'Test Tower' }
  ];

  const formatStatusName = (name: string) => {
    return name.replace(/_/g, ' ');
  };

  const handleStatusSelect = (statusId: string) => {
    setSelectedStatus(statusId);
    
    // Reset all config states
    setShowMeetingConfig(false);
    setShowViewingConfig(false);
    setShowFollowUpConfig(false);
    setShowOfferNegotiationConfig(false);

    // Show appropriate config based on status
    const selectedStatusObj = statuses.find(s => s.id.toString() === statusId);
    if (selectedStatusObj) {
      switch (selectedStatusObj.name) {
        case 'MEETING_SCHEDULED':
          setShowMeetingConfig(true);
          break;
        case 'VIEWING_SCHEDULED':
          setShowViewingConfig(true);
          break;
        case 'FOLLOW_UP':
          setShowFollowUpConfig(true);
          break;
        case 'OFFER_NEGOTIATION':
          setShowOfferNegotiationConfig(true);
          break;
      }
    }
  };

  const handleSave = () => {
    // Navigate back with the selected status and config
    const selectedStatusObj = statuses.find(s => s.id.toString() === selectedStatus);
    
    if (selectedStatusObj) {
      let config = {};
      
      switch (selectedStatusObj.name) {
        case 'MEETING_SCHEDULED':
          config = { meetingConfig };
          break;
        case 'VIEWING_SCHEDULED':
          config = { viewingConfig };
          break;
        case 'FOLLOW_UP':
          config = { followUpConfig };
          break;
        case 'OFFER_NEGOTIATION':
          config = { offerNegotiationConfig };
          break;
      }
      
      // Navigate back with result
      router.back();
      // TODO: Pass the result back to the calling screen
    }
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <ArrowLeft size={24} color="#000" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Edit Status</Text>
        <TouchableOpacity onPress={handleSave} style={styles.saveButton}>
          <Save size={24} color="#B89C4C" />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Status Selection */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Select Status</Text>
          <View style={styles.statusGrid}>
            {enabledStatuses.map((status) => (
              <TouchableOpacity
                key={status.id}
                style={[
                  styles.statusPill,
                  selectedStatus === status.id.toString() && styles.selectedStatusPill
                ]}
                onPress={() => handleStatusSelect(status.id.toString())}
              >
                <View
                  style={[
                    styles.statusIndicator,
                    { backgroundColor: status.background_color }
                  ]}
                />
                <Text style={[
                  styles.statusText,
                  selectedStatus === status.id.toString() && styles.selectedStatusText
                ]}>
                  {formatStatusName(status.name)}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Configuration Sections */}
        {showMeetingConfig && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Meeting Configuration</Text>
            <View style={styles.configContainer}>
              <View style={styles.inputGroup}>
                <Text style={styles.inputLabel}>Title</Text>
                <TextInput
                  style={styles.textInput}
                  value={meetingConfig.title}
                  onChangeText={(text) => setMeetingConfig(prev => ({ ...prev, title: text }))}
                  placeholder="Meeting title"
                />
              </View>

              <View style={styles.inputGroup}>
                <Text style={styles.inputLabel}>Content</Text>
                <TextInput
                  style={[styles.textInput, styles.textArea]}
                  value={meetingConfig.content}
                  onChangeText={(text) => setMeetingConfig(prev => ({ ...prev, content: text }))}
                  placeholder="Meeting description"
                  multiline
                  numberOfLines={3}
                />
              </View>

              <View style={styles.inputGroup}>
                <Text style={styles.inputLabel}>Due Date</Text>
                <TouchableOpacity
                  style={styles.dateButton}
                  onPress={() => {
                    setCurrentConfigType('meeting');
                    setDatePickerMode('date');
                    setShowDatePicker(true);
                  }}
                >
                  <Calendar size={16} color="#6B7280" />
                  <Text style={styles.dateButtonText}>
                    {meetingConfig.dueDate || 'Select date'}
                  </Text>
                </TouchableOpacity>
              </View>

              <View style={styles.inputGroup}>
                <Text style={styles.inputLabel}>Priority</Text>
                <Dropdown
                  options={[
                    { id: 'Low', label: 'Low' },
                    { id: 'Medium', label: 'Medium' },
                    { id: 'High', label: 'High' }
                  ]}
                  selectedValue={meetingConfig.priority}
                  onSelect={(value) => setMeetingConfig(prev => ({ ...prev, priority: value }))}
                  placeholder="Select priority"
                />
              </View>

              <View style={styles.switchGroup}>
                <Text style={styles.inputLabel}>Send Email</Text>
                <Switch
                  value={meetingConfig.sendEmail}
                  onValueChange={(value) => setMeetingConfig(prev => ({ ...prev, sendEmail: value }))}
                  trackColor={{ false: '#E5E7EB', true: '#B89C4C' }}
                  thumbColor={meetingConfig.sendEmail ? '#fff' : '#fff'}
                />
              </View>

              <View style={styles.inputGroup}>
                <Text style={styles.inputLabel}>Remarks</Text>
                <TextInput
                  style={[styles.textInput, styles.textArea]}
                  value={meetingConfig.remarks}
                  onChangeText={(text) => setMeetingConfig(prev => ({ ...prev, remarks: text }))}
                  placeholder="Additional remarks"
                  multiline
                  numberOfLines={3}
                />
              </View>
            </View>
          </View>
        )}

        {showFollowUpConfig && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Follow Up Configuration</Text>
            <View style={styles.configContainer}>
              <View style={styles.inputGroup}>
                <Text style={styles.inputLabel}>Title</Text>
                <TextInput
                  style={styles.textInput}
                  value={followUpConfig.title}
                  onChangeText={(text) => setFollowUpConfig(prev => ({ ...prev, title: text }))}
                  placeholder="Follow up title"
                />
              </View>

              <View style={styles.inputGroup}>
                <Text style={styles.inputLabel}>Content</Text>
                <TextInput
                  style={[styles.textInput, styles.textArea]}
                  value={followUpConfig.content}
                  onChangeText={(text) => setFollowUpConfig(prev => ({ ...prev, content: text }))}
                  placeholder="Follow up description"
                  multiline
                  numberOfLines={3}
                />
              </View>

              <View style={styles.inputGroup}>
                <Text style={styles.inputLabel}>Due Date</Text>
                <TouchableOpacity
                  style={styles.dateButton}
                  onPress={() => {
                    setCurrentConfigType('followUp');
                    setDatePickerMode('date');
                    setShowDatePicker(true);
                  }}
                >
                  <Calendar size={16} color="#6B7280" />
                  <Text style={styles.dateButtonText}>
                    {followUpConfig.dueDate || 'Select date'}
                  </Text>
                </TouchableOpacity>
              </View>

              <View style={styles.inputGroup}>
                <Text style={styles.inputLabel}>Priority</Text>
                <Dropdown
                  options={[
                    { id: 'Low', label: 'Low' },
                    { id: 'Medium', label: 'Medium' },
                    { id: 'High', label: 'High' }
                  ]}
                  selectedValue={followUpConfig.priority}
                  onSelect={(value) => setFollowUpConfig(prev => ({ ...prev, priority: value }))}
                  placeholder="Select priority"
                />
              </View>

              <View style={styles.switchGroup}>
                <Text style={styles.inputLabel}>Send Email</Text>
                <Switch
                  value={followUpConfig.sendEmail}
                  onValueChange={(value) => setFollowUpConfig(prev => ({ ...prev, sendEmail: value }))}
                  trackColor={{ false: '#E5E7EB', true: '#B89C4C' }}
                  thumbColor={followUpConfig.sendEmail ? '#fff' : '#fff'}
                />
              </View>

              <View style={styles.inputGroup}>
                <Text style={styles.inputLabel}>Remarks</Text>
                <TextInput
                  style={[styles.textInput, styles.textArea]}
                  value={followUpConfig.remarks}
                  onChangeText={(text) => setFollowUpConfig(prev => ({ ...prev, remarks: text }))}
                  placeholder="Additional remarks"
                  multiline
                  numberOfLines={3}
                />
              </View>
            </View>
          </View>
        )}

        {showViewingConfig && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Viewing Configuration</Text>
            <View style={styles.configContainer}>
              <View style={styles.row}>
                <View style={styles.halfWidth}>
                  <Text style={styles.inputLabel}>Location</Text>
                  <Dropdown
                    options={locationOptions}
                    selectedValue={viewingConfig.search}
                    onSelect={(value) => setViewingConfig(prev => ({ ...prev, search: value }))}
                    placeholder="Select location"
                  />
                </View>
                <View style={styles.halfWidth}>
                  <Text style={styles.inputLabel}>Rent/Sale</Text>
                  <Dropdown
                    options={RENT_SALE_OPTIONS}
                    selectedValue={viewingConfig.rentSale}
                    onSelect={(value) => setViewingConfig(prev => ({ ...prev, rentSale: value }))}
                    placeholder="Select type"
                  />
                </View>
              </View>

              <View style={styles.row}>
                <View style={styles.halfWidth}>
                  <Text style={styles.inputLabel}>Tower/Building</Text>
                  <Dropdown
                    options={finalTowerOptions}
                    selectedValue={viewingConfig.towerBuilding}
                    onSelect={(value) => setViewingConfig(prev => ({ ...prev, towerBuilding: value }))}
                    placeholder="Select tower"
                  />
                </View>
                <View style={styles.halfWidth}>
                  <Text style={styles.inputLabel}>Bedrooms</Text>
                  <MultiSelectDropdown
                    options={bedroomOptions}
                    selectedValues={viewingConfig.bedrooms}
                    onSelectionChange={(values) => setViewingConfig(prev => ({ ...prev, bedrooms: values }))}
                    placeholder="Select bedrooms"
                  />
                </View>
              </View>

              <View style={styles.row}>
                <View style={styles.halfWidth}>
                  <Text style={styles.inputLabel}>Min Area</Text>
                  <Dropdown
                    options={MIN_AREA_OPTIONS}
                    selectedValue={viewingConfig.minArea}
                    onSelect={(value) => setViewingConfig(prev => ({ ...prev, minArea: value }))}
                    placeholder="Min area"
                  />
                </View>
                <View style={styles.halfWidth}>
                  <Text style={styles.inputLabel}>Max Area</Text>
                  <Dropdown
                    options={MAX_AREA_OPTIONS}
                    selectedValue={viewingConfig.maxArea}
                    onSelect={(value) => setViewingConfig(prev => ({ ...prev, maxArea: value }))}
                    placeholder="Max area"
                  />
                </View>
              </View>

              <View style={styles.row}>
                <View style={styles.halfWidth}>
                  <Text style={styles.inputLabel}>Min Price</Text>
                  <Dropdown
                    options={PRICE_MIN_OPTIONS}
                    selectedValue={viewingConfig.priceMin}
                    onSelect={(value) => setViewingConfig(prev => ({ ...prev, priceMin: value }))}
                    placeholder="Min price"
                  />
                </View>
                <View style={styles.halfWidth}>
                  <Text style={styles.inputLabel}>Max Price</Text>
                  <Dropdown
                    options={PRICE_MAX_OPTIONS}
                    selectedValue={viewingConfig.priceMax}
                    onSelect={(value) => setViewingConfig(prev => ({ ...prev, priceMax: value }))}
                    placeholder="Max price"
                  />
                </View>
              </View>
            </View>
          </View>
        )}

        {showOfferNegotiationConfig && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Offer Negotiation Configuration</Text>
            <View style={styles.configContainer}>
              <View style={styles.row}>
                <View style={styles.halfWidth}>
                  <Text style={styles.inputLabel}>Location</Text>
                  <Dropdown
                    options={locationOptions}
                    selectedValue={offerNegotiationConfig.search}
                    onSelect={(value) => setOfferNegotiationConfig(prev => ({ ...prev, search: value }))}
                    placeholder="Select location"
                  />
                </View>
                <View style={styles.halfWidth}>
                  <Text style={styles.inputLabel}>Rent/Sale</Text>
                  <Dropdown
                    options={RENT_SALE_OPTIONS}
                    selectedValue={offerNegotiationConfig.rentSale}
                    onSelect={(value) => setOfferNegotiationConfig(prev => ({ ...prev, rentSale: value }))}
                    placeholder="Select type"
                  />
                </View>
              </View>

              <View style={styles.row}>
                <View style={styles.halfWidth}>
                  <Text style={styles.inputLabel}>Tower/Building</Text>
                  <Dropdown
                    options={finalTowerOptions}
                    selectedValue={offerNegotiationConfig.towerBuilding}
                    onSelect={(value) => setOfferNegotiationConfig(prev => ({ ...prev, towerBuilding: value }))}
                    placeholder="Select tower"
                  />
                </View>
                <View style={styles.halfWidth}>
                  <Text style={styles.inputLabel}>Bedrooms</Text>
                  <MultiSelectDropdown
                    options={bedroomOptions}
                    selectedValues={offerNegotiationConfig.bedrooms}
                    onSelectionChange={(values) => setOfferNegotiationConfig(prev => ({ ...prev, bedrooms: values }))}
                    placeholder="Select bedrooms"
                  />
                </View>
              </View>

              <View style={styles.row}>
                <View style={styles.halfWidth}>
                  <Text style={styles.inputLabel}>Min Area</Text>
                  <Dropdown
                    options={MIN_AREA_OPTIONS}
                    selectedValue={offerNegotiationConfig.minArea}
                    onSelect={(value) => setOfferNegotiationConfig(prev => ({ ...prev, minArea: value }))}
                    placeholder="Min area"
                  />
                </View>
                <View style={styles.halfWidth}>
                  <Text style={styles.inputLabel}>Max Area</Text>
                  <Dropdown
                    options={MAX_AREA_OPTIONS}
                    selectedValue={offerNegotiationConfig.maxArea}
                    onSelect={(value) => setOfferNegotiationConfig(prev => ({ ...prev, maxArea: value }))}
                    placeholder="Max area"
                  />
                </View>
              </View>

              <View style={styles.row}>
                <View style={styles.halfWidth}>
                  <Text style={styles.inputLabel}>Min Price</Text>
                  <Dropdown
                    options={PRICE_MIN_OPTIONS}
                    selectedValue={offerNegotiationConfig.priceMin}
                    onSelect={(value) => setOfferNegotiationConfig(prev => ({ ...prev, priceMin: value }))}
                    placeholder="Min price"
                  />
                </View>
                <View style={styles.halfWidth}>
                  <Text style={styles.inputLabel}>Max Price</Text>
                  <Dropdown
                    options={PRICE_MAX_OPTIONS}
                    selectedValue={offerNegotiationConfig.priceMax}
                    onSelect={(value) => setOfferNegotiationConfig(prev => ({ ...prev, priceMax: value }))}
                    placeholder="Max price"
                  />
                </View>
              </View>
            </View>
          </View>
        )}

        {/* Date Picker */}
        {showDatePicker && (
          <DateTimePicker
            value={new Date()}
            mode={datePickerMode}
            display={Platform.OS === 'ios' ? 'spinner' : 'default'}
            onChange={(event, selectedDate) => {
              setShowDatePicker(false);
              if (selectedDate) {
                const dateString = selectedDate.toISOString().split('T')[0];
                if (currentConfigType === 'meeting') {
                  setMeetingConfig(prev => ({ ...prev, dueDate: dateString }));
                } else if (currentConfigType === 'followUp') {
                  setFollowUpConfig(prev => ({ ...prev, dueDate: dateString }));
                }
              }
            }}
          />
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingTop: 60,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000',
  },
  saveButton: {
    padding: 8,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000',
    marginBottom: 12,
  },
  statusGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  statusPill: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    backgroundColor: '#fff',
    gap: 6,
  },
  selectedStatusPill: {
    borderColor: '#B89C4C',
    backgroundColor: '#FEF3C7',
  },
  statusIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  statusText: {
    fontSize: 14,
    color: '#6B7280',
    fontWeight: '500',
  },
  selectedStatusText: {
    color: '#B89C4C',
    fontWeight: '600',
  },
  configContainer: {
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    padding: 16,
    gap: 16,
  },
  inputGroup: {
    gap: 6,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 14,
    backgroundColor: '#fff',
  },
  textArea: {
    minHeight: 80,
    textAlignVertical: 'top',
  },
  dateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    backgroundColor: '#fff',
    gap: 8,
  },
  dateButtonText: {
    fontSize: 14,
    color: '#374151',
  },
  switchGroup: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  row: {
    flexDirection: 'row',
    gap: 12,
  },
  halfWidth: {
    flex: 1,
  },
});
