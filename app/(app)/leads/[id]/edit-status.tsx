import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Switch,
} from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { ArrowLeft, Calendar } from 'lucide-react-native';
import DateTimePicker from '@react-native-community/datetimepicker';
import Dropdown from '@/components/Dropdown';
import MultiSelectDropdown from '@/components/MultiSelectDropdown';
import {
  RENT_SALE_OPTIONS,
  MIN_AREA_OPTIONS,
  MAX_AREA_OPTIONS,
  PRICE_MIN_OPTIONS,
  PRICE_MAX_OPTIONS,
} from '@/constants/dropdownOptions';

interface StatusOption {
  id: number;
  name: string;
  background_color: string;
  is_disabled: number;
}

interface MeetingConfig {
  title: string;
  content: string;
  dueDate: string;
  priority: string;
  sendEmail: boolean;
  remarks: string;
}

interface FollowUpConfig {
  title: string;
  content: string;
  dueDate: string;
  priority: string;
  sendEmail: boolean;
  remarks: string;
}

interface ViewingConfig {
  search: string;
  rentSale: string;
  towerBuilding: string;
  bedrooms: string[];
  minArea: string;
  maxArea: string;
  priceMin: string;
  priceMax: string;
  selectedProperties: string[];
}

export default function EditStatusScreen() {
  const router = useRouter();
  const params = useLocalSearchParams();
  const leadId = params.id as string;
  const currentStatusId = params.statusId as string;
  const statuses = JSON.parse(decodeURIComponent(params.statuses as string)) as StatusOption[];

  const [tempSelectedStatus, setTempSelectedStatus] = useState(currentStatusId);
  const [showMeetingConfig, setShowMeetingConfig] = useState(false);
  const [showFollowUpConfig, setShowFollowUpConfig] = useState(false);
  const [showViewingConfig, setShowViewingConfig] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [currentConfigType, setCurrentConfigType] = useState<'meeting' | 'followUp'>('meeting');

  const [meetingConfig, setMeetingConfig] = useState<MeetingConfig>({
    title: 'Meeting appointment',
    content: 'Meeting appointment reminder.',
    dueDate: '',
    priority: 'Low',
    sendEmail: false,
    remarks: '',
  });

  const [followUpConfig, setFollowUpConfig] = useState<FollowUpConfig>({
    title: 'Follow up',
    content: 'Follow up reminder.',
    dueDate: '',
    priority: 'Low',
    sendEmail: false,
    remarks: '',
  });

  const [viewingConfig, setViewingConfig] = useState<ViewingConfig>({
    search: '',
    rentSale: '',
    towerBuilding: '',
    bedrooms: [],
    minArea: '',
    maxArea: '',
    priceMin: '',
    priceMax: '',
    selectedProperties: [],
  });

  const enabledStatuses = statuses.filter(status => status.is_disabled === 0);

  // Simple dropdown options for testing
  const locationOptions = [
    { id: '1', label: 'Dubai Marina' },
    { id: '2', label: 'Downtown Dubai' },
    { id: '3', label: 'JBR' },
  ];

  const bedroomOptions = [
    { id: '1', label: '1 Bedroom' },
    { id: '2', label: '2 Bedrooms' },
    { id: '3', label: '3 Bedrooms' },
  ];

  const towerOptions = [
    { id: 'any', label: 'Any' },
    { id: '1', label: 'Tower 1' },
    { id: '2', label: 'Tower 2' },
  ];

  useEffect(() => {
    setTempSelectedStatus(currentStatusId);
  }, [currentStatusId]);

  const formatStatusName = (name: string) => {
    return name.replace(/_/g, ' ');
  };

  const handleStatusToggle = (statusId: string) => {
    // Always select the new status (no deselection)
    setTempSelectedStatus(statusId);

    // Check if MEETING_SCHEDULED, VIEWING_SCHEDULED, or FOLLOW_UP is selected
    const selectedStatusObj = statuses.find(s => s.id.toString() === statusId);
    if (selectedStatusObj && selectedStatusObj.name === 'MEETING_SCHEDULED') {
      setShowMeetingConfig(true);
      setShowFollowUpConfig(false);
      setShowViewingConfig(false);
    } else if (selectedStatusObj && selectedStatusObj.name === 'VIEWING_SCHEDULED') {
      setShowViewingConfig(true);
      setShowMeetingConfig(false);
      setShowFollowUpConfig(false);
    } else if (selectedStatusObj && selectedStatusObj.name === 'FOLLOW_UP') {
      setShowFollowUpConfig(true);
      setShowMeetingConfig(false);
      setShowViewingConfig(false);
    } else {
      setShowMeetingConfig(false);
      setShowFollowUpConfig(false);
      setShowViewingConfig(false);
    }
  };

  const handleSave = () => {
    // TODO: Implement save logic
    console.log('Saving status:', tempSelectedStatus);
    router.back();
  };

  const updateMeetingConfig = (field: keyof MeetingConfig, value: string | boolean) => {
    setMeetingConfig(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const updateFollowUpConfig = (field: keyof FollowUpConfig, value: string | boolean) => {
    setFollowUpConfig(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const updateViewingConfig = (field: keyof ViewingConfig, value: string | string[]) => {
    setViewingConfig(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const resetViewingFilters = () => {
    console.log('🔄 Resetting viewing filters...');
    setViewingConfig({
      search: '',
      rentSale: '',
      towerBuilding: '',
      bedrooms: [],
      minArea: '',
      maxArea: '',
      priceMin: '',
      priceMax: '',
      selectedProperties: [],
    });
  };

  // Check if any filters are applied
  const hasFilters = viewingConfig?.search || viewingConfig?.rentSale || viewingConfig?.towerBuilding ||
                    (viewingConfig?.bedrooms && viewingConfig.bedrooms.length > 0) || viewingConfig?.minArea ||
                    viewingConfig?.maxArea || viewingConfig?.priceMin || viewingConfig?.priceMax;

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <ArrowLeft size={24} color="#000" />
        </TouchableOpacity>
        <Text style={styles.title}>Select Status</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Status Pills */}
        <ScrollView
          horizontal
          style={styles.statusScrollContainer}
          contentContainerStyle={styles.statusScrollContent}
          showsHorizontalScrollIndicator={false}
        >
          {enabledStatuses.map((status) => (
            <TouchableOpacity
              key={status.id}
              style={[
                styles.statusPill,
                tempSelectedStatus === status.id.toString() && styles.selectedStatusPill
              ]}
              onPress={() => handleStatusToggle(status.id.toString())}
            >
              <Text
                style={[
                  styles.statusPillText,
                  tempSelectedStatus === status.id.toString() && styles.selectedStatusPillText
                ]}
              >
                {formatStatusName(status.name)}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>

        {/* Meeting Configuration */}
        {showMeetingConfig && (
          <View style={styles.meetingConfigContainer}>
            <Text style={styles.meetingConfigTitle}>Lead configuration</Text>

            {/* Reminder Section */}
            <Text style={styles.sectionTitle}>Reminder</Text>

            {/* Title */}
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Title</Text>
              <TextInput
                style={styles.textInput}
                value={meetingConfig.title}
                onChangeText={(text) => updateMeetingConfig('title', text)}
                placeholder="Meeting appointment"
              />
            </View>

            {/* Content */}
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Content</Text>
              <TextInput
                style={[styles.textInput, styles.textArea]}
                value={meetingConfig.content}
                onChangeText={(text) => updateMeetingConfig('content', text)}
                placeholder="Meeting appointment reminder."
                multiline
                numberOfLines={3}
              />
            </View>

            {/* Due Date */}
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Due Date</Text>
              <View style={styles.dateInputContainer}>
                <TextInput
                  style={[styles.textInput, styles.dateInput]}
                  value={meetingConfig.dueDate}
                  onChangeText={(text) => updateMeetingConfig('dueDate', text)}
                  placeholder="dd.mm.yyyy, --:--"
                />
                <TouchableOpacity
                  style={styles.calendarButton}
                  onPress={() => {
                    setCurrentConfigType('meeting');
                    setShowDatePicker(true);
                  }}
                >
                  <Calendar size={20} color="#6B7280" />
                </TouchableOpacity>
              </View>
              {/* Inline Date Picker for Meeting */}
              {showDatePicker && currentConfigType === 'meeting' && (
                <View style={styles.inlineDatePicker}>
                  <DateTimePicker
                    value={new Date()}
                    mode="date"
                    display="default"
                    onChange={(event, selectedDate) => {
                      setShowDatePicker(false);
                      if (selectedDate) {
                        const dateString = selectedDate.toLocaleDateString();
                        updateMeetingConfig('dueDate', dateString);
                      }
                    }}
                  />
                </View>
              )}
              {!meetingConfig.dueDate && (
                <Text style={styles.errorText}>Due Date is required.</Text>
              )}
            </View>

            {/* Priority */}
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Priority</Text>
              <TouchableOpacity style={styles.dropdownButton}>
                <Text style={styles.dropdownText}>{meetingConfig.priority}</Text>
                <Text style={styles.dropdownArrow}>▼</Text>
              </TouchableOpacity>
            </View>

            {/* Send Email */}
            <View style={styles.checkboxContainer}>
              <Switch
                value={meetingConfig.sendEmail}
                onValueChange={(value) => updateMeetingConfig('sendEmail', value)}
                trackColor={{ false: '#E5E7EB', true: '#B89C4C' }}
                thumbColor={meetingConfig.sendEmail ? '#fff' : '#fff'}
              />
              <Text style={styles.checkboxLabel}>Send Email</Text>
            </View>

            {/* Remarks Section */}
            <Text style={styles.sectionTitle}>Remarks</Text>
            <TextInput
              style={[styles.textInput, styles.textArea]}
              value={meetingConfig.remarks}
              onChangeText={(text) => updateMeetingConfig('remarks', text)}
              placeholder="Remarks"
              multiline
              numberOfLines={3}
            />
          </View>
        )}

        {/* Follow Up Configuration */}
        {showFollowUpConfig && (
          <View style={styles.meetingConfigContainer}>
            <Text style={styles.meetingConfigTitle}>Lead configuration</Text>

            {/* Reminder Section */}
            <Text style={styles.sectionTitle}>Reminder</Text>

            {/* Title */}
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Title</Text>
              <TextInput
                style={styles.textInput}
                value={followUpConfig.title}
                onChangeText={(text) => updateFollowUpConfig('title', text)}
                placeholder="Follow up"
              />
            </View>

            {/* Content */}
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Content</Text>
              <TextInput
                style={[styles.textInput, styles.textArea]}
                value={followUpConfig.content}
                onChangeText={(text) => updateFollowUpConfig('content', text)}
                placeholder="Follow up reminder."
                multiline
                numberOfLines={3}
              />
            </View>

            {/* Due Date */}
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Due Date</Text>
              <View style={styles.dateInputContainer}>
                <TextInput
                  style={[styles.textInput, styles.dateInput]}
                  value={followUpConfig.dueDate}
                  onChangeText={(text) => updateFollowUpConfig('dueDate', text)}
                  placeholder="dd.mm.yyyy, --:--"
                />
                <TouchableOpacity
                  style={styles.calendarButton}
                  onPress={() => {
                    setCurrentConfigType('followUp');
                    setShowDatePicker(true);
                  }}
                >
                  <Calendar size={20} color="#6B7280" />
                </TouchableOpacity>
              </View>
              {/* Inline Date Picker for Follow Up */}
              {showDatePicker && currentConfigType === 'followUp' && (
                <View style={styles.inlineDatePicker}>
                  <DateTimePicker
                    value={new Date()}
                    mode="date"
                    display="default"
                    onChange={(event, selectedDate) => {
                      setShowDatePicker(false);
                      if (selectedDate) {
                        const dateString = selectedDate.toLocaleDateString();
                        updateFollowUpConfig('dueDate', dateString);
                      }
                    }}
                  />
                </View>
              )}
              {!followUpConfig.dueDate && (
                <Text style={styles.errorText}>Due Date is required.</Text>
              )}
            </View>

            {/* Priority */}
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Priority</Text>
              <TouchableOpacity style={styles.dropdownButton}>
                <Text style={styles.dropdownText}>{followUpConfig.priority}</Text>
                <Text style={styles.dropdownArrow}>▼</Text>
              </TouchableOpacity>
            </View>

            {/* Send Email */}
            <View style={styles.checkboxContainer}>
              <Switch
                value={followUpConfig.sendEmail}
                onValueChange={(value) => updateFollowUpConfig('sendEmail', value)}
                trackColor={{ false: '#E5E7EB', true: '#B89C4C' }}
                thumbColor={followUpConfig.sendEmail ? '#fff' : '#fff'}
              />
              <Text style={styles.checkboxLabel}>Send Email</Text>
            </View>

            {/* Remarks Section */}
            <Text style={styles.sectionTitle}>Remarks</Text>
            <TextInput
              style={[styles.textInput, styles.textArea]}
              value={followUpConfig.remarks}
              onChangeText={(text) => updateFollowUpConfig('remarks', text)}
              placeholder="Remarks"
              multiline
              numberOfLines={3}
            />
          </View>
        )}

        {/* Viewing Configuration */}
        {showViewingConfig && (
          <View style={styles.meetingConfigContainer}>
            <Text style={styles.meetingConfigTitle}>Lead configuration</Text>

            {/* Property Search Section */}
            <Text style={styles.sectionTitle}>Property Search</Text>

            {/* First Row: Location and Rent/Sale */}
            <View style={styles.row}>
              <View style={styles.halfWidth}>
                <Text style={styles.inputLabel}>Location</Text>
                <Dropdown
                  options={locationOptions}
                  selectedValue={viewingConfig.search}
                  onSelect={(value) => updateViewingConfig('search', value)}
                  placeholder="Select location"
                />
              </View>
              <View style={styles.halfWidth}>
                <Text style={styles.inputLabel}>Rent/Sale</Text>
                <Dropdown
                  options={RENT_SALE_OPTIONS}
                  selectedValue={viewingConfig.rentSale}
                  onSelect={(value) => updateViewingConfig('rentSale', value)}
                  placeholder="Select type"
                />
              </View>
            </View>

            {/* Second Row: Tower/Building and Bedrooms */}
            <View style={styles.row}>
              <View style={styles.halfWidth}>
                <Text style={styles.inputLabel}>Tower/Building</Text>
                <Dropdown
                  options={towerOptions}
                  selectedValue={viewingConfig.towerBuilding}
                  onSelect={(value) => updateViewingConfig('towerBuilding', value)}
                  placeholder="Select tower"
                />
              </View>
              <View style={styles.halfWidth}>
                <Text style={styles.inputLabel}>Bedrooms</Text>
                <MultiSelectDropdown
                  options={bedroomOptions}
                  selectedValues={viewingConfig.bedrooms}
                  onSelectionChange={(values) => updateViewingConfig('bedrooms', values)}
                  placeholder="Select bedrooms"
                />
              </View>
            </View>

            {/* Third Row: Min Area and Max Area */}
            <View style={styles.row}>
              <View style={styles.halfWidth}>
                <Text style={styles.inputLabel}>Min Area</Text>
                <Dropdown
                  options={MIN_AREA_OPTIONS}
                  selectedValue={viewingConfig.minArea}
                  onSelect={(value) => updateViewingConfig('minArea', value)}
                  placeholder="Min area"
                />
              </View>
              <View style={styles.halfWidth}>
                <Text style={styles.inputLabel}>Max Area</Text>
                <Dropdown
                  options={MAX_AREA_OPTIONS}
                  selectedValue={viewingConfig.maxArea}
                  onSelect={(value) => updateViewingConfig('maxArea', value)}
                  placeholder="Max area"
                />
              </View>
            </View>

            {/* Fourth Row: Min Price and Max Price */}
            <View style={styles.row}>
              <View style={styles.halfWidth}>
                <Text style={styles.inputLabel}>Min Price</Text>
                <Dropdown
                  options={PRICE_MIN_OPTIONS}
                  selectedValue={viewingConfig.priceMin}
                  onSelect={(value) => updateViewingConfig('priceMin', value)}
                  placeholder="Min price"
                />
              </View>
              <View style={styles.halfWidth}>
                <Text style={styles.inputLabel}>Max Price</Text>
                <Dropdown
                  options={PRICE_MAX_OPTIONS}
                  selectedValue={viewingConfig.priceMax}
                  onSelect={(value) => updateViewingConfig('priceMax', value)}
                  placeholder="Max price"
                />
              </View>
            </View>

            {/* Reset Filters Button */}
            <TouchableOpacity style={styles.resetButton} onPress={resetViewingFilters}>
              <Text style={styles.resetButtonText}>Reset Filters</Text>
            </TouchableOpacity>

            {/* Properties List */}
            {hasFilters && (
              <View style={styles.propertiesSection}>
                <Text style={styles.sectionTitle}>Properties</Text>
                {isLoadingListings ? (
                  <Text style={styles.loadingText}>Loading properties...</Text>
                ) : listingsData?.data?.length > 0 ? (
                  <>
                    <FlatList
                      data={listingsData.data}
                      renderItem={renderListingItem}
                      keyExtractor={(item) => item.id.toString()}
                      getItemLayout={getItemLayout}
                      style={styles.listingsList}
                      showsVerticalScrollIndicator={false}
                      removeClippedSubviews={true}
                      maxToRenderPerBatch={10}
                      windowSize={10}
                    />

                    {/* Pagination */}
                    <Pagination
                      currentPage={currentViewingPage}
                      totalPages={Math.ceil((listingsData.total || 0) / ITEMS_PER_PAGE)}
                      onPageChange={handleViewingPageChange}
                      totalItems={listingsData.total || 0}
                      itemsPerPage={ITEMS_PER_PAGE}
                    />

                    {/* Selected Properties Count */}
                    {viewingConfig.selectedProperties.length > 0 && (
                      <Text style={styles.selectedCount}>
                        {viewingConfig.selectedProperties.length} properties selected
                      </Text>
                    )}
                  </>
                ) : (
                  <Text style={styles.noPropertiesText}>No properties found with current filters</Text>
                )}
              </View>
            )}
          </View>
        )}


      </ScrollView>

      {/* Action Buttons */}
      <View style={styles.buttonContainer}>
        <TouchableOpacity style={styles.cancelButton} onPress={() => router.back()}>
          <Text style={styles.cancelButtonText}>Cancel</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles.saveButton,
            !tempSelectedStatus && styles.saveButtonDisabled
          ]}
          onPress={handleSave}
          disabled={!tempSelectedStatus}
        >
          <Text style={[
            styles.saveButtonText,
            !tempSelectedStatus && styles.saveButtonTextDisabled
          ]}>
            Save
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    paddingTop: 60,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: {
    padding: 4,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
  },
  placeholder: {
    width: 32, // Same width as back button for centering
  },
  content: {
    flex: 1,
  },
  statusScrollContainer: {
    marginVertical: 20,
  },
  statusScrollContent: {
    paddingHorizontal: 16,
    gap: 12,
  },
  statusPill: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
  },
  selectedStatusPill: {
    backgroundColor: '#B89C4C',
  },
  statusPillText: {
    fontSize: 14,
    color: '#4B5563',
    fontWeight: '500',
    textTransform: 'capitalize',
  },
  selectedStatusPillText: {
    color: '#fff',
  },
  buttonContainer: {
    flexDirection: 'row',
    padding: 16,
    gap: 12,
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 24,
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 16,
    color: '#6B7280',
    fontWeight: '500',
  },
  saveButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 24,
    backgroundColor: '#B89C4C',
    borderRadius: 8,
    alignItems: 'center',
  },
  saveButtonDisabled: {
    backgroundColor: '#E5E7EB',
  },
  saveButtonText: {
    fontSize: 16,
    color: 'white',
    fontWeight: '600',
  },
  saveButtonTextDisabled: {
    color: '#9CA3AF',
  },
  // Meeting Configuration Styles
  meetingConfigContainer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    maxHeight: 400,
  },
  meetingConfigTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 12,
    marginTop: 8,
  },
  inputContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 6,
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 14,
    backgroundColor: '#fff',
  },
  textArea: {
    minHeight: 80,
    textAlignVertical: 'top',
  },
  dateInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dateInput: {
    flex: 1,
    marginRight: 8,
  },
  calendarButton: {
    padding: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    backgroundColor: '#fff',
  },
  inlineDatePicker: {
    marginTop: 8,
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    padding: 8,
  },
  errorText: {
    fontSize: 12,
    color: '#EF4444',
    marginTop: 4,
  },
  dropdownButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    backgroundColor: '#fff',
  },
  dropdownText: {
    fontSize: 14,
    color: '#374151',
  },
  dropdownArrow: {
    fontSize: 12,
    color: '#6B7280',
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  checkboxLabel: {
    fontSize: 14,
    color: '#374151',
    fontWeight: '500',
  },
  // Row layout styles
  row: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 16,
  },
  halfWidth: {
    flex: 1,
  },
  // Reset button styles
  resetButton: {
    backgroundColor: '#F3F4F6',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 8,
    marginBottom: 16,
  },
  resetButtonText: {
    fontSize: 14,
    color: '#6B7280',
    fontWeight: '500',
  },
  // Properties section styles
  propertiesSection: {
    marginTop: 16,
  },
  loadingText: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
    paddingVertical: 20,
  },
  noPropertiesText: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
    paddingVertical: 20,
  },
  listingsList: {
    maxHeight: 300,
    marginBottom: 16,
  },
  selectedCount: {
    fontSize: 14,
    color: '#B89C4C',
    fontWeight: '600',
    textAlign: 'center',
    marginTop: 8,
  },
  // Listing item styles
  listingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 8,
  },
  listingCheckboxContainer: {
    marginRight: 12,
  },
  listingCheckbox: {
    width: 20,
    height: 20,
    borderWidth: 2,
    borderColor: '#E5E7EB',
    borderRadius: 4,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#fff',
  },
  listingCheckboxSelected: {
    backgroundColor: '#B89C4C',
    borderColor: '#B89C4C',
  },
  listingCheckmark: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  listingCardContainer: {
    flex: 1,
  },
});

export default EditStatusScreen;
