import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Switch,
  FlatList,
} from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { ArrowLeft, Calendar } from 'lucide-react-native';
import DateTimePicker from '@react-native-community/datetimepicker';
import {
  RENT_SALE_OPTIONS,
  MIN_AREA_OPTIONS,
  MAX_AREA_OPTIONS,
  PRICE_MIN_OPTIONS,
  PRICE_MAX_OPTIONS,
  transformApiDataToDropdownOptions
} from '@/constants/dropdownOptions';
import { useQuery } from '@tanstack/react-query';
import {
  fetchGeographies,
  fetchBedrooms,
  fetchListingTypes,
} from '@/lib/api';
import { api } from '@/lib/api';

interface StatusOption {
  id: number;
  name: string;
  background_color: string;
  is_disabled: number;
}

interface MeetingConfig {
  title: string;
  content: string;
  dueDate: string;
  priority: string;
  sendEmail: boolean;
  remarks: string;
}

interface FollowUpConfig {
  title: string;
  content: string;
  dueDate: string;
  priority: string;
  sendEmail: boolean;
  remarks: string;
}

interface ViewingConfig {
  search: string;
  rentSale: string;
  towerBuilding: string;
  bedrooms: string[];
  minArea: string;
  maxArea: string;
  priceMin: string;
  priceMax: string;
  selectedProperties: string[];
}

export default function EditStatusScreen() {
  const router = useRouter();
  const params = useLocalSearchParams();
  const leadId = params.id as string;
  const currentStatusId = params.statusId as string;
  const statuses = JSON.parse(decodeURIComponent(params.statuses as string)) as StatusOption[];

  console.log('📊 EditStatusScreen loaded with:');
  console.log('- Lead ID:', leadId);
  console.log('- Current Status ID:', currentStatusId);
  console.log('- Statuses count:', statuses.length);
  console.log('- Statuses:', statuses.map(s => `${s.id}: ${s.name}`));

  // Find current status
  const currentStatus = statuses.find(s => s.id.toString() === currentStatusId);
  console.log('- Current Status Object:', currentStatus);

  const [tempSelectedStatus, setTempSelectedStatus] = useState(currentStatusId);
  const [showMeetingConfig, setShowMeetingConfig] = useState(false);
  const [showFollowUpConfig, setShowFollowUpConfig] = useState(false);
  const [showViewingConfig, setShowViewingConfig] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [currentConfigType, setCurrentConfigType] = useState<'meeting' | 'followUp'>('meeting');

  const [meetingConfig, setMeetingConfig] = useState<MeetingConfig>({
    title: 'Meeting appointment',
    content: 'Meeting appointment reminder.',
    dueDate: '',
    priority: 'Low',
    sendEmail: false,
    remarks: '',
  });

  const [followUpConfig, setFollowUpConfig] = useState<FollowUpConfig>({
    title: 'Follow up',
    content: 'Follow up reminder.',
    dueDate: '',
    priority: 'Low',
    sendEmail: false,
    remarks: '',
  });

  const [viewingConfig, setViewingConfig] = useState<ViewingConfig>({
    search: '',
    rentSale: '',
    towerBuilding: '',
    bedrooms: [],
    minArea: '',
    maxArea: '',
    priceMin: '',
    priceMax: '',
    selectedProperties: [],
  });

  // Listings state
  const [listings, setListings] = useState<any[]>([]);
  const [isLoadingListings, setIsLoadingListings] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalListings, setTotalListings] = useState(0);
  const ITEMS_PER_PAGE = 10;

  const enabledStatuses = statuses.filter(status => status.is_disabled === 0);

  // Fetch dropdown data from API
  const { data: geographies = [] } = useQuery({
    queryKey: ['geographies'],
    queryFn: fetchGeographies,
  });

  const { data: bedrooms = [] } = useQuery({
    queryKey: ['bedrooms'],
    queryFn: fetchBedrooms,
  });

  const { data: listingTypes = [] } = useQuery({
    queryKey: ['listingTypes'],
    queryFn: fetchListingTypes,
  });

  // Log data when loaded
  useEffect(() => {
    if (geographies.length > 0) {
      console.log('� Geographies loaded:', geographies.length, 'items');
    }
  }, [geographies]);

  useEffect(() => {
    if (bedrooms.length > 0) {
      console.log('🛏️ Bedrooms loaded:', bedrooms.length, 'items');
    }
  }, [bedrooms]);

  // Get selected location object for towers query
  const selectedLocation = geographies.find((geo: any) => geo.id?.toString() === viewingConfig.search);

  const { data: towers = [{ id: 'any', name: 'Any' }] } = useQuery({
    queryKey: ['towers', selectedLocation?.id],
    queryFn: async () => {
      if (!selectedLocation?.id) {
        return [{ id: 'any', name: 'Any' }];
      }
      try {
        const { data } = await api.get(`/geography/${selectedLocation.id}/towers`);
        return [{ id: 'any', name: 'Any' }, ...data];
      } catch (error) {
        console.error('Error fetching towers:', error);
        return [{ id: 'any', name: 'Any' }];
      }
    },
    enabled: true, // Always enabled, but returns default when no location
  });

  // Transform API data to dropdown format
  const locationOptions = transformApiDataToDropdownOptions(geographies);
  const bedroomOptions = transformApiDataToDropdownOptions(bedrooms);
  const towerOptions = transformApiDataToDropdownOptions(towers);

  useEffect(() => {
    setTempSelectedStatus(currentStatusId);
  }, [currentStatusId]);

  const formatStatusName = (name: string) => {
    return name.replace(/_/g, ' ');
  };

  const handleStatusToggle = (statusId: string) => {
    // Always select the new status (no deselection)
    setTempSelectedStatus(statusId);

    // Check if MEETING_SCHEDULED, VIEWING_SCHEDULED, or FOLLOW_UP is selected
    const selectedStatusObj = statuses.find(s => s.id.toString() === statusId);
    console.log('🔍 Selected status:', selectedStatusObj?.name, 'ID:', statusId);

    if (selectedStatusObj && selectedStatusObj.name === 'MEETING_SCHEDULED') {
      console.log('📅 Showing meeting config');
      setShowMeetingConfig(true);
      setShowFollowUpConfig(false);
      setShowViewingConfig(false);
    } else if (selectedStatusObj && selectedStatusObj.name === 'VIEWING_SCHEDULED') {
      console.log('🏠 Showing viewing config');
      setShowViewingConfig(true);
      setShowMeetingConfig(false);
      setShowFollowUpConfig(false);
    } else if (selectedStatusObj && selectedStatusObj.name === 'FOLLOW_UP') {
      console.log('📞 Showing follow-up config');
      setShowFollowUpConfig(true);
      setShowMeetingConfig(false);
      setShowViewingConfig(false);
    } else {
      console.log('❌ No special config needed');
      setShowMeetingConfig(false);
      setShowFollowUpConfig(false);
      setShowViewingConfig(false);
    }
  };

  const handleSave = () => {
    // TODO: Implement save logic
    console.log('Saving status:', tempSelectedStatus);
    router.back();
  };

  const updateMeetingConfig = (field: keyof MeetingConfig, value: string | boolean) => {
    setMeetingConfig(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const updateFollowUpConfig = (field: keyof FollowUpConfig, value: string | boolean) => {
    setFollowUpConfig(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const updateViewingConfig = (field: keyof ViewingConfig, value: string | string[]) => {
    setViewingConfig(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Helper functions for pill selection
  const toggleSingleSelection = (field: keyof ViewingConfig, value: string) => {
    setViewingConfig(prev => ({
      ...prev,
      [field]: prev[field] === value ? '' : value
    }));
  };

  const toggleMultipleSelection = (field: keyof ViewingConfig, value: string) => {
    setViewingConfig(prev => {
      const currentValues = Array.isArray(prev[field]) ? prev[field] as string[] : [];
      const newValues = currentValues.includes(value)
        ? currentValues.filter(v => v !== value)
        : [...currentValues, value];
      return {
        ...prev,
        [field]: newValues
      };
    });
  };

  const resetViewingFilters = () => {
    console.log('🔄 Resetting viewing filters...');
    setViewingConfig({
      search: '',
      rentSale: '',
      towerBuilding: '',
      bedrooms: [],
      minArea: '',
      maxArea: '',
      priceMin: '',
      priceMax: '',
      selectedProperties: [],
    });
  };

  // Check if any filters are applied
  const hasFilters = viewingConfig?.search || viewingConfig?.rentSale || viewingConfig?.towerBuilding ||
                    (viewingConfig?.bedrooms && viewingConfig.bedrooms.length > 0) || viewingConfig?.minArea ||
                    viewingConfig?.maxArea || viewingConfig?.priceMin || viewingConfig?.priceMax;

  // Function to load listings based on filters
  const loadListings = async (page = 1) => {
    if (!hasFilters) return;

    setIsLoadingListings(true);
    try {
      const filters: any = {
        page,
        per_page: ITEMS_PER_PAGE,
      };

      // Map filters to API parameters
      if (viewingConfig.search) {
        // Find the selected location object to get the proper location ID
        const selectedLocationObj = geographies.find((geo: any) => geo.id?.toString() === viewingConfig.search);
        if (selectedLocationObj) {
          filters.location = selectedLocationObj.id;
        }
      }

      if (viewingConfig.rentSale) {
        // Map rent/sale to adType
        const rentSaleMap: any = {
          'rent': 'rent',
          'sale': 'sale',
          'rent_sale': 'All'
        };
        filters.adType = rentSaleMap[viewingConfig.rentSale] || 'All';
      }

      if (viewingConfig.towerBuilding && viewingConfig.towerBuilding !== 'any') {
        filters.tower = viewingConfig.towerBuilding;
      }

      if (viewingConfig.bedrooms.length > 0) {
        // Map bedroom IDs to the format expected by API
        filters.bedrooms = viewingConfig.bedrooms.join(',');
      }

      console.log('🔍 Loading listings with filters:', filters);

      // Call real API
      const response = await api.get('/listings', { params: filters });
      const listingsData = response.data;

      setListings(listingsData.data || []);
      setTotalListings(listingsData.total || 0);
      setTotalPages(Math.ceil((listingsData.total || 0) / ITEMS_PER_PAGE));
      setCurrentPage(page);

    } catch (error) {
      console.error('Error loading listings:', error);
      // Fallback to empty state on error
      setListings([]);
      setTotalListings(0);
      setTotalPages(1);
    } finally {
      setIsLoadingListings(false);
    }
  };

  // Load listings when filters change
  useEffect(() => {
    if (hasFilters) {
      loadListings(1);
    } else {
      setListings([]);
      setTotalListings(0);
    }
  }, [viewingConfig.search, viewingConfig.rentSale, viewingConfig.towerBuilding,
      viewingConfig.bedrooms, viewingConfig.minArea, viewingConfig.maxArea,
      viewingConfig.priceMin, viewingConfig.priceMax]);

  // Function to render listing item
  const renderListingItem = ({ item }: { item: any }) => {
    // Format price
    const formatPrice = (price: number) => {
      return new Intl.NumberFormat('en-QA', {
        style: 'currency',
        currency: 'QAR',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
      }).format(price);
    };

    // Format area
    const formatArea = (area: number) => {
      return `${area} sqm`;
    };

    // Get bedroom info
    const bedroomInfo = item.bedroom_id ?
      bedrooms.find((b: any) => b.id === item.bedroom_id)?.name || `${item.bedroom_id} BR` :
      'Studio';

    return (
      <TouchableOpacity
        style={[
          styles.listingCard,
          viewingConfig.selectedProperties.includes(item.id.toString()) && styles.selectedListingCard
        ]}
        onPress={() => togglePropertySelection(item.id.toString())}
      >
        <View style={styles.listingContent}>
          <Text style={styles.listingTitle} numberOfLines={2}>
            {item.title || `${item.listing_type?.name || 'Property'} in ${item.location?.name || 'Location'}`}
          </Text>
          <View style={styles.listingDetails}>
            <Text style={styles.listingPrice}>
              {item.price ? formatPrice(item.price) : 'Price on request'}
            </Text>
            <Text style={styles.listingInfo}>
              {bedroomInfo} • {item.area ? formatArea(item.area) : 'Area N/A'}
            </Text>
          </View>
        </View>
        {viewingConfig.selectedProperties.includes(item.id.toString()) && (
          <View style={styles.selectedIndicator}>
            <Text style={styles.selectedIndicatorText}>✓</Text>
          </View>
        )}
      </TouchableOpacity>
    );
  };

  // Function to toggle property selection
  const togglePropertySelection = (propertyId: string) => {
    setViewingConfig(prev => {
      const currentSelected = prev.selectedProperties;
      const newSelected = currentSelected.includes(propertyId)
        ? currentSelected.filter(id => id !== propertyId)
        : [...currentSelected, propertyId];

      return {
        ...prev,
        selectedProperties: newSelected
      };
    });
  };

  // Initialize configuration based on current status
  useEffect(() => {
    console.log('🚀 Initializing with current status:', currentStatusId);
    handleStatusToggle(currentStatusId);
  }, []);

  // Debug useEffect
  useEffect(() => {
    console.log('🔄 State changes:');
    console.log('- showViewingConfig:', showViewingConfig);
    console.log('- showMeetingConfig:', showMeetingConfig);
    console.log('- showFollowUpConfig:', showFollowUpConfig);
    console.log('- tempSelectedStatus:', tempSelectedStatus);
  }, [showViewingConfig, showMeetingConfig, showFollowUpConfig, tempSelectedStatus]);

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <ArrowLeft size={24} color="#000" />
        </TouchableOpacity>
        <Text style={styles.title}>Select Status</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Status Pills */}
        <ScrollView
          horizontal
          style={styles.statusScrollContainer}
          contentContainerStyle={styles.statusScrollContent}
          showsHorizontalScrollIndicator={false}
        >
          {enabledStatuses.map((status) => (
            <TouchableOpacity
              key={status.id}
              style={[
                styles.statusPill,
                tempSelectedStatus === status.id.toString() && styles.selectedStatusPill
              ]}
              onPress={() => handleStatusToggle(status.id.toString())}
            >
              <Text
                style={[
                  styles.statusPillText,
                  tempSelectedStatus === status.id.toString() && styles.selectedStatusPillText
                ]}
              >
                {formatStatusName(status.name)}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>

        {/* Meeting Configuration */}
        {showMeetingConfig && (
          <View style={styles.meetingConfigContainer}>
            <Text style={styles.meetingConfigTitle}>Lead configuration</Text>

            {/* Reminder Section */}
            <Text style={styles.sectionTitle}>Reminder</Text>

            {/* Title */}
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Title</Text>
              <TextInput
                style={styles.textInput}
                value={meetingConfig.title}
                onChangeText={(text) => updateMeetingConfig('title', text)}
                placeholder="Meeting appointment"
              />
            </View>

            {/* Content */}
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Content</Text>
              <TextInput
                style={[styles.textInput, styles.textArea]}
                value={meetingConfig.content}
                onChangeText={(text) => updateMeetingConfig('content', text)}
                placeholder="Meeting appointment reminder."
                multiline
                numberOfLines={3}
              />
            </View>

            {/* Due Date */}
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Due Date</Text>
              <View style={styles.dateInputContainer}>
                <TextInput
                  style={[styles.textInput, styles.dateInput]}
                  value={meetingConfig.dueDate}
                  onChangeText={(text) => updateMeetingConfig('dueDate', text)}
                  placeholder="dd.mm.yyyy, --:--"
                />
                <TouchableOpacity
                  style={styles.calendarButton}
                  onPress={() => {
                    setCurrentConfigType('meeting');
                    setShowDatePicker(true);
                  }}
                >
                  <Calendar size={20} color="#6B7280" />
                </TouchableOpacity>
              </View>
              {/* Inline Date Picker for Meeting */}
              {showDatePicker && currentConfigType === 'meeting' && (
                <View style={styles.inlineDatePicker}>
                  <DateTimePicker
                    value={new Date()}
                    mode="date"
                    display="default"
                    onChange={(event, selectedDate) => {
                      setShowDatePicker(false);
                      if (selectedDate) {
                        const dateString = selectedDate.toLocaleDateString();
                        updateMeetingConfig('dueDate', dateString);
                      }
                    }}
                  />
                </View>
              )}
              {!meetingConfig.dueDate && (
                <Text style={styles.errorText}>Due Date is required.</Text>
              )}
            </View>

            {/* Priority */}
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Priority</Text>
              <TouchableOpacity style={styles.dropdownButton}>
                <Text style={styles.dropdownText}>{meetingConfig.priority}</Text>
                <Text style={styles.dropdownArrow}>▼</Text>
              </TouchableOpacity>
            </View>

            {/* Send Email */}
            <View style={styles.checkboxContainer}>
              <Switch
                value={meetingConfig.sendEmail}
                onValueChange={(value) => updateMeetingConfig('sendEmail', value)}
                trackColor={{ false: '#E5E7EB', true: '#B89C4C' }}
                thumbColor={meetingConfig.sendEmail ? '#fff' : '#fff'}
              />
              <Text style={styles.checkboxLabel}>Send Email</Text>
            </View>

            {/* Remarks Section */}
            <Text style={styles.sectionTitle}>Remarks</Text>
            <TextInput
              style={[styles.textInput, styles.textArea]}
              value={meetingConfig.remarks}
              onChangeText={(text) => updateMeetingConfig('remarks', text)}
              placeholder="Remarks"
              multiline
              numberOfLines={3}
            />
          </View>
        )}

        {/* Follow Up Configuration */}
        {showFollowUpConfig && (
          <View style={styles.meetingConfigContainer}>
            <Text style={styles.meetingConfigTitle}>Lead configuration</Text>

            {/* Reminder Section */}
            <Text style={styles.sectionTitle}>Reminder</Text>

            {/* Title */}
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Title</Text>
              <TextInput
                style={styles.textInput}
                value={followUpConfig.title}
                onChangeText={(text) => updateFollowUpConfig('title', text)}
                placeholder="Follow up"
              />
            </View>

            {/* Content */}
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Content</Text>
              <TextInput
                style={[styles.textInput, styles.textArea]}
                value={followUpConfig.content}
                onChangeText={(text) => updateFollowUpConfig('content', text)}
                placeholder="Follow up reminder."
                multiline
                numberOfLines={3}
              />
            </View>

            {/* Due Date */}
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Due Date</Text>
              <View style={styles.dateInputContainer}>
                <TextInput
                  style={[styles.textInput, styles.dateInput]}
                  value={followUpConfig.dueDate}
                  onChangeText={(text) => updateFollowUpConfig('dueDate', text)}
                  placeholder="dd.mm.yyyy, --:--"
                />
                <TouchableOpacity
                  style={styles.calendarButton}
                  onPress={() => {
                    setCurrentConfigType('followUp');
                    setShowDatePicker(true);
                  }}
                >
                  <Calendar size={20} color="#6B7280" />
                </TouchableOpacity>
              </View>
              {/* Inline Date Picker for Follow Up */}
              {showDatePicker && currentConfigType === 'followUp' && (
                <View style={styles.inlineDatePicker}>
                  <DateTimePicker
                    value={new Date()}
                    mode="date"
                    display="default"
                    onChange={(event, selectedDate) => {
                      setShowDatePicker(false);
                      if (selectedDate) {
                        const dateString = selectedDate.toLocaleDateString();
                        updateFollowUpConfig('dueDate', dateString);
                      }
                    }}
                  />
                </View>
              )}
              {!followUpConfig.dueDate && (
                <Text style={styles.errorText}>Due Date is required.</Text>
              )}
            </View>

            {/* Priority */}
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Priority</Text>
              <TouchableOpacity style={styles.dropdownButton}>
                <Text style={styles.dropdownText}>{followUpConfig.priority}</Text>
                <Text style={styles.dropdownArrow}>▼</Text>
              </TouchableOpacity>
            </View>

            {/* Send Email */}
            <View style={styles.checkboxContainer}>
              <Switch
                value={followUpConfig.sendEmail}
                onValueChange={(value) => updateFollowUpConfig('sendEmail', value)}
                trackColor={{ false: '#E5E7EB', true: '#B89C4C' }}
                thumbColor={followUpConfig.sendEmail ? '#fff' : '#fff'}
              />
              <Text style={styles.checkboxLabel}>Send Email</Text>
            </View>

            {/* Remarks Section */}
            <Text style={styles.sectionTitle}>Remarks</Text>
            <TextInput
              style={[styles.textInput, styles.textArea]}
              value={followUpConfig.remarks}
              onChangeText={(text) => updateFollowUpConfig('remarks', text)}
              placeholder="Remarks"
              multiline
              numberOfLines={3}
            />
          </View>
        )}

        {/* Viewing Configuration */}
        {showViewingConfig && (
          <View style={styles.meetingConfigContainer}>
            <Text style={styles.meetingConfigTitle}>Lead configuration</Text>

            {/* Property Search Section */}
            <Text style={styles.sectionTitle}>Property Search</Text>

            {/* Location Pills */}
            <Text style={styles.inputLabel}>Location</Text>
            <ScrollView
              horizontal
              style={styles.pillScrollContainer}
              contentContainerStyle={styles.pillScrollContent}
              showsHorizontalScrollIndicator={false}
            >
              {locationOptions.map((option) => (
                <TouchableOpacity
                  key={option.id}
                  style={[
                    styles.filterPill,
                    viewingConfig.search === option.id && styles.selectedFilterPill
                  ]}
                  onPress={() => toggleSingleSelection('search', option.id)}
                >
                  <Text
                    style={[
                      styles.filterPillText,
                      viewingConfig.search === option.id && styles.selectedFilterPillText
                    ]}
                  >
                    {option.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>

            {/* Rent/Sale Pills */}
            <Text style={styles.inputLabel}>Rent/Sale</Text>
            <ScrollView
              horizontal
              style={styles.pillScrollContainer}
              contentContainerStyle={styles.pillScrollContent}
              showsHorizontalScrollIndicator={false}
            >
              {RENT_SALE_OPTIONS.map((option) => (
                <TouchableOpacity
                  key={option.id}
                  style={[
                    styles.filterPill,
                    viewingConfig.rentSale === option.id && styles.selectedFilterPill
                  ]}
                  onPress={() => toggleSingleSelection('rentSale', option.id)}
                >
                  <Text
                    style={[
                      styles.filterPillText,
                      viewingConfig.rentSale === option.id && styles.selectedFilterPillText
                    ]}
                  >
                    {option.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>

            {/* Tower/Building Pills */}
            <Text style={styles.inputLabel}>Tower/Building</Text>
            <ScrollView
              horizontal
              style={styles.pillScrollContainer}
              contentContainerStyle={styles.pillScrollContent}
              showsHorizontalScrollIndicator={false}
            >
              {towerOptions.map((option) => (
                <TouchableOpacity
                  key={option.id}
                  style={[
                    styles.filterPill,
                    viewingConfig.towerBuilding === option.id && styles.selectedFilterPill
                  ]}
                  onPress={() => toggleSingleSelection('towerBuilding', option.id)}
                >
                  <Text
                    style={[
                      styles.filterPillText,
                      viewingConfig.towerBuilding === option.id && styles.selectedFilterPillText
                    ]}
                  >
                    {option.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>

            {/* Bedrooms Pills (Multiple Selection) */}
            <Text style={styles.inputLabel}>Bedrooms</Text>
            <ScrollView
              horizontal
              style={styles.pillScrollContainer}
              contentContainerStyle={styles.pillScrollContent}
              showsHorizontalScrollIndicator={false}
            >
              {bedroomOptions.map((option) => (
                <TouchableOpacity
                  key={option.id}
                  style={[
                    styles.filterPill,
                    viewingConfig.bedrooms.includes(option.id) && styles.selectedFilterPill
                  ]}
                  onPress={() => toggleMultipleSelection('bedrooms', option.id)}
                >
                  <Text
                    style={[
                      styles.filterPillText,
                      viewingConfig.bedrooms.includes(option.id) && styles.selectedFilterPillText
                    ]}
                  >
                    {option.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>

            {/* Min Area Pills */}
            <Text style={styles.inputLabel}>Min Area</Text>
            <ScrollView
              horizontal
              style={styles.pillScrollContainer}
              contentContainerStyle={styles.pillScrollContent}
              showsHorizontalScrollIndicator={false}
            >
              {MIN_AREA_OPTIONS.map((option) => (
                <TouchableOpacity
                  key={option.id}
                  style={[
                    styles.filterPill,
                    viewingConfig.minArea === option.id && styles.selectedFilterPill
                  ]}
                  onPress={() => toggleSingleSelection('minArea', option.id)}
                >
                  <Text
                    style={[
                      styles.filterPillText,
                      viewingConfig.minArea === option.id && styles.selectedFilterPillText
                    ]}
                  >
                    {option.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>

            {/* Max Area Pills */}
            <Text style={styles.inputLabel}>Max Area</Text>
            <ScrollView
              horizontal
              style={styles.pillScrollContainer}
              contentContainerStyle={styles.pillScrollContent}
              showsHorizontalScrollIndicator={false}
            >
              {MAX_AREA_OPTIONS.map((option) => (
                <TouchableOpacity
                  key={option.id}
                  style={[
                    styles.filterPill,
                    viewingConfig.maxArea === option.id && styles.selectedFilterPill
                  ]}
                  onPress={() => toggleSingleSelection('maxArea', option.id)}
                >
                  <Text
                    style={[
                      styles.filterPillText,
                      viewingConfig.maxArea === option.id && styles.selectedFilterPillText
                    ]}
                  >
                    {option.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>

            {/* Min Price Pills */}
            <Text style={styles.inputLabel}>Min Price</Text>
            <ScrollView
              horizontal
              style={styles.pillScrollContainer}
              contentContainerStyle={styles.pillScrollContent}
              showsHorizontalScrollIndicator={false}
            >
              {PRICE_MIN_OPTIONS.map((option) => (
                <TouchableOpacity
                  key={option.id}
                  style={[
                    styles.filterPill,
                    viewingConfig.priceMin === option.id && styles.selectedFilterPill
                  ]}
                  onPress={() => toggleSingleSelection('priceMin', option.id)}
                >
                  <Text
                    style={[
                      styles.filterPillText,
                      viewingConfig.priceMin === option.id && styles.selectedFilterPillText
                    ]}
                  >
                    {option.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>

            {/* Max Price Pills */}
            <Text style={styles.inputLabel}>Max Price</Text>
            <ScrollView
              horizontal
              style={styles.pillScrollContainer}
              contentContainerStyle={styles.pillScrollContent}
              showsHorizontalScrollIndicator={false}
            >
              {PRICE_MAX_OPTIONS.map((option) => (
                <TouchableOpacity
                  key={option.id}
                  style={[
                    styles.filterPill,
                    viewingConfig.priceMax === option.id && styles.selectedFilterPill
                  ]}
                  onPress={() => toggleSingleSelection('priceMax', option.id)}
                >
                  <Text
                    style={[
                      styles.filterPillText,
                      viewingConfig.priceMax === option.id && styles.selectedFilterPillText
                    ]}
                  >
                    {option.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>

            {/* Reset Filters Button */}
            <TouchableOpacity style={styles.resetButton} onPress={resetViewingFilters}>
              <Text style={styles.resetButtonText}>Reset Filters</Text>
            </TouchableOpacity>

            {/* Properties List */}
            {hasFilters && (
              <View style={styles.propertiesSection}>
                <Text style={styles.sectionTitle}>Properties ({totalListings})</Text>

                {isLoadingListings ? (
                  <Text style={styles.loadingText}>Loading properties...</Text>
                ) : listings.length > 0 ? (
                  <>
                    <FlatList
                      data={listings}
                      renderItem={renderListingItem}
                      keyExtractor={(item) => item.id.toString()}
                      style={{ maxHeight: 300 }}
                      showsVerticalScrollIndicator={false}
                    />

                    {/* Selected Properties Count */}
                    {viewingConfig.selectedProperties.length > 0 && (
                      <Text style={styles.selectedCount}>
                        {viewingConfig.selectedProperties.length} properties selected
                      </Text>
                    )}
                  </>
                ) : (
                  <Text style={styles.noPropertiesText}>
                    No properties found with current filters
                  </Text>
                )}
              </View>
            )}
          </View>
        )}


      </ScrollView>

      {/* Action Buttons */}
      <View style={styles.buttonContainer}>
        <TouchableOpacity style={styles.cancelButton} onPress={() => router.back()}>
          <Text style={styles.cancelButtonText}>Cancel</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles.saveButton,
            !tempSelectedStatus && styles.saveButtonDisabled
          ]}
          onPress={handleSave}
          disabled={!tempSelectedStatus}
        >
          <Text style={[
            styles.saveButtonText,
            !tempSelectedStatus && styles.saveButtonTextDisabled
          ]}>
            Save
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    paddingTop: 60,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: {
    padding: 4,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
  },
  placeholder: {
    width: 32, // Same width as back button for centering
  },
  content: {
    flex: 1,
  },
  statusScrollContainer: {
    marginVertical: 20,
  },
  statusScrollContent: {
    paddingHorizontal: 16,
    gap: 12,
  },
  statusPill: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
  },
  selectedStatusPill: {
    backgroundColor: '#B89C4C',
  },
  statusPillText: {
    fontSize: 14,
    color: '#4B5563',
    fontWeight: '500',
    textTransform: 'capitalize',
  },
  selectedStatusPillText: {
    color: '#fff',
  },
  buttonContainer: {
    flexDirection: 'row',
    padding: 16,
    gap: 12,
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 24,
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 16,
    color: '#6B7280',
    fontWeight: '500',
  },
  saveButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 24,
    backgroundColor: '#B89C4C',
    borderRadius: 8,
    alignItems: 'center',
  },
  saveButtonDisabled: {
    backgroundColor: '#E5E7EB',
  },
  saveButtonText: {
    fontSize: 16,
    color: 'white',
    fontWeight: '600',
  },
  saveButtonTextDisabled: {
    color: '#9CA3AF',
  },
  // Meeting Configuration Styles
  meetingConfigContainer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    maxHeight: 400,
  },
  meetingConfigTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 12,
    marginTop: 8,
  },
  inputContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 6,
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 14,
    backgroundColor: '#fff',
  },
  textArea: {
    minHeight: 80,
    textAlignVertical: 'top',
  },
  dateInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dateInput: {
    flex: 1,
    marginRight: 8,
  },
  calendarButton: {
    padding: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    backgroundColor: '#fff',
  },
  inlineDatePicker: {
    marginTop: 8,
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    padding: 8,
  },
  errorText: {
    fontSize: 12,
    color: '#EF4444',
    marginTop: 4,
  },
  dropdownButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    backgroundColor: '#fff',
  },
  dropdownText: {
    fontSize: 14,
    color: '#374151',
  },
  dropdownArrow: {
    fontSize: 12,
    color: '#6B7280',
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  checkboxLabel: {
    fontSize: 14,
    color: '#374151',
    fontWeight: '500',
  },
  // Row layout styles
  row: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 16,
  },
  halfWidth: {
    flex: 1,
  },
  // Reset button styles
  resetButton: {
    backgroundColor: '#F3F4F6',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 8,
    marginBottom: 16,
  },
  resetButtonText: {
    fontSize: 14,
    color: '#6B7280',
    fontWeight: '500',
  },
  // Properties section styles
  propertiesSection: {
    marginTop: 16,
  },
  loadingText: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
    paddingVertical: 20,
  },
  noPropertiesText: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
    paddingVertical: 20,
  },
  listingsList: {
    maxHeight: 300,
    marginBottom: 16,
  },
  selectedCount: {
    fontSize: 14,
    color: '#B89C4C',
    fontWeight: '600',
    textAlign: 'center',
    marginTop: 8,
  },
  // Listing item styles
  listingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 8,
  },
  listingCheckboxContainer: {
    marginRight: 12,
  },
  listingCheckbox: {
    width: 20,
    height: 20,
    borderWidth: 2,
    borderColor: '#E5E7EB',
    borderRadius: 4,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#fff',
  },
  listingCheckboxSelected: {
    backgroundColor: '#B89C4C',
    borderColor: '#B89C4C',
  },
  listingCheckmark: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  listingCardContainer: {
    flex: 1,
  },
  pillScrollContainer: {
    marginVertical: 8,
  },
  pillScrollContent: {
    paddingHorizontal: 0,
    gap: 8,
  },
  filterPill: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  selectedFilterPill: {
    backgroundColor: '#B89C4C',
    borderColor: '#B89C4C',
  },
  filterPillText: {
    fontSize: 14,
    color: '#4B5563',
    fontWeight: '500',
  },
  selectedFilterPillText: {
    color: '#fff',
  },
  // Listing styles
  listingCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginVertical: 6,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  selectedListingCard: {
    borderColor: '#B89C4C',
    backgroundColor: '#FEF3C7',
  },
  listingContent: {
    flex: 1,
  },
  listingTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 4,
  },
  listingDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  listingPrice: {
    fontSize: 14,
    fontWeight: '600',
    color: '#B89C4C',
  },
  listingInfo: {
    fontSize: 14,
    color: '#6B7280',
  },
  selectedIndicator: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#B89C4C',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 12,
  },
  selectedIndicatorText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
});
