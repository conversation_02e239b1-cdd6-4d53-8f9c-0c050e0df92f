import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Stack, router, useLocalSearchParams } from 'expo-router';
import { CircleCheck as CheckCircle, ArrowRight } from 'lucide-react-native';
import Button from '@/components/Button';

export default function NextStep() {
    const { leadId } = useLocalSearchParams();

    const handleViewLead = () => {
        router.replace({
            pathname: '/(app)/(tabs)',
            params: { screen: 'leads' }
        });
        router.replace(`/leads/${leadId}`);
    };

    const handleCreateAnother = () => {
        router.replace({
            pathname: '/(app)/(tabs)',
            params: { screen: 'leads' }
        });
        router.replace('/leads/create');
    };

    return (
        <>
            <Stack.Screen
                options={{
                    headerShown: false,
                }}
            />

            <View style={styles.container}>
                <View style={styles.content}>
                    <View style={styles.iconContainer}>
                        <CheckCircle size={48} color="#10B981" />
                    </View>

                    <Text style={styles.title}>Lead Created Successfully!</Text>
                    <Text style={styles.subtitle}>Lead #{leadId} has been created</Text>

                    <View style={styles.actions}>
                        <Button
                            label="View Lead"
                            onPress={handleViewLead}
                            icon={<ArrowRight size={20} color="#fff" />}
                            fullWidth
                        />
                        <Button
                            label="Create Another Lead"
                            onPress={handleCreateAnother}
                            variant="secondary"
                            fullWidth
                        />
                    </View>
                </View>
            </View>
        </>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#F9FAFB',
        justifyContent: 'center',
        alignItems: 'center',
        padding: 20,
    },
    content: {
        alignItems: 'center',
        maxWidth: 400,
        width: '100%',
    },
    iconContainer: {
        width: 80,
        height: 80,
        borderRadius: 40,
        backgroundColor: '#D1FAE5',
        justifyContent: 'center',
        alignItems: 'center',
        marginBottom: 24,
    },
    title: {
        fontSize: 24,
        fontWeight: '600',
        color: '#111827',
        marginBottom: 8,
        textAlign: 'center',
    },
    subtitle: {
        fontSize: 16,
        color: '#6B7280',
        marginBottom: 32,
        textAlign: 'center',
    },
    actions: {
        width: '100%',
        gap: 12,
    },
});