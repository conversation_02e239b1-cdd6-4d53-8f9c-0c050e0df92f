import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { Stack, router, useLocalSearchParams } from 'expo-router';
import { ArrowLeft, User, Building2, Phone, Mail } from 'lucide-react-native';
import Button from '@/components/Button';
import AdTypeSelector from '@/components/AdTypeSelector';

type Contact = {
  id: number;
  name: string;
  email_1: string | null;
  prefix_mobile_1: string | null;
  mobile_1: string | null;
  company_name: string | null;
};

type AdType = 'rent' | 'sale';

export default function CreateLead() {
  const params = useLocalSearchParams();
  const [selectedContact, setSelectedContact] = useState<Contact | null>(null);
  const [selectedAdType, setSelectedAdType] = useState<AdType | null>(null);

  useEffect(() => {
    if (params.contact) {
      try {
        const contact = JSON.parse(params.contact as string);
        setSelectedContact(contact);
      } catch (error) {
        console.error('Error parsing contact:', error);
      }
    }
  }, [params.contact]);

  const isFormValid = selectedContact !== null && selectedAdType !== null;

  const handleNext = () => {
    if (!isFormValid) return;
    router.push({
      pathname: '/leads/create/details',
      params: {
        contact: JSON.stringify(selectedContact),
        adType: selectedAdType,
      }
    });
  };

  const openContactSelector = () => {
    router.push('/leads/create/select-contact');
  };

  return (
    <>
      <Stack.Screen
        options={{
          headerShown: true,
          header: () => (
            <View style={styles.header}>
              <Button
                variant="ghost"
                icon={<ArrowLeft size={24} color="#111827" />}
                onPress={() => router.back()}
              />
              <Text style={styles.headerTitle}>Create Lead</Text>
            </View>
          ),
        }}
      />

      <View style={styles.container}>
        <ScrollView style={styles.content}>
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Contact Information</Text>
            <Text style={styles.sectionDescription}>
              Select a contact to create a lead for
            </Text>

            {selectedContact ? (
              <View style={styles.selectedContact}>
                <View style={styles.contactInfo}>
                  <Text style={styles.contactName}>{selectedContact.name}</Text>

                  {selectedContact.company_name && (
                    <View style={styles.detailRow}>
                      <Building2 size={16} color="#6B7280" />
                      <Text style={styles.contactDetail}>
                        {selectedContact.company_name}
                      </Text>
                    </View>
                  )}

                  {selectedContact.mobile_1 && (
                    <View style={styles.detailRow}>
                      <Phone size={16} color="#6B7280" />
                      <Text style={styles.contactDetail}>
                        {selectedContact.prefix_mobile_1} {selectedContact.mobile_1}
                      </Text>
                    </View>
                  )}

                  {selectedContact.email_1 && (
                    <View style={styles.detailRow}>
                      <Mail size={16} color="#6B7280" />
                      <Text style={styles.contactDetail}>
                        {selectedContact.email_1}
                      </Text>
                    </View>
                  )}
                </View>
                <Button
                  variant="secondary"
                  label="Change"
                  onPress={openContactSelector}
                />
              </View>
            ) : (
              <Button
                label="Select Contact"
                icon={<User size={20} color="#fff" />}
                onPress={openContactSelector}
                fullWidth
              />
            )}
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Ad Type</Text>
            <Text style={styles.sectionDescription}>
              Select whether this lead is interested in renting or buying
            </Text>
            <AdTypeSelector
              selectedType={selectedAdType}
              onSelectType={setSelectedAdType}
            />
          </View>
        </ScrollView>

        <View style={styles.footer}>
          <Button
            label="Next"
            onPress={handleNext}
            disabled={!isFormValid}
            fullWidth
          />
        </View>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  content: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
    gap: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#111827',
  },
  section: {
    margin: 20,
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 16,
  },
  selectedContact: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  contactInfo: {
    flex: 1,
    marginRight: 16,
  },
  contactName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 12,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 8,
  },
  contactDetail: {
    fontSize: 14,
    color: '#6B7280',
  },
  footer: {
    padding: 20,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    backgroundColor: '#fff',
  },
});