import React, { useState } from 'react';
import { View, Text, StyleSheet, FlatList, RefreshControl, TouchableOpacity, ActivityIndicator } from 'react-native';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Bell, BellOff } from 'lucide-react-native';
import { Stack } from 'expo-router';
import { api } from '@/lib/api';
import { formatDistanceToNow } from '@/lib/date';
import NotificationFilters from '@/components/NotificationFilters';
import FloatingFilterButton from '@/components/FloatingFilterButton';
import FilterDialog from '@/components/FilterDialog';
import NotificationDetailsModal from '@/components/NotificationDetailsModal';
import { Notification } from '@/types/global';

interface NotificationsResponse {
  data: Notification[];
  count: number;
}

interface MarkAsReadResponse {
  message: string;
  data: {
    id: number;
    read_at: string;
  };
}

const ITEMS_PER_PAGE = 20;

function parseNotificationData(notification: Notification): Notification {
  if (!notification.payload) return notification;

  try {
    const parsedData = typeof notification.payload == "string" ? JSON.parse(notification.payload) : notification.payload;
    return {
      ...notification,
      parsedData,
    };
  } catch (error) {
    console.error('Error parsing notification data:', error);
    return notification;
  }
}

function NotificationSkeleton() {
  return (
    <View style={styles.skeletonContainer}>
      <View style={styles.skeletonDot} />
      <View style={styles.skeletonContent}>
        <View style={styles.skeletonTitle} />
        <View style={styles.skeletonText} />
        <View style={styles.skeletonDate} />
      </View>
    </View>
  );
}

export function NotificationsScreen() {
  const queryClient = useQueryClient();
  const [page, setPage] = useState(1);
  const [isFilterVisible, setIsFilterVisible] = useState(false);
  const [readStatus, setReadStatus] = useState<'all' | 'read' | 'unread'>('all');
  const [dateRange, setDateRange] = useState<'all' | 'today' | 'week' | 'month'>('all');
  const [markAsReadError, setMarkAsReadError] = useState<string | null>(null);
  const [selectedNotification, setSelectedNotification] = useState<Notification | null>(null);

  const { data, isLoading, error, refetch, isFetching } = useQuery({
    queryKey: ['notifications', page, readStatus, dateRange],
    queryFn: async () => {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: ITEMS_PER_PAGE.toString(),
      });

      if (readStatus !== 'all') {
        params.append('read', readStatus === 'read' ? '1' : '0');
      }

      if (dateRange !== 'all') {
        params.append('date_range', dateRange);
      }

      const { data } = await api.get<NotificationsResponse>(
        `/profile/notifications?${params.toString()}`
      );

      return {
        ...data,
        data: data.data.map(parseNotificationData),
      };
    }
  });

  const markAsReadMutation = useMutation({
    mutationFn: async (id: number) => {
      const { data } = await api.post<MarkAsReadResponse>(`/profile/notifications/${id}/read`);
      return data;
    },
    onSuccess: (data) => {
      queryClient.setQueryData<NotificationsResponse>(['notifications', page, readStatus, dateRange], (old) => {
        if (!old) return old;

        return {
          ...old,
          data: old.data.map(notification =>
            notification.id === data.data.id
              ? { ...notification, read_at: data.data.read_at }
              : notification
          )
        };
      });

      setMarkAsReadError(null);
    },
    onError: (error: any) => {
      setMarkAsReadError(
        error.response?.data?.message ||
        'Failed to mark notification as read. Please try again.'
      );

      setTimeout(() => setMarkAsReadError(null), 3000);
    }
  });

  const handleNotificationPress = (notification: Notification) => {
    setSelectedNotification(notification);
    if (!notification.read_at) {
      markAsReadMutation.mutate(notification.id);
      notification.read_at = (new Date()).toISOString();
    }
  };

  const renderNotification = ({ item }: { item: Notification }) => (
    <TouchableOpacity
      style={[styles.notificationItem, !item.read_at && styles.unreadItem]}
      onPress={() => handleNotificationPress(item)}
      disabled={markAsReadMutation.isPending}
    >
      {!item.read_at && <View style={styles.unreadDot} />}
      <View style={styles.notificationContent}>
        <Text style={[styles.title, !item.read_at && styles.unreadTitle]} numberOfLines={1}>
          {item.title}
        </Text>
        <Text style={styles.message} numberOfLines={2}>
          {item.body}
        </Text>
        <Text style={styles.timestamp}>
          {formatDistanceToNow(new Date(item.sent_at))}
        </Text>
      </View>
    </TouchableOpacity>
  );

  if (error) {
    return (
      <View style={styles.centerContainer}>
        <Text style={styles.errorText}>Failed to load notifications</Text>
        <TouchableOpacity style={styles.retryButton} onPress={() => refetch()}>
          <Text style={styles.retryButtonText}>Try Again</Text>
        </TouchableOpacity>
      </View>
    );
  }

  const notifications = data?.data ?? [];
  const totalCount = data?.count ?? 0;
  const unreadCount = notifications.filter(n => !n.read_at).length;

  return (
    <>
      <Stack.Screen 
        options={{
          title: 'Notifications',
          headerShadowVisible: false,
        }} 
      />
      <View style={styles.container}>
        <View style={styles.header}>
          <View style={styles.headerContent}>
            <Bell size={24} color="#1F2937" />
            <Text style={styles.headerTitle}>Notifications</Text>
            {unreadCount > 0 && (
              <View style={styles.badge}>
                <Text style={styles.badgeText}>{unreadCount}</Text>
              </View>
            )}
          </View>
        </View>

        {markAsReadError && (
          <View style={styles.errorBanner}>
            <Text style={styles.errorBannerText}>{markAsReadError}</Text>
          </View>
        )}

        {isLoading ? (
          <FlatList
            data={Array(5).fill(null)}
            renderItem={() => <NotificationSkeleton />}
            contentContainerStyle={styles.list}
          />
        ) : notifications.length === 0 ? (
          <View style={styles.emptyState}>
            <BellOff size={48} color="#9CA3AF" />
            <Text style={styles.emptyTitle}>No notifications</Text>
            <Text style={styles.emptyMessage}>
              You're all caught up! New notifications will appear here.
            </Text>
          </View>
        ) : (
          <FlatList
            data={notifications}
            renderItem={renderNotification}
            keyExtractor={(item) => `notification_${item.id}`}
            contentContainerStyle={styles.list}
            refreshControl={
              <RefreshControl
                refreshing={isFetching && !isLoading}
                onRefresh={refetch}
                tintColor="#B89C4C"
              />
            }
            onEndReached={() => {
              if (notifications.length < totalCount) {
                setPage(prev => prev + 1);
              }
            }}
            onEndReachedThreshold={0.5}
            ListFooterComponent={
              isFetching && !isLoading ? (
                <View style={styles.loadingMore}>
                  <ActivityIndicator color="#B89C4C" />
                </View>
              ) : null
            }
          />
        )}

        <FloatingFilterButton onPress={() => setIsFilterVisible(true)} />

        <FilterDialog
          visible={isFilterVisible}
          onClose={() => setIsFilterVisible(false)}
          title="Filter Notifications"
        >
          <NotificationFilters
            readStatus={readStatus}
            dateRange={dateRange}
            onReadStatusChange={(status) => {
              setReadStatus(status);
              setPage(1);
            }}
            onDateRangeChange={(range) => {
              setDateRange(range);
              setPage(1);
            }}
          />
        </FilterDialog>

        {selectedNotification && (
          <NotificationDetailsModal
            visible={true}
            onClose={() => setSelectedNotification(null)}
            notification={{
              ...selectedNotification,
              type: selectedNotification.parsedData?.type,
              parsedData: selectedNotification.parsedData,
            }}
          />
        )}
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1F2937',
  },
  list: {
    padding: 16,
  },
  notificationItem: {
    flexDirection: 'row',
    padding: 16,
    borderRadius: 8,
    backgroundColor: '#fff',
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  unreadItem: {
    backgroundColor: '#F3F4F6',
  },
  notificationContent: {
    flex: 1,
  },
  unreadDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#B89C4C',
    marginRight: 12,
    marginTop: 6,
  },
  title: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1F2937',
    marginBottom: 4,
  },
  unreadTitle: {
    fontWeight: '600',
  },
  message: {
    fontSize: 14,
    color: '#4B5563',
    marginBottom: 8,
  },
  timestamp: {
    fontSize: 12,
    color: '#6B7280',
  },
  centerContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
  },
  errorText: {
    fontSize: 16,
    color: '#EF4444',
    marginBottom: 12,
  },
  retryButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#B89C4C',
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#fff',
    fontWeight: '500',
  },
  errorBanner: {
    backgroundColor: '#FEE2E2',
    padding: 12,
    marginHorizontal: 16,
    marginTop: 16,
    borderRadius: 8,
  },
  errorBannerText: {
    color: '#EF4444',
    fontSize: 14,
  },
  emptyState: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1F2937',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyMessage: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
  },
  loadingMore: {
    padding: 16,
    alignItems: 'center',
  },
  skeletonContainer: {
    flexDirection: 'row',
    padding: 16,
    borderRadius: 8,
    backgroundColor: '#fff',
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  skeletonDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#E5E7EB',
    marginRight: 12,
    marginTop: 6,
  },
  skeletonContent: {
    flex: 1,
  },
  skeletonTitle: {
    height: 20,
    backgroundColor: '#E5E7EB',
    borderRadius: 4,
    marginBottom: 8,
    width: '60%',
  },
  skeletonText: {
    height: 16,
    backgroundColor: '#E5E7EB',
    borderRadius: 4,
    marginBottom: 8,
    width: '80%',
  },
  skeletonDate: {
    height: 12,
    backgroundColor: '#E5E7EB',
    borderRadius: 4,
    width: '30%',
  },
  badge: {
    backgroundColor: '#EF4444',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 2,
    minWidth: 24,
  },
  badgeText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
    textAlign: 'center',
  },
});