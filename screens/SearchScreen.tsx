import React, { useState } from 'react';
import { View, Text, StyleSheet, FlatList } from 'react-native';
import { Stack, router } from 'expo-router';
import { ArrowLeft } from 'lucide-react-native';
import SearchBar from '@/components/SearchBar';
import Button from '@/components/Button';

export function SearchScreen() {
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    // Implement your search logic here
  };

  return (
    <>
      <Stack.Screen
        options={{
          headerShown: true,
          header: () => (
            <View style={styles.header}>
              <View style={styles.backButtonContainer}>
                <Button
                  variant="ghost"
                  icon={<ArrowLeft size={24} color="#111827" />}
                  onPress={() => router.back()}
                />
              </View>
              <Text style={styles.headerTitle}>Search</Text>
              <View style={styles.backButtonContainer} />
            </View>
          ),
        }}
      />
      <View style={styles.container}>
        <SearchBar
          value={searchQuery}
          onChangeText={handleSearch}
          placeholder="Search..."
          isLoading={isLoading}
          debounceDelay={500}
        />
        <View style={styles.content}>
          <Text style={styles.placeholder}>
            Search for leads, properties, or tasks
          </Text>
        </View>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
    position: 'relative',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#111827',
    textAlign: 'center',
    flex: 1,
  },
  backButtonContainer: {
    width: 40,
    alignItems: 'flex-start',
  },
  container: {
    flex: 1,
    backgroundColor: '#fff',
    paddingTop: 20,
    paddingHorizontal: 20,
  },
  content: {
    flex: 1,
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  placeholder: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
  },
});