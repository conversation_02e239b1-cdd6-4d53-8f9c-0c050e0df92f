import React, { useState, useRef } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  Image, 
  TouchableOpacity, 
  ScrollView,
  Animated 
} from 'react-native';
import { Settings, Mail, Phone, Building2, Languages as Language, Shield, LogOut } from 'lucide-react-native';
import { useProfile } from '@/contexts/ProfileContext';
import { useAuth } from '@/contexts/AuthContext';
import EditProfileModal from '@/components/EditProfileModal';

export default function ProfileScreen() {
  const { logout } = useAuth();
  const { profile, loading, error, refreshProfile } = useProfile();
  const [isEditing, setIsEditing] = useState(false);

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Error logging out:', error);
    }
  };

  if (loading) {
    return <ProfileSkeleton />;
  }

  if (error) {
    return (
      <View style={[styles.container, styles.errorContainer]}>
        <Text style={styles.errorText}>Error loading profile</Text>
        <TouchableOpacity 
          style={styles.retryButton}
          onPress={refreshProfile}
        >
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <>
      <ScrollView style={styles.container}>
        <View style={styles.header}>
          <View style={styles.avatarContainer}>
            <Image
              source={
                profile?.profile_image_url
                  ? { uri: profile.profile_image_url }
                  : { uri: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80' }
              }
              style={styles.avatar}
            />
            <TouchableOpacity 
              style={styles.editButton}
              onPress={() => setIsEditing(true)}
            >
              <Text style={styles.editButtonText}>Edit</Text>
            </TouchableOpacity>
          </View>
          <Text style={styles.name}>{profile?.name}</Text>
          <Text style={styles.role}>{profile?.position}</Text>
          {profile?.short_bio && (
            <Text style={styles.bio}>{profile.short_bio}</Text>
          )}
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Contact Information</Text>
          <View style={styles.infoItem}>
            <Mail size={20} color="#6B7280" />
            <Text style={styles.infoText}>{profile?.email}</Text>
          </View>
          {profile?.phone && (
            <View style={styles.infoItem}>
              <Phone size={20} color="#6B7280" />
              <Text style={styles.infoText}>{profile.phone}</Text>
            </View>
          )}
          <View style={styles.infoItem}>
            <Language size={20} color="#6B7280" />
            <Text style={styles.infoText}>{profile?.languages || 'Not specified'}</Text>
          </View>
          {profile?.rating && (
            <View style={styles.infoItem}>
              <Building2 size={20} color="#6B7280" />
              <Text style={styles.infoText}>Rating: {profile.rating}</Text>
            </View>
          )}
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Account Settings</Text>
          <TouchableOpacity style={styles.settingItem}>
            <Settings size={20} color="#6B7280" />
            <Text style={styles.settingText}>Preferences</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.settingItem}>
            <Shield size={20} color="#6B7280" />
            <Text style={styles.settingText}>Privacy & Security</Text>
          </TouchableOpacity>
          <TouchableOpacity 
            style={[styles.settingItem, styles.logoutButton]} 
            onPress={handleLogout}
          >
            <LogOut size={20} color="#EF4444" />
            <Text style={styles.logoutText}>Log Out</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>

      {isEditing && profile && (
        <EditProfileModal
          profile={profile}
          onClose={() => {
            setIsEditing(false);
            refreshProfile(); // Refresh profile data after editing
          }}
        />
      )}
    </>
  );
}

function ProfileSkeleton() {
  const pulseAnim = React.useRef(new Animated.Value(0)).current;

  React.useEffect(() => {
    const pulse = Animated.sequence([
      Animated.timing(pulseAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.timing(pulseAnim, {
        toValue: 0,
        duration: 1000,
        useNativeDriver: true,
      }),
    ]);

    Animated.loop(pulse).start();
  }, []);

  const opacity = pulseAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0.3, 0.7],
  });

  const Placeholder = ({ width, height, style }: { width: number, height: number, style?: any }) => (
    <Animated.View
      style={[
        {
          width,
          height,
          backgroundColor: '#E5E7EB',
          borderRadius: 8,
          opacity,
        },
        style,
      ]}
    />
  );

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <View style={styles.avatarContainer}>
          <Placeholder
            width={120}
            height={120}
            style={{ borderRadius: 60, marginBottom: 8 }}
          />
          <Placeholder width={60} height={32} style={{ borderRadius: 16 }} />
        </View>
        <Placeholder width={180} height={28} style={{ marginBottom: 8 }} />
        <Placeholder width={120} height={20} style={{ marginBottom: 12 }} />
        <Placeholder width={240} height={60} />
      </View>

      <View style={styles.section}>
        <Placeholder width={160} height={24} style={{ marginBottom: 20 }} />
        <View style={{ gap: 16 }}>
          {[1, 2, 3, 4].map((i) => (
            <View key={i} style={styles.infoItem}>
              <Placeholder width={20} height={20} style={{ borderRadius: 4 }} />
              <Placeholder width={200} height={20} style={{ borderRadius: 4 }} />
            </View>
          ))}
        </View>
      </View>

      <View style={styles.section}>
        <Placeholder width={160} height={24} style={{ marginBottom: 20 }} />
        <View style={{ gap: 16 }}>
          {[1, 2, 3].map((i) => (
            <View key={i} style={styles.settingItem}>
              <Placeholder width={20} height={20} style={{ borderRadius: 4 }} />
              <Placeholder width={160} height={20} style={{ borderRadius: 4 }} />
            </View>
          ))}
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    color: '#EF4444',
    fontSize: 16,
    marginBottom: 12,
  },
  retryButton: {
    backgroundColor: '#3B82F6',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
  header: {
    backgroundColor: '#fff',
    padding: 20,
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  avatarContainer: {
    marginBottom: 16,
    alignItems: 'center',
  },
  avatar: {
    width: 120,
    height: 120,
    borderRadius: 60,
    marginBottom: 8,
  },
  editButton: {
    position: 'absolute',
    right: -20,
    bottom: 8,
    backgroundColor: '#3B82F6',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  editButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
  name: {
    fontSize: 24,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 4,
  },
  role: {
    fontSize: 16,
    color: '#6B7280',
    marginBottom: 8,
  },
  bio: {
    fontSize: 14,
    color: '#4B5563',
    textAlign: 'center',
    paddingHorizontal: 20,
    lineHeight: 20,
  },
  section: {
    backgroundColor: '#fff',
    padding: 20,
    marginTop: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 16,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    gap: 12,
  },
  infoText: {
    fontSize: 16,
    color: '#4B5563',
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    gap: 12,
  },
  settingText: {
    fontSize: 16,
    color: '#4B5563',
  },
  logoutButton: {
    marginTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  logoutText: {
    color: '#EF4444',
    fontSize: 16,
    fontWeight: '500',
  },
  skeletonContainer: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
});
