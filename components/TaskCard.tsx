import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Animated } from 'react-native';
import { Task } from '@/types/global';
import { Calendar, User, Clock, Tag } from 'lucide-react-native';
import { router } from 'expo-router';

interface TaskCardProps {
  task: Task;
  isLoading?: boolean;
}

export function TaskCardSkeleton() {
  const pulseAnim = React.useRef(new Animated.Value(0)).current;

  React.useEffect(() => {
    const pulse = Animated.sequence([
      Animated.timing(pulseAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.timing(pulseAnim, {
        toValue: 0,
        duration: 1000,
        useNativeDriver: true,
      }),
    ]);

    Animated.loop(pulse).start();
  }, []);

  const opacity = pulseAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0.3, 0.7],
  });

  const Placeholder = ({ width, height, style }: { width: number, height: number, style?: any }) => (
    <Animated.View
      style={[{
        width,
        height,
        backgroundColor: '#E5E7EB',
        borderRadius: 4,
        opacity,
      }, style]}
    />
  );

  return (
    <View style={styles.card}>
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <Placeholder width={200} height={20} style={{ marginBottom: 8 }} />
          <View style={styles.typeAndStatus}>
            <Placeholder width={80} height={24} style={{ borderRadius: 12 }} />
            <Placeholder width={80} height={24} style={{ borderRadius: 12 }} />
          </View>
        </View>
      </View>

      <View style={styles.content}>
        <View style={styles.infoRow}>
          <User size={16} color="#6B7280" />
          <Placeholder width={160} height={16} />
        </View>
        <View style={styles.infoRow}>
          <Calendar size={16} color="#6B7280" />
          <Placeholder width={120} height={16} />
        </View>
        <View style={styles.infoRow}>
          <Tag size={16} color="#6B7280" />
          <Placeholder width={100} height={16} />
        </View>
      </View>
    </View>
  );
}

export default function TaskCard({ task, isLoading }: TaskCardProps) {
  if (isLoading) {
    return <TaskCardSkeleton />;
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return '#10B981';
      case 'overdue':
        return '#EF4444';
      case 'in_progress':
        return '#F59E0B';
      default:
        return '#6B7280';
    }
  };

  const getObjectTypeLabel = (type: 'lead' | 'contact') => {
    return type.charAt(0).toUpperCase() + type.slice(1);
  };

  const getName = () => {
    if (task.object_type === 'lead' && task.lead?.name) {
      return task.lead.name;
    }
    return task.ct_contact_name || 'No name';
  };

  return (
    <TouchableOpacity
      style={styles.container}
      onPress={() => {
        if (task.object_type === 'lead' && task.lead_id) {
          router.push(`/leads/${task.lead_id}`);
        }
      }}
    >
      <View style={styles.header}>
        <Text style={styles.subject} numberOfLines={2}>
          {task.subject}
        </Text>
        <View style={styles.badges}>
          <View style={[styles.badge, { backgroundColor: getStatusColor(task.status) }]}>
            <Text style={styles.badgeText}>
              {task.status.replace('_', ' ').toUpperCase()}
            </Text>
          </View>
          <View style={[styles.badge, { backgroundColor: '#8B5CF6' }]}>
            <Text style={styles.badgeText}>
              {getObjectTypeLabel(task.object_type)}
            </Text>
          </View>
        </View>
      </View>

      <View style={styles.content}>
        <View style={styles.infoRow}>
          <User size={16} color="#6B7280" />
          <Text style={styles.infoText}>{getName()}</Text>
        </View>
        <View style={styles.infoRow}>
          <Calendar size={16} color="#6B7280" />
          <Text style={styles.infoText}>
            Due: {task.due_date}
          </Text>
        </View>
        <View style={styles.infoRow}>
          <Clock size={16} color="#6B7280" />
          <Text style={styles.infoText}>
            Created: {new Date(task.created_at).toLocaleDateString()}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  header: {
    marginBottom: 12,
  },
  subject: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 8,
  },
  badges: {
    flexDirection: 'row',
    gap: 8,
  },
  badge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  badgeText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '500',
  },
  content: {
    gap: 8,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  infoText: {
    fontSize: 14,
    color: '#4B5563',
  },
});
