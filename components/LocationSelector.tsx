import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, ActivityIndicator } from 'react-native';
import { MapPin, Building2, Flag } from 'lucide-react-native';

interface Country {
  id: number;
  name: string;
  code: string;
  phone_prefix: string;
}

interface Location {
  id: number;
  name: string;
  slug: string;
  parent_id: number;
}

interface Tower {
  id: number;
  name: string;
}

interface LocationSelectorProps {
  countries: Country[];
  locations: Location[];
  towers: Tower[];
  selectedCountry: Country | null;
  selectedLocation: Location | null;
  selectedTower: Tower | null;
  isLoadingLocations: boolean;
  isLoadingTowers: boolean;
  onCountryChange: (country: Country) => void;
  onLocationChange: (location: Location) => void;
  onTowerChange: (tower: Tower) => void;
}

export default function LocationSelector({
  countries,
  locations,
  towers,
  selectedCountry,
  selectedLocation,
  selectedTower,
  isLoadingLocations,
  isLoadingTowers,
  onCountryChange,
  onLocationChange,
  onTowerChange,
}: LocationSelectorProps) {
  // Sort countries to put Qatar first
  const sortedCountries = React.useMemo(() => {
    return [...countries].sort((a, b) => {
      // Put Qatar first
      if (a.name === 'Qatar') return -1;
      if (b.name === 'Qatar') return 1;
      // Sort the rest alphabetically
      return a.name.localeCompare(b.name);
    });
  }, [countries]);

  return (
    <View style={styles.container}>
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Flag size={20} color="#6B7280" />
          <Text style={styles.sectionTitle}>Country</Text>
        </View>
        <ScrollView 
          horizontal 
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.optionsContainer}
        >
          {sortedCountries.map((country) => (
            <TouchableOpacity
              key={country.id}
              style={[
                styles.option,
                selectedCountry?.id === country.id && styles.optionSelected,
              ]}
              onPress={() => onCountryChange(country)}
            >
              <Text
                style={[
                  styles.optionText,
                  selectedCountry?.id === country.id && styles.optionTextSelected,
                ]}
              >
                {country.name}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {selectedCountry && (
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <MapPin size={20} color="#6B7280" />
            <Text style={styles.sectionTitle}>Location</Text>
          </View>
          {isLoadingLocations ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="small" color="#B89C4C" />
              <Text style={styles.loadingText}>Loading locations...</Text>
            </View>
          ) : locations.length === 0 ? (
            <Text style={styles.emptyText}>No locations available</Text>
          ) : (
            <ScrollView 
              horizontal 
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.optionsContainer}
            >
              {locations.map((location) => (
                <TouchableOpacity
                  key={location.id}
                  style={[
                    styles.option,
                    selectedLocation?.id === location.id && styles.optionSelected,
                  ]}
                  onPress={() => onLocationChange(location)}
                >
                  <Text
                    style={[
                      styles.optionText,
                      selectedLocation?.id === location.id && styles.optionTextSelected,
                    ]}
                  >
                    {location.name}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          )}
        </View>
      )}

      {selectedLocation && (
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Building2 size={20} color="#6B7280" />
            <Text style={styles.sectionTitle}>Tower</Text>
          </View>
          {isLoadingTowers ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="small" color="#B89C4C" />
              <Text style={styles.loadingText}>Loading towers...</Text>
            </View>
          ) : towers.length === 0 ? (
            <Text style={styles.emptyText}>No towers available</Text>
          ) : (
            <ScrollView 
              horizontal 
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.optionsContainer}
            >
              {towers.map((tower) => (
                <TouchableOpacity
                  key={tower.id}
                  style={[
                    styles.option,
                    selectedTower?.id === tower.id && styles.optionSelected,
                  ]}
                  onPress={() => onTowerChange(tower)}
                >
                  <Text
                    style={[
                      styles.optionText,
                      selectedTower?.id === tower.id && styles.optionTextSelected,
                    ]}
                  >
                    {tower.name}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          )}
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    gap: 24,
  },
  section: {
    gap: 12,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#4B5563',
  },
  optionsContainer: {
    gap: 8,
    paddingRight: 20,
  },
  option: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
  },
  optionSelected: {
    backgroundColor: '#B89C4C',
  },
  optionText: {
    fontSize: 14,
    color: '#4B5563',
    fontWeight: '500',
  },
  optionTextSelected: {
    color: '#fff',
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    paddingVertical: 8,
  },
  loadingText: {
    fontSize: 14,
    color: '#6B7280',
  },
  emptyText: {
    fontSize: 14,
    color: '#6B7280',
    fontStyle: 'italic',
    paddingVertical: 8,
  },
});