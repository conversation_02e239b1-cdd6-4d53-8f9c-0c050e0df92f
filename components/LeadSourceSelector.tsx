import React, { useState } from 'react';
import { View, Text, StyleSheet, Modal, TouchableOpacity, FlatList, ActivityIndicator } from 'react-native';
import { X, Search } from 'lucide-react-native';
import { useQuery } from '@tanstack/react-query';
import { api } from '@/lib/api';
import SearchBar from '@/components/SearchBar';
import { IdNamePack } from '@/types/global';

interface LeadSource {
    id: number;
    name: string;
    description: string | null;
}

interface LeadSourceSelectorProps {
    visible: boolean;
    onClose: () => void;
    onSelect: (source: LeadSource) => void;
}

export default function LeadSourceSelector({
    visible,
    onClose,
    onSelect,
}: LeadSourceSelectorProps) {
    const [searchQuery, setSearchQuery] = useState('');

    const { data, isLoading, error } = useQuery({
        queryKey: ['leadSources'],
        queryFn: async () => {
            const { data } = await api.get('/lead-source', {
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': "XMLHttpRequest"
                }
            });
            return data;
        },
    });

    const sources = data ?? [];
    const filteredSources = searchQuery.length >= 2
        ? sources.filter((source: IdNamePack) =>
            source.name.toLowerCase().includes(searchQuery.toLowerCase())
        )
        : sources;

    const handleSelect = (source: LeadSource) => {
        onSelect(source);
        onClose();
    };

    return (
        <Modal
            visible={visible}
            transparent
            animationType="slide"
            onRequestClose={onClose}
        >
            <View style={styles.overlay}>
                <View style={styles.modal}>
                    <View style={styles.header}>
                        <Text style={styles.title}>Select Lead Source</Text>
                        <TouchableOpacity
                            onPress={onClose}
                            hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
                        >
                            <X size={24} color="#6B7280" />
                        </TouchableOpacity>
                    </View>

                    <View style={styles.searchContainer}>
                        <SearchBar
                            value={searchQuery}
                            onChangeText={setSearchQuery}
                            placeholder="Search lead sources..."
                            icon={<Search size={20} color="#6B7280" />}
                            isLoading={isLoading}
                        />
                    </View>

                    {error ? (
                        <View style={styles.messageContainer}>
                            <Text style={styles.errorText}>Error loading lead sources</Text>
                        </View>
                    ) : isLoading ? (
                        <View style={styles.messageContainer}>
                            <ActivityIndicator size="large" color="#B89C4C" />
                        </View>
                    ) : filteredSources.length === 0 ? (
                        <View style={styles.messageContainer}>
                            <Text style={styles.messageText}>
                                {searchQuery.length >= 2
                                    ? "No matching lead sources found"
                                    : "No lead sources available"}
                            </Text>
                        </View>
                    ) : (
                        <FlatList
                            data={filteredSources}
                            keyExtractor={(item) => item.id.toString()}
                            renderItem={({ item }) => (
                                <TouchableOpacity
                                    style={styles.sourceItem}
                                    onPress={() => handleSelect(item)}
                                >
                                    <Text style={styles.sourceName}>{item.name}</Text>
                                    {item.description && (
                                        <Text style={styles.sourceDescription}>
                                            {item.description}
                                        </Text>
                                    )}
                                </TouchableOpacity>
                            )}
                            contentContainerStyle={styles.listContent}
                        />
                    )}
                </View>
            </View>
        </Modal>
    );
}

const styles = StyleSheet.create({
    overlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'flex-end',
    },
    modal: {
        backgroundColor: '#fff',
        borderTopLeftRadius: 24,
        borderTopRightRadius: 24,
        maxHeight: '90%',
        minHeight: '90%',
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 20,
        borderBottomWidth: 1,
        borderBottomColor: '#E5E7EB',
    },
    title: {
        fontSize: 20,
        fontWeight: '600',
        color: '#1F2937',
    },
    searchContainer: {
        padding: 20,
        paddingBottom: 12,
    },
    listContent: {
        padding: 20,
        paddingTop: 0,
    },
    sourceItem: {
        backgroundColor: '#F9FAFB',
        padding: 16,
        borderRadius: 12,
        marginBottom: 12,
    },
    sourceName: {
        fontSize: 16,
        fontWeight: '600',
        color: '#1F2937',
        marginBottom: 4,
    },
    sourceDescription: {
        fontSize: 14,
        color: '#6B7280',
    },
    messageContainer: {
        padding: 20,
        alignItems: 'center',
    },
    messageText: {
        fontSize: 14,
        color: '#6B7280',
        textAlign: 'center',
    },
    errorText: {
        fontSize: 14,
        color: '#EF4444',
        textAlign: 'center',
    },
});