import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, SafeAreaView } from 'react-native';
import { useRouter, usePathname } from 'expo-router';
import { Search, Bell } from 'lucide-react-native';
import { useQuery } from '@tanstack/react-query';
import { api } from '@/lib/api';
import UserAvatar from '@/components/UserAvatar';
import { useProfile } from '@/contexts/ProfileContext';

const fetchUnreadNotificationsCount = async () => {
  const { data } = await api.get('/api/notifications/unread/count');
  return data.count;
};

const getPageTitle = (pathname: string): string => {
  const routes: { [key: string]: string } = {
    '/dashboard': 'Dashboard',
    '/leads': 'Leads',
    '/inventory': 'Inventory',
    '/tasks': 'Tasks',
  };
  
  return routes[pathname] || '';
};

export default function TopHeader() {
  const router = useRouter();
  const pathname = usePathname();
  const { profile } = useProfile(); // Use the profile context instead
  
  const { data: unreadCount = 0 } = useQuery({
    queryKey: ['unreadNotifications'],
    queryFn: fetchUnreadNotificationsCount,
    refetchInterval: 30000,
  });

  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.container}>
        <Text style={styles.title}>{getPageTitle(pathname)}</Text>
        <View style={styles.actions}>
          <TouchableOpacity 
            style={styles.iconButton}
            onPress={() => router.push('/search')}
          >
            <Search size={24} color="#1F2937" />
          </TouchableOpacity>

          <TouchableOpacity 
            style={styles.iconButton}
            onPress={() => router.push('/notifications')}
          >
            <View>
              <Bell size={24} color="#1F2937" />
              {unreadCount > 0 && (
                <View style={styles.badge}>
                  <Text style={styles.badgeText}>
                    {unreadCount > 99 ? '99+' : unreadCount}
                  </Text>
                </View>
              )}
            </View>
          </TouchableOpacity>

          <TouchableOpacity 
            style={styles.iconButton}
            onPress={() => router.push('/profile')}
          >
            <UserAvatar
              imageUrl={profile?.profile_image_url}
              name={profile?.name || ''}
              size={32}
              fontSize={14}
            />
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    backgroundColor: '#fff',
  },
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1F2937',
  },
  actions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  iconButton: {
    padding: 4,
  },
  badge: {
    position: 'absolute',
    top: -6,
    right: -6,
    backgroundColor: '#EF4444',
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 4,
  },
  badgeText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
});
