import React from 'react';
import { View, Text, Pressable, StyleSheet } from 'react-native';
import { Minus, Plus } from 'lucide-react-native';

interface SelectorProps {
    items: Array<{ key: string; value: string }>;
    selectedItem: { key: string; value: string };
    onSelectionChange: (item: { key: string; value: string }) => void;
}

export function Selector({ items, selectedItem, onSelectionChange }: SelectorProps) {
    const currentIndex = items.findIndex(item => item.key === selectedItem?.key);

    const handlePrevious = () => {
        if (currentIndex > 0) {
            onSelectionChange(items[currentIndex - 1]);
        }
    };

    const handleNext = () => {
        if (currentIndex < items.length - 1) {
            onSelectionChange(items[currentIndex + 1]);
        }
    };

    return (
        <View style={styles.container}>
            <Pressable
                style={[
                    styles.button,
                    currentIndex === 0 && styles.buttonDisabled
                ]}
                onPress={handlePrevious}
                disabled={currentIndex === 0}>
                <Minus
                    size={20}
                    color={currentIndex === 0 ? '#A1A1AA' : '#18181B'}
                />
            </Pressable>

            <View style={styles.valueContainer}>
                <Text style={styles.value}>{selectedItem?.value}</Text>
            </View>

            <Pressable
                style={[
                    styles.button,
                    currentIndex === items.length - 1 && styles.buttonDisabled
                ]}
                onPress={handleNext}
                disabled={currentIndex === items.length - 1}>
                <Plus
                    size={20}
                    color={currentIndex === items.length - 1 ? '#A1A1AA' : '#18181B'}
                />
            </Pressable>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 12,
    },
    button: {
        width: 40,
        height: 40,
        borderRadius: 20,
        backgroundColor: '#F4F4F5',
        justifyContent: 'center',
        alignItems: 'center',
    },
    buttonDisabled: {
        opacity: 0.5,
    },
    valueContainer: {
        minWidth: 80,
        paddingHorizontal: 12,
        height: 40,
        backgroundColor: '#F4F4F5',
        borderRadius: 20,
        justifyContent: 'center',
        alignItems: 'center',
    },
    value: {
        fontSize: 16,
        fontWeight: '500',
        color: '#18181B',
    },
});