import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { useQuery } from '@tanstack/react-query';
import { fetchLeadStatuses, fetchPropertyTypes } from '@/lib/api';
import { RotateCcw, ArrowDown, ArrowUp } from 'lucide-react-native';
import { ListingType } from '@/types/global';

interface LeadFiltersProps {
  selectedStatus: string;
  selectedSort: string;
  selectedPropertyType: number | null;
  selectedListType: string;
  propertyTypes: Array<ListingType>;
  onStatusChange: (status: string) => void;
  onSortChange: (sort: string) => void;
  onPropertyTypeChange: (type: number | null) => void;
  onListTypeChange: (type: string) => void;
  onReset?: () => void;
  isLoading: boolean;
  leadsCount?: number;
  onViewLeads?: () => void;
  onApplyFilters?: () => void;
}

const sortOptions = [
  {
    label: 'Latest Contact',
    options: [
      { label: 'Latest Contact (Newest)', value: 'lastContact_desc' },
      { label: 'Latest Contact (Oldest)', value: 'lastContact_asc' },
    ]
  },
  {
    label: 'Created Date',
    options: [
      { label: 'Created (Newest)', value: 'created_desc' },
      { label: 'Created (Oldest)', value: 'created_asc' },
    ]
  }
];

const listTypes = [
  { label: 'Masterlist', value: 'masterlist' },
  { label: 'Personal', value: 'personal' },
];


export default function LeadFilters({
  selectedStatus,
  selectedSort,
  selectedPropertyType,
  selectedListType,
  propertyTypes,
  onStatusChange,
  onSortChange,
  onPropertyTypeChange,
  onListTypeChange,
  onReset,
  leadsCount,
  onViewLeads,
}: LeadFiltersProps) {
  const { data: statuses = [] } = useQuery({
    queryKey: ['leadStatuses'],
    queryFn: fetchLeadStatuses,
  });

  const hasActiveFilters = selectedStatus !== 'all' ||
    selectedSort !== 'lastContact_desc' || // Update default sort check
    selectedPropertyType !== null ||
    selectedListType !== 'personal';

  const handleReset = () => {
    if (onReset) {
      // Use the provided reset callback if available
      onReset();
    } else {
      // Fallback to individual callbacks
      onStatusChange('all');
      onSortChange('lastContact_desc');
      onPropertyTypeChange(null);
      onListTypeChange('personal');
    }
  };

  return (
    <View style={styles.container}>
      {hasActiveFilters && (
        <TouchableOpacity
          style={styles.resetButton}
          onPress={handleReset}
        >
          <RotateCcw size={16} color="#6B7280" />
          <Text style={styles.resetButtonText}>Reset all filters</Text>
        </TouchableOpacity>
      )}

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>List Type</Text>
        <View style={styles.optionsGrid}>
          {listTypes.map((type) => (
            <TouchableOpacity
              key={type.value}
              style={[
                styles.optionButton,
                selectedListType === type.value && styles.optionButtonActive,
              ]}
              onPress={() => onListTypeChange(type.value)}
            >
              <Text
                style={[
                  styles.optionButtonText,
                  selectedListType === type.value && styles.optionButtonTextActive,
                ]}
              >
                {type.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Status</Text>
        <View style={styles.optionsGrid}>
          <TouchableOpacity
            style={[
              styles.optionButton,
              selectedStatus === 'all' && styles.optionButtonActive,
            ]}
            onPress={() => onStatusChange('all')}
          >
            <Text
              style={[
                styles.optionButtonText,
                selectedStatus === 'all' && styles.optionButtonTextActive,
              ]}
            >
              All
            </Text>
          </TouchableOpacity>
          {statuses.map((status) => (
            <TouchableOpacity
              key={status.name}
              style={[
                styles.optionButton,
                selectedStatus === status.name && { backgroundColor: status.background_color },
              ]}
              onPress={() => onStatusChange(status.name)}
            >
              <Text
                style={[
                  styles.optionButtonText,
                  selectedStatus === status.name && styles.optionButtonTextActive,
                ]}
              >
                {status.name.split('_').map(word =>
                  word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
                ).join(' ')}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Property Type</Text>
        <View style={styles.optionsGrid}>
          <TouchableOpacity
            style={[
              styles.optionButton,
              selectedPropertyType === null && styles.optionButtonActive,
            ]}
            onPress={() => onPropertyTypeChange(null)}
          >
            <Text
              style={[
                styles.optionButtonText,
                selectedPropertyType === null && styles.optionButtonTextActive,
              ]}
            >
              All
            </Text>
          </TouchableOpacity>
          {propertyTypes.map((type: ListingType) => (
            <TouchableOpacity
              key={type.id}
              style={[
                styles.optionButton,
                selectedPropertyType === type.id && styles.optionButtonActive,
              ]}
              onPress={() => onPropertyTypeChange(type.id)}
            >
              <Text
                style={[
                  styles.optionButtonText,
                  selectedPropertyType === type.id && styles.optionButtonTextActive,
                ]}
              >
                {type.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Sort By</Text>
        <View style={styles.optionsGrid}>
          {sortOptions.flatMap(group =>
            group.options.map(option => (
              <TouchableOpacity
                key={option.value}
                style={[
                  styles.optionButton,
                  selectedSort === option.value && styles.optionButtonActive,
                ]}
                onPress={() => onSortChange(option.value)}
              >
                <View style={styles.sortOptionContent}>
                  <Text
                    style={[
                      styles.optionButtonText,
                      selectedSort === option.value && styles.optionButtonTextActive,
                    ]}
                  >
                    {option.label}
                  </Text>
                  {option.value.endsWith('_desc') ? (
                    <ArrowDown
                      size={14}
                      color={selectedSort === option.value ? '#fff' : '#4B5563'}
                    />
                  ) : (
                    <ArrowUp
                      size={14}
                      color={selectedSort === option.value ? '#fff' : '#4B5563'}
                    />
                  )}
                </View>
              </TouchableOpacity>
            ))
          )}
        </View>
      </View>

    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingBottom: 20,
  },
  resetButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
    marginBottom: 24,
    alignSelf: 'flex-start',
  },
  resetButtonText: {
    color: '#6B7280',
    fontSize: 14,
    fontWeight: '500',
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 12,
  },
  optionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  optionButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
  },
  optionButtonActive: {
    backgroundColor: '#B89C4C',
  },
  optionButtonText: {
    fontSize: 14,
    color: '#4B5563',
    fontWeight: '500',
  },
  optionButtonTextActive: {
    color: '#fff',
  },
  sortOptionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
});