import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { DollarSign } from 'lucide-react-native';

interface PriceSelectorProps {
    title: string;
    values: number[];
    selectedValue: number | null;
    discriminant: string;
    onSelect: (value: number) => void;
    formatPrice: (price: number) => string;
}

export default function PriceSelector({
    title,
    values,
    selectedValue,
    discriminant,
    onSelect,
    formatPrice
}: PriceSelectorProps) {
    return (
        <View style={styles.container}>
            <View style={styles.header}>
                <DollarSign size={20} color="#6B7280" />
                <Text style={styles.title}>{title}</Text>
            </View>

            <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={styles.optionsContainer}
            >
                {values.map((value) => (
                    <TouchableOpacity
                        key={`${discriminant}_${value}`}
                        style={[
                            styles.option,
                            selectedValue === value && styles.optionSelected
                        ]}
                        onPress={() => onSelect(value)}
                    >
                        <Text
                            style={[
                                styles.optionText,
                                selectedValue === value && styles.optionTextSelected
                            ]}
                        >
                            {formatPrice(value)}
                        </Text>
                    </TouchableOpacity>
                ))}
            </ScrollView>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 8,
        marginBottom: 12,
    },
    title: {
        fontSize: 14,
        fontWeight: '500',
        color: '#4B5563',
    },
    optionsContainer: {
        gap: 8,
        paddingRight: 20,
    },
    option: {
        paddingHorizontal: 16,
        paddingVertical: 8,
        borderRadius: 20,
        backgroundColor: '#F3F4F6',
    },
    optionSelected: {
        backgroundColor: '#B89C4C',
    },
    optionText: {
        fontSize: 14,
        color: '#4B5563',
        fontWeight: '500',
    },
    optionTextSelected: {
        color: '#fff',
    },
});