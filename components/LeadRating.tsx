import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Star } from 'lucide-react-native';

interface LeadRatingProps {
    rating: string | null;
    showLabel?: boolean;
    size?: 'small' | 'medium' | 'large';
}

const getSizeConfig = (size: 'small' | 'medium' | 'large') => {
    switch (size) {
        case 'large':
            return {
                badge: { paddingHorizontal: 12, paddingVertical: 6 },
                icon: 18,
                text: 16,
            };
        case 'medium':
            return {
                badge: { paddingHorizontal: 10, paddingVertical: 5 },
                icon: 16,
                text: 14,
            };
        case 'small':
            return {
                badge: { paddingHorizontal: 8, paddingVertical: 4 },
                icon: 14,
                text: 12,
            };
    }
};

export default function LeadRating({ rating, showLabel = false, size = 'small' }: LeadRatingProps) {
    if (!rating) {
        return (
            <View style={[
                styles.badge,
                { backgroundColor: '#6b7280' },
                getSizeConfig(size).badge,
            ]}>
                <Text style={[styles.text, { fontSize: getSizeConfig(size).text }]}>
                    Non Rated
                </Text>
            </View>
        );
    }

    const getRatingColor = (rating: string) => {
        switch (rating) {
            case 'A':
                return '#22c55e';
            case 'B':
                return '#3b82f6';
            case 'C':
                return '#f59e0b';
            case 'D':
                return '#fb923c';
            case 'E':
                return '#ef4444';
            case 'F':
                return '#991b1b';
            default:
                return '#6b7280';
        }
    };

    const sizeConfig = getSizeConfig(size);

    return (
        <View style={[
            styles.badge,
            { backgroundColor: getRatingColor(rating) },
            sizeConfig.badge,
        ]}>
            <Star size={sizeConfig.icon} color="#fff" />
            <Text style={[styles.text, { fontSize: sizeConfig.text }]}>
                {showLabel ? `Rating ${rating}` : rating}
            </Text>
        </View>
    );
}

const styles = StyleSheet.create({
    badge: {
        flexDirection: 'row',
        alignItems: 'center',
        borderRadius: 16,
        gap: 4,
    },
    text: {
        color: '#fff',
        fontWeight: '500',
    },
});