import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  TextInput,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';


interface StatusOption {
  id: number;
  name: string;
  background_color: string;
  is_disabled: number;
}

interface StatusDropdownProps {
  statuses: StatusOption[];
  selectedStatus: string;
  onSelectStatus: (statusId: string) => void;
  onAdditionalAction?: (statusId: string) => void; // For future extensibility
}



export default function StatusDropdown({
  statuses,
  selectedStatus,
  onSelectStatus,
  onAdditionalAction,
}: StatusDropdownProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [searchText, setSearchText] = useState('');

  const enabledStatuses = statuses.filter(status => status.is_disabled === 0);

  const filteredStatuses = enabledStatuses.filter(status =>
    status.name.toLowerCase().includes(searchText.toLowerCase())
  );

  const formatStatusName = (name: string) => {
    return name.replace(/_/g, ' ');
  };

  const getCurrentStatus = () => {
    return enabledStatuses.find(status => status.id.toString() === selectedStatus);
  };

  const handleStatusSelect = (statusId: string) => {
    onSelectStatus(statusId);
    setIsExpanded(false);
    setSearchText('');
  };

  const currentStatus = getCurrentStatus();

  return (
    <View style={styles.container}>
      <Text style={styles.label}>Status</Text>
      {/* Current Status Display */}
      <TouchableOpacity
        style={styles.dropdownButton}
        onPress={() => setIsExpanded(!isExpanded)}
      >
        <View style={styles.statusDisplay}>
          {currentStatus && (
            <View
              style={[
                styles.statusIndicator,
                { backgroundColor: currentStatus.background_color }
              ]}
            />
          )}
          <Text style={styles.statusText}>
            {currentStatus ? formatStatusName(currentStatus.name) : 'Select Status'}
          </Text>
        </View>
        <Ionicons
          name={isExpanded ? "chevron-up" : "chevron-down"}
          size={20}
          color="#6B7280"
        />
      </TouchableOpacity>

      {/* Expanded Dropdown */}
      {isExpanded && (
        <View style={styles.dropdownContent}>
          {/* Search Input */}
          <View style={styles.searchContainer}>
            <Ionicons name="search" size={16} color="#6B7280" />
            <TextInput
              style={styles.searchInput}
              placeholder="Search status..."
              value={searchText}
              onChangeText={setSearchText}
              placeholderTextColor="#9CA3AF"
            />
          </View>

          {/* Status Options */}
          <ScrollView style={styles.optionsContainer} showsVerticalScrollIndicator={false}>
            {filteredStatuses.map((status) => (
              <TouchableOpacity
                key={status.id}
                style={[
                  styles.statusOption,
                  selectedStatus === status.id.toString() && styles.selectedOption
                ]}
                onPress={() => handleStatusSelect(status.id.toString())}
              >
                <View style={styles.statusOptionContent}>
                  <View
                    style={[
                      styles.statusIndicator,
                      { backgroundColor: status.background_color }
                    ]}
                  />
                  <Text style={[
                    styles.statusOptionText,
                    selectedStatus === status.id.toString() && styles.selectedOptionText
                  ]}>
                    {formatStatusName(status.name)}
                  </Text>
                </View>
                {selectedStatus === status.id.toString() && (
                  <Ionicons name="checkmark" size={16} color="#B89C4C" />
                )}
              </TouchableOpacity>
            ))}
          </ScrollView>

          {/* Future extensibility: Additional actions can be added here */}
          {onAdditionalAction && (
            <View style={styles.additionalActions}>
              {/* Placeholder for future additional functionality */}
            </View>
          )}
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    zIndex: 1000,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 8,
  },
  dropdownButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#F9FAFB',
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    minHeight: 48,
  },
  statusDisplay: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  statusText: {
    fontSize: 16,
    color: '#374151',
    flex: 1,
  },
  dropdownContent: {
    position: 'absolute',
    top: '100%',
    left: 0,
    right: 0,
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    marginTop: 4,
    maxHeight: 300,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
    zIndex: 1001,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 14,
    color: '#374151',
    paddingVertical: 4,
  },
  optionsContainer: {
    maxHeight: 200,
  },
  statusOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 12,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  selectedOption: {
    backgroundColor: '#F0F9FF',
  },
  statusOptionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  statusOptionText: {
    fontSize: 14,
    color: '#374151',
    marginLeft: 8,
  },
  selectedOptionText: {
    color: '#B89C4C',
    fontWeight: '500',
  },
  additionalActions: {
    padding: 12,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
});
