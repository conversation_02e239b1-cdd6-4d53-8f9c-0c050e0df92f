import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { RotateCcw } from 'lucide-react-native';

interface NotificationFiltersProps {
  readStatus: 'all' | 'read' | 'unread';
  dateRange: 'all' | 'today' | 'week' | 'month';
  onReadStatusChange: (status: 'all' | 'read' | 'unread') => void;
  onDateRangeChange: (range: 'all' | 'today' | 'week' | 'month') => void;
}

export default function NotificationFilters({
  readStatus,
  dateRange,
  onReadStatusChange,
  onDateRangeChange,
}: NotificationFiltersProps) {
  const hasActiveFilters = readStatus !== 'all' || dateRange !== 'all';

  const handleReset = () => {
    onReadStatusChange('all');
    onDateRangeChange('all');
  };

  return (
    <View style={styles.container}>
      {hasActiveFilters && (
        <TouchableOpacity
          style={styles.resetButton}
          onPress={handleReset}
        >
          <RotateCcw size={16} color="#6B7280" />
          <Text style={styles.resetButtonText}>Reset filters</Text>
        </TouchableOpacity>
      )}

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Status</Text>
        <View style={styles.optionsGrid}>
          <TouchableOpacity
            style={[
              styles.optionButton,
              readStatus === 'all' && styles.optionButtonActive,
            ]}
            onPress={() => onReadStatusChange('all')}
          >
            <Text
              style={[
                styles.optionButtonText,
                readStatus === 'all' && styles.optionButtonTextActive,
              ]}
            >
              All
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.optionButton,
              readStatus === 'unread' && styles.optionButtonActive,
            ]}
            onPress={() => onReadStatusChange('unread')}
          >
            <Text
              style={[
                styles.optionButtonText,
                readStatus === 'unread' && styles.optionButtonTextActive,
              ]}
            >
              Unread
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.optionButton,
              readStatus === 'read' && styles.optionButtonActive,
            ]}
            onPress={() => onReadStatusChange('read')}
          >
            <Text
              style={[
                styles.optionButtonText,
                readStatus === 'read' && styles.optionButtonTextActive,
              ]}
            >
              Read
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Time Period</Text>
        <View style={styles.optionsGrid}>
          <TouchableOpacity
            style={[
              styles.optionButton,
              dateRange === 'all' && styles.optionButtonActive,
            ]}
            onPress={() => onDateRangeChange('all')}
          >
            <Text
              style={[
                styles.optionButtonText,
                dateRange === 'all' && styles.optionButtonTextActive,
              ]}
            >
              All Time
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.optionButton,
              dateRange === 'today' && styles.optionButtonActive,
            ]}
            onPress={() => onDateRangeChange('today')}
          >
            <Text
              style={[
                styles.optionButtonText,
                dateRange === 'today' && styles.optionButtonTextActive,
              ]}
            >
              Today
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.optionButton,
              dateRange === 'week' && styles.optionButtonActive,
            ]}
            onPress={() => onDateRangeChange('week')}
          >
            <Text
              style={[
                styles.optionButtonText,
                dateRange === 'week' && styles.optionButtonTextActive,
              ]}
            >
              This Week
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.optionButton,
              dateRange === 'month' && styles.optionButtonActive,
            ]}
            onPress={() => onDateRangeChange('month')}
          >
            <Text
              style={[
                styles.optionButtonText,
                dateRange === 'month' && styles.optionButtonTextActive,
              ]}
            >
              This Month
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingBottom: 20,
  },
  resetButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
    marginBottom: 24,
    alignSelf: 'flex-start',
  },
  resetButtonText: {
    color: '#6B7280',
    fontSize: 14,
    fontWeight: '500',
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 12,
  },
  optionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  optionButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
  },
  optionButtonActive: {
    backgroundColor: '#B89C4C',
  },
  optionButtonText: {
    fontSize: 14,
    color: '#4B5563',
    fontWeight: '500',
  },
  optionButtonTextActive: {
    color: '#fff',
  },
});