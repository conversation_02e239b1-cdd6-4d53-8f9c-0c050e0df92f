import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Linking, Platform, Animated } from 'react-native';
import { Phone, Hash, Clock, Star } from 'lucide-react-native';
import { FontAwesome } from '@expo/vector-icons';
import LeadRating from './LeadRating';
import LeadStatus from './LeadStatus';

export type Lead = {
  id: number;
  contact_name: string;
  contact_email_1: string;
  contact_mobile_1: string;
  contact_prefix_mobile_1: string;
  complete_phone_no: string;
  lead_status_name: string | null;
  lead_status_background_color: string;
  last_call_timing: string;
  assignment_user_name: string;
  rating: string;
  requirements: string;
  created_at: string;
};

interface LeadCardProps {
  lead?: Lead;
  isLoading?: boolean;
}

export function LeadCardSkeleton() {
  const pulseAnim = React.useRef(new Animated.Value(0)).current;

  React.useEffect(() => {
    const pulse = Animated.sequence([
      Animated.timing(pulseAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.timing(pulseAnim, {
        toValue: 0,
        duration: 1000,
        useNativeDriver: true,
      }),
    ]);

    Animated.loop(pulse).start();
  }, []);

  const opacity = pulseAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0.3, 0.7],
  });

  const Placeholder = ({ width, height, style }: { width: number, height: number, style?: any }) => (
    <Animated.View
      style={[{
        width,
        height,
        backgroundColor: '#E5E7EB',
        borderRadius: 4,
        opacity,
      }, style]}
    />
  );

  return (
    <View style={styles.card}>
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <View style={styles.nameRow}>
            <Placeholder width={40} height={24} style={{ borderRadius: 12 }} />
            <Placeholder width={150} height={24} style={{ marginBottom: 8 }} />
          </View>
          <Placeholder width={200} height={18} style={{ marginBottom: 4 }} />
          <Placeholder width={140} height={20} />
        </View>
        <View style={styles.actionButtons}>
          <View style={[styles.callButton, { opacity: 0.5 }]}>
            <Phone size={20} color="#fff" />
            <Text style={styles.callButtonText}>Call</Text>
          </View>
          <View style={[styles.whatsappButton, { opacity: 0.5 }]}>
            <FontAwesome name="whatsapp" size={20} color="#fff" />
            <Text style={styles.whatsappButtonText}>WhatsApp</Text>
          </View>
        </View>
      </View>



      <View style={styles.footer}>
        <View style={styles.statusRow}>
          <Placeholder width={80} height={24} style={{ borderRadius: 16 }} />
          <Placeholder width={80} height={24} style={{ borderRadius: 16 }} />
        </View>
        <Placeholder width={120} height={16} />
      </View>
    </View>
  );
}

export default function LeadCard({ lead, isLoading }: LeadCardProps) {
  if (isLoading || !lead) {
    return <LeadCardSkeleton />;
  }

  const handleCall = () => {
    const phoneUrl = Platform.select({
      ios: `tel:${lead.complete_phone_no}`,
      android: `tel:${lead.complete_phone_no}`,
      web: `tel:${lead.complete_phone_no}`,
    });

    if (phoneUrl) {
      Linking.openURL(phoneUrl).catch((err) => console.error('Error opening phone:', err));
    }
  };

  const handleWhatsApp = async () => {
    try {
      // Clean and format the phone number
      let cleanPhone = lead.complete_phone_no.replace(/[^\d+]/g, '');

      // Ensure the phone number starts with + if it doesn't already
      if (!cleanPhone.startsWith('+')) {
        cleanPhone = `+${cleanPhone}`;
      }

      console.log('Attempting to open WhatsApp for:', cleanPhone);

      // Try different WhatsApp URL schemes
      const whatsappUrls = [
        `whatsapp://send?phone=${cleanPhone}`,
        `https://wa.me/${cleanPhone}`,
        `https://api.whatsapp.com/send?phone=${cleanPhone}`
      ];

      // Try the native app first
      const canOpenNative = await Linking.canOpenURL(whatsappUrls[0]);

      if (canOpenNative) {
        console.log('Opening native WhatsApp app');
        await Linking.openURL(whatsappUrls[0]);
      } else {
        console.log('Native WhatsApp not available, trying web version');
        // Try web version
        await Linking.openURL(whatsappUrls[1]);
      }
    } catch (error) {
      console.error('Error opening WhatsApp:', error);

      // Final fallback - try the API URL
      try {
        const cleanPhone = lead.complete_phone_no.replace(/[^\d+]/g, '');
        const fallbackUrl = `https://api.whatsapp.com/send?phone=${cleanPhone.startsWith('+') ? cleanPhone : '+' + cleanPhone}`;
        console.log('Trying fallback URL:', fallbackUrl);
        await Linking.openURL(fallbackUrl);
      } catch (fallbackError) {
        console.error('All WhatsApp options failed:', fallbackError);
        // You could show an alert here to inform the user
      }
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 24) {
      return `${diffInHours}h ago`;
    } else if (diffInHours < 48) {
      return 'Yesterday';
    } else {
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      });
    }
  };

  return (
    <View style={styles.card}>
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <View style={styles.nameRow}>
            <View style={styles.idBadge}>
              <Hash size={12} color="#6B7280" />
              <Text style={styles.idText}>{lead.id}</Text>
            </View>
            <Text style={styles.name}>{lead.contact_name || 'No Name'}</Text>
          </View>
          <Text style={styles.assignedAgent}>Assigned to: {lead.assignment_user_name}</Text>
          <Text style={styles.phoneNumber}>{lead.complete_phone_no}</Text>
        </View>
        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={styles.callButton}
            onPress={handleCall}
          >
            <Phone size={20} color="#fff" />
            <Text style={styles.callButtonText}>Call</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.whatsappButton}
            onPress={handleWhatsApp}
          >
            <FontAwesome name="whatsapp" size={20} color="#fff" />
            <Text style={styles.whatsappButtonText}>WhatsApp</Text>
          </TouchableOpacity>
        </View>
      </View>



      <View style={styles.footer}>
        <View style={styles.footerContent}>
          <View style={styles.statusRow}>
            <LeadStatus
              name={lead.lead_status_name}
              backgroundColor={lead.lead_status_background_color}
            />
            <LeadRating rating={lead.rating} />
          </View>
          {lead.last_call_timing && (
            <View style={styles.lastContactRow}>
              <Clock size={12} color="#6B7280" />
              <Text style={styles.lastContactText}>
                Last contact: {formatDate(lead.last_call_timing)}
              </Text>
            </View>
          )}
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  card: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
    width: '100%',
  },
  headerContent: {
    flex: 1,
    marginRight: 16,
  },
  nameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 4,
    flexWrap: 'wrap',
  },
  name: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
  },
  idBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F3F4F6',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
    gap: 4,
  },
  idText: {
    fontSize: 13,
    color: '#4B5563',
    fontWeight: '500',
  },
  phoneNumber: {
    fontSize: 16,
    color: '#374151',
    fontWeight: '500',
    marginTop: 4,
    marginBottom: 8,
  },
  assignedAgent: {
    fontSize: 14,
    color: '#6B7280',
  },
  actionButtons: {
    flexDirection: 'column',
    gap: 8,
  },
  callButton: {
    backgroundColor: '#2563EB',
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
    gap: 8,
    minWidth: 100,
    justifyContent: 'center',
  },
  callButtonText: {
    color: '#fff',
    fontWeight: '500',
  },
  whatsappButton: {
    backgroundColor: '#25D366',
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
    gap: 8,
    minWidth: 100,
    justifyContent: 'center',
  },
  whatsappButtonText: {
    color: '#fff',
    fontWeight: '500',
  },

  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  statusRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  badge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 16,
    gap: 4,
  },
  badgeText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '500',
  },
  lastContact: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  date: {
    color: '#6B7280',
    fontSize: 12,
  },
  footerContent: {
    gap: 8,
  },
  lastContactRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  lastContactText: {
    fontSize: 12,
    color: '#6B7280',
  },
});