import React, { useState } from 'react';
import { View, Text, TextInput, StyleSheet, TouchableOpacity, ScrollView, ActivityIndicator, Modal, Dimensions, Pressable, Image, Platform } from 'react-native';
import { X, Camera } from 'lucide-react-native';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import * as ImagePicker from 'expo-image-picker';
import { api } from '@/lib/api';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

interface EditProfileModalProps {
  profile: {
    email: string;
    name: string;
    phone: string;
    position: string;
    languages: string;
    short_bio: string;
    profile_image_url?: string;
  };
  onClose: () => void;
}

export default function EditProfileModal({ profile, onClose }: EditProfileModalProps) {
  const [formData, setFormData] = useState({
    email: profile.email,
    name: profile.name,
    phone: profile.phone || '',
    position: profile.position || '',
    languages: profile.languages || '',
    short_bio: profile.short_bio || '',
  });

  const [photo, setPhoto] = useState<{
    uri: string;
    base64?: string;
    name?: string;
  } | null>(null);

  const queryClient = useQueryClient();

  const { mutate, isPending, error } = useMutation({
    mutationFn: async (data: typeof formData) => {
      const payload = { ...data };
      
      if (photo?.base64) {
        payload.photo = {
          name: photo.name || 'profile-photo.jpg',
          content: photo.base64
        };
      }

      const response = await api.patch('/profile', payload);
      return response.data;
    },
    onSuccess: (data) => {
      // Invalidate both profile-related queries
      queryClient.invalidateQueries({ queryKey: ['profile'] });
      // Also update the profile context data
      queryClient.setQueryData(['profile'], data);
      onClose();
    },
  });

  const pickImage = async () => {
    try {
      // Request permissions first
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      
      if (status !== 'granted') {
        alert('Permission to access camera roll is required!');
        return;
      }

      let result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ['images'],
        allowsEditing: true,
        aspect: [4, 3],
        quality: 1,
        base64: true,
      });

      if (!result.canceled) {
        const asset = result.assets[0];
        const name = asset.uri.split('/').pop() || 'profile-photo.jpg';
        
        setPhoto({
          uri: asset.uri,
          base64: asset.base64 as string,
          name
        });
      }
    } catch (err) {
      console.error('Error picking image:', err);
      alert('Failed to pick image. Please try again.');
    }
  };

  const handleSubmit = () => {
    mutate(formData);
  };

  return (
    <Modal
      visible={true}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <Pressable style={styles.dismissArea} onPress={onClose} />
        <View style={styles.modalContent}>
          <View style={styles.header}>
            <Text style={styles.title}>Edit Profile</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <X size={24} color="#6B7280" />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.form} showsVerticalScrollIndicator={false}>
            {error && (
              <Text style={styles.error}>
                {error instanceof Error ? error.message : 'An error occurred'}
              </Text>
            )}

            <View style={styles.photoSection}>
              <View style={styles.photoContainer}>
                {photo ? (
                  <Image source={{ uri: photo.uri }} style={styles.photo} />
                ) : profile.profile_image_url ? (
                  <Image source={{ uri: profile.profile_image_url }} style={styles.photo} />
                ) : (
                  <View style={[styles.photo, styles.photoPlaceholder]} />
                )}
                <TouchableOpacity 
                  style={styles.changePhotoButton}
                  onPress={pickImage}
                >
                  <Camera size={20} color="#fff" />
                </TouchableOpacity>
              </View>
              <TouchableOpacity onPress={pickImage}>
                <Text style={styles.changePhotoText}>Change Photo</Text>
              </TouchableOpacity>
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Name *</Text>
              <TextInput
                style={styles.input}
                value={formData.name}
                onChangeText={(text) => setFormData(prev => ({ ...prev, name: text }))}
                placeholder="Your name"
                maxLength={128}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Email *</Text>
              <TextInput
                style={styles.input}
                value={formData.email}
                onChangeText={(text) => setFormData(prev => ({ ...prev, email: text }))}
                placeholder="Your email"
                keyboardType="email-address"
                autoCapitalize="none"
                maxLength={56}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Phone</Text>
              <TextInput
                style={styles.input}
                value={formData.phone}
                onChangeText={(text) => setFormData(prev => ({ ...prev, phone: text }))}
                placeholder="Your phone number"
                keyboardType="phone-pad"
                maxLength={32}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Position</Text>
              <TextInput
                style={styles.input}
                value={formData.position}
                onChangeText={(text) => setFormData(prev => ({ ...prev, position: text }))}
                placeholder="Your position"
                maxLength={32}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Languages</Text>
              <TextInput
                style={styles.input}
                value={formData.languages}
                onChangeText={(text) => setFormData(prev => ({ ...prev, languages: text }))}
                placeholder="Languages you speak"
                maxLength={255}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Bio</Text>
              <TextInput
                style={[styles.input, styles.textArea]}
                value={formData.short_bio}
                onChangeText={(text) => setFormData(prev => ({ ...prev, short_bio: text }))}
                placeholder="Tell us about yourself"
                multiline
                numberOfLines={4}
                maxLength={1024}
              />
            </View>
          </ScrollView>

          <View style={styles.footer}>
            <TouchableOpacity 
              style={[styles.button, styles.cancelButton]} 
              onPress={onClose}
              disabled={isPending}
            >
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            <TouchableOpacity 
              style={[styles.button, styles.saveButton, isPending && styles.buttonDisabled]} 
              onPress={handleSubmit}
              disabled={isPending}
            >
              {isPending ? (
                <ActivityIndicator color="#fff" />
              ) : (
                <Text style={styles.saveButtonText}>Save Changes</Text>
              )}
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  dismissArea: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  modalContent: {
    width: Math.min(SCREEN_WIDTH * 0.9, 600),
    height: SCREEN_HEIGHT * 0.85,
    backgroundColor: '#fff',
    borderRadius: 16,
    overflow: 'hidden',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1F2937',
  },
  closeButton: {
    padding: 8,
  },
  form: {
    flex: 1,
    padding: 20,
  },
  photoSection: {
    alignItems: 'center',
    marginBottom: 24,
  },
  photoContainer: {
    position: 'relative',
    marginBottom: 12,
  },
  photo: {
    width: 120,
    height: 120,
    borderRadius: 60,
  },
  photoPlaceholder: {
    backgroundColor: '#E5E7EB',
  },
  changePhotoButton: {
    position: 'absolute',
    right: 0,
    bottom: 0,
    backgroundColor: '#3B82F6',
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: '#fff',
  },
  changePhotoText: {
    color: '#3B82F6',
    fontSize: 14,
    fontWeight: '500',
  },
  error: {
    backgroundColor: '#FEE2E2',
    color: '#EF4444',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: '#1F2937',
    backgroundColor: '#fff',
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  footer: {
    flexDirection: 'row',
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    gap: 12,
  },
  button: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: '#F3F4F6',
  },
  cancelButtonText: {
    color: '#4B5563',
    fontSize: 16,
    fontWeight: '500',
  },
  saveButton: {
    backgroundColor: '#3B82F6',
  },
  buttonDisabled: {
    backgroundColor: '#93C5FD',
  },
  saveButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
  },
});
