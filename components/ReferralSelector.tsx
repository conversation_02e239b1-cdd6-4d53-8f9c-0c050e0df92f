import React, { useState } from 'react';
import { View, Text, StyleSheet, Modal, TouchableOpacity, FlatList, ActivityIndicator } from 'react-native';
import { X, Search } from 'lucide-react-native';
import { useQuery } from '@tanstack/react-query';
import { api } from '@/lib/api';
import SearchBar from '@/components/SearchBar';

interface Referral {
    id: number;
    name: string;
    email: string | null;
    phone: string | null;
}

interface ReferralSelectorProps {
    visible: boolean;
    onClose: () => void;
    onSelect: (referral: Referral) => void;
}

export default function ReferralSelector({
    visible,
    onClose,
    onSelect,
}: ReferralSelectorProps) {
    const [searchQuery, setSearchQuery] = useState('');

    const { data, isLoading, error } = useQuery({
        queryKey: ['referrals', searchQuery],
        queryFn: async () => {
            const { data } = await api.get('/referrals', {
                params: {
                    q: searchQuery,
                    limit: 10,
                },
            });
            return data;
        },
        enabled: searchQuery.length >= 2,
    });

    const referrals = data?.data ?? [];

    return (
        <Modal
            visible={visible}
            transparent
            animationType="slide"
            onRequestClose={onClose}
        >
            <View style={styles.overlay}>
                <View style={styles.modal}>
                    <View style={styles.header}>
                        <Text style={styles.title}>Select Referral</Text>
                        <TouchableOpacity
                            onPress={onClose}
                            hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
                        >
                            <X size={24} color="#6B7280" />
                        </TouchableOpacity>
                    </View>

                    <View style={styles.searchContainer}>
                        <SearchBar
                            value={searchQuery}
                            onChangeText={setSearchQuery}
                            placeholder="Search referrals..."
                            icon={<Search size={20} color="#6B7280" />}
                            isLoading={isLoading}
                        />
                    </View>

                    {error ? (
                        <View style={styles.messageContainer}>
                            <Text style={styles.errorText}>Error loading referrals</Text>
                        </View>
                    ) : searchQuery.length < 2 ? (
                        <View style={styles.messageContainer}>
                            <Text style={styles.messageText}>
                                Type at least 2 characters to search
                            </Text>
                        </View>
                    ) : isLoading ? (
                        <View style={styles.messageContainer}>
                            <ActivityIndicator size="large" color="#B89C4C" />
                        </View>
                    ) : referrals.length === 0 ? (
                        <View style={styles.messageContainer}>
                            <Text style={styles.messageText}>No referrals found</Text>
                        </View>
                    ) : (
                        <FlatList
                            data={referrals}
                            keyExtractor={(item) => item.id.toString()}
                            renderItem={({ item }) => (
                                <TouchableOpacity
                                    style={styles.referralItem}
                                    onPress={() => {
                                        onSelect(item);
                                        onClose();
                                    }}
                                >
                                    <Text style={styles.referralName}>{item.name}</Text>
                                    {item.email && (
                                        <Text style={styles.referralDetail}>{item.email}</Text>
                                    )}
                                    {item.phone && (
                                        <Text style={styles.referralDetail}>{item.phone}</Text>
                                    )}
                                </TouchableOpacity>
                            )}
                            contentContainerStyle={styles.listContent}
                        />
                    )}
                </View>
            </View>
        </Modal>
    );
}

const styles = StyleSheet.create({
    overlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'flex-end',
    },
    modal: {
        backgroundColor: '#fff',
        borderTopLeftRadius: 24,
        borderTopRightRadius: 24,
        maxHeight: '80%',
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 20,
        borderBottomWidth: 1,
        borderBottomColor: '#E5E7EB',
    },
    title: {
        fontSize: 20,
        fontWeight: '600',
        color: '#1F2937',
    },
    searchContainer: {
        padding: 20,
        paddingBottom: 12,
    },
    listContent: {
        padding: 20,
        paddingTop: 0,
    },
    referralItem: {
        backgroundColor: '#F9FAFB',
        padding: 16,
        borderRadius: 12,
        marginBottom: 12,
    },
    referralName: {
        fontSize: 16,
        fontWeight: '600',
        color: '#1F2937',
        marginBottom: 4,
    },
    referralDetail: {
        fontSize: 14,
        color: '#6B7280',
    },
    messageContainer: {
        padding: 20,
        alignItems: 'center',
    },
    messageText: {
        fontSize: 14,
        color: '#6B7280',
        textAlign: 'center',
    },
    errorText: {
        fontSize: 14,
        color: '#EF4444',
        textAlign: 'center',
    },
});