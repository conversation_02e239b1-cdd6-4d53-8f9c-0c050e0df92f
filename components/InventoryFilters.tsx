import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { RotateCcw } from 'lucide-react-native';
import { ListingType } from '@/types/global';

interface InventoryFiltersProps {
    selectedStatus: string;
    selectedPropertyType: number;
    selectedAdType: string;
    selectedViewType: string;
    propertyTypes: Array<ListingType>;
    onPropertyTypeChange: (type: number) => void;
    onAdTypeChange: (type: string) => void;
    onViewTypeChange: (type: string) => void;
}

const viewTypes = [
    { key: 'master', 'label': 'Available' },
    { key: 'not_available', 'label': 'Not Available' },
    { key: 'pending', 'label': 'Pending' },
    { key: 'archived', 'label': 'Archived' },
    { key: 'personal', 'label': 'Personal' },
    { key: 'all_listings', 'label': 'All' },
];

const adTypes = [
    'All',
    'Rent',
    'Sale',
];

const statuses = [
    'All',
    'Available',
    'Reserved',
    'Rented',
    'Sold',
];

export default function InventoryFilters({
    selectedStatus,
    selectedPropertyType,
    selectedAdType,
    selectedViewType,
    propertyTypes,
    onPropertyTypeChange,
    onAdTypeChange,
    onViewTypeChange,
}: InventoryFiltersProps) {
    const hasActiveFilters =
        selectedStatus !== 'All' ||
        selectedPropertyType !== null ||
        selectedAdType !== 'All'
    selectedViewType !== 'master';

    const handleReset = () => {
        onPropertyTypeChange(null);
        onAdTypeChange('All');
        onViewTypeChange('master');
    };

    return (
        <View style={styles.container}>
            {hasActiveFilters && (
                <TouchableOpacity
                    style={styles.resetButton}
                    onPress={handleReset}
                >
                    <RotateCcw size={16} color="#6B7280" />
                    <Text style={styles.resetButtonText}>Reset filters</Text>
                </TouchableOpacity>
            )}

            <View style={styles.section}>
                <Text style={styles.sectionTitle}>View Type</Text>
                <View style={styles.optionsGrid}>
                    {viewTypes.map((type) => (
                        <TouchableOpacity
                            key={type.key}
                            style={[
                                styles.optionButton,
                                selectedViewType === type.key && styles.optionButtonActive,
                            ]}
                            onPress={() => onViewTypeChange(type.key)}
                        >
                            <Text
                                style={[
                                    styles.optionButtonText,
                                    selectedViewType === type.key && styles.optionButtonTextActive,
                                ]}
                            >
                                {type.label}
                            </Text>
                        </TouchableOpacity>
                    ))}
                </View>
            </View>

            <View style={styles.section}>
                <Text style={styles.sectionTitle}>Property Type</Text>
                <View style={styles.optionsGrid}>
                    <TouchableOpacity
                        key={null}
                        style={[
                            styles.optionButton,
                            selectedPropertyType === null && styles.optionButtonActive,
                        ]}
                        onPress={() => onPropertyTypeChange(null)}
                    >
                        <Text
                            style={[
                                styles.optionButtonText,
                                selectedPropertyType === null && styles.optionButtonTextActive,
                            ]}
                        >
                            All
                        </Text>
                    </TouchableOpacity>
                    {propertyTypes.map((type: ListingType) => (
                        <TouchableOpacity
                            key={type.id}
                            style={[
                                styles.optionButton,
                                selectedPropertyType === type.id && styles.optionButtonActive,
                            ]}
                            onPress={() => onPropertyTypeChange(type.id)}
                        >
                            <Text
                                style={[
                                    styles.optionButtonText,
                                    selectedPropertyType === type.id && styles.optionButtonTextActive,
                                ]}
                            >
                                {type.label}
                            </Text>
                        </TouchableOpacity>
                    ))}
                </View>
            </View>

            <View style={styles.section}>
                <Text style={styles.sectionTitle}>Ad Type</Text>
                <View style={styles.optionsGrid}>
                    {adTypes.map((type) => (
                        <TouchableOpacity
                            key={type}
                            style={[
                                styles.optionButton,
                                selectedAdType === type && styles.optionButtonActive,
                            ]}
                            onPress={() => onAdTypeChange(type)}
                        >
                            <Text
                                style={[
                                    styles.optionButtonText,
                                    selectedAdType === type && styles.optionButtonTextActive,
                                ]}
                            >
                                {type}
                            </Text>
                        </TouchableOpacity>
                    ))}
                </View>
            </View>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        paddingBottom: 20,
    },
    resetButton: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 8,
        paddingVertical: 12,
        paddingHorizontal: 16,
        backgroundColor: '#F3F4F6',
        borderRadius: 8,
        marginBottom: 24,
        alignSelf: 'flex-start',
    },
    resetButtonText: {
        color: '#6B7280',
        fontSize: 14,
        fontWeight: '500',
    },
    section: {
        marginBottom: 24,
    },
    sectionTitle: {
        fontSize: 16,
        fontWeight: '600',
        color: '#1F2937',
        marginBottom: 12,
    },
    optionsGrid: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        gap: 8,
    },
    optionButton: {
        paddingHorizontal: 16,
        paddingVertical: 8,
        borderRadius: 20,
        backgroundColor: '#F3F4F6',
    },
    optionButtonActive: {
        backgroundColor: '#B89C4C',
    },
    optionButtonText: {
        fontSize: 14,
        color: '#4B5563',
        fontWeight: '500',
    },
    optionButtonTextActive: {
        color: '#fff',
    },
});