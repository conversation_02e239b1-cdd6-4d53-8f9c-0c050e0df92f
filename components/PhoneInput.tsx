import React, { useState, useMemo } from 'react';
import { View, Text, StyleSheet, TextInput, TouchableOpacity, Modal, FlatList } from 'react-native';
import { ChevronDown, Search } from 'lucide-react-native';
import { useCountries } from '@/hooks/useConfiguration';
import { Country } from '@/types/global';
import SearchBar from './SearchBar';

interface PhoneInputProps {
  value: string;
  onChangeText: (text: string) => void;
  onPrefixChange?: (prefix: string) => void;
  label?: string;
  placeholder?: string;
}

export default function PhoneInput({ 
  value, 
  onChangeText, 
  onPrefixChange,
  label, 
  placeholder = 'Enter phone number' 
}: PhoneInputProps) {
  const { countries, defaultCountry } = useCountries();
  const [selectedCountry, setSelectedCountry] = useState<Country>(defaultCountry);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  const filteredCountries = useMemo(() => {
    if (!searchQuery.trim()) return countries;
    const query = searchQuery.toLowerCase();
    return countries.filter(country => 
      country.name.toLowerCase().includes(query) || 
      country.phone_prefix.includes(query)
    );
  }, [countries, searchQuery]);

  const handleCountrySelect = (country: Country) => {
    setSelectedCountry(country);
    onPrefixChange?.(country.phone_prefix);
    setIsModalVisible(false);
    setSearchQuery(''); // Reset search when closing
  };

  const handlePhoneChange = (text: string) => {
    // Remove any non-digit characters
    const cleaned = text.replace(/[^0-9]/g, '');
    
    // Limit to 8 digits (Qatar phone number format)
    const limited = cleaned.slice(0, 8);
    
    // Format the number as #### ####
    const formatted = limited.replace(/(\d{4})(?=\d)/g, '$1 ');
    onChangeText(formatted);
  };

  return (
    <View style={styles.container}>
      {label && <Text style={styles.label}>{label}</Text>}
      <View style={styles.inputContainer}>
        <TouchableOpacity 
          style={styles.prefixButton}
          onPress={() => setIsModalVisible(true)}
        >
          <Text style={styles.prefixText}>{selectedCountry?.phone_prefix}</Text>
          <ChevronDown size={16} color="#6B7280" />
        </TouchableOpacity>
        <TextInput
          style={styles.input}
          value={value}
          onChangeText={handlePhoneChange}
          placeholder={placeholder}
          keyboardType="phone-pad"
          maxLength={9} // 8 digits + 1 space
        />
      </View>

      <Modal
        visible={isModalVisible}
        transparent={true}
        animationType="slide"
        onRequestClose={() => {
          setIsModalVisible(false);
          setSearchQuery(''); // Reset search when closing
        }}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Country</Text>
              <TouchableOpacity 
                onPress={() => {
                  setIsModalVisible(false);
                  setSearchQuery(''); // Reset search when closing
                }}
                hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
              >
                <Text style={styles.closeButton}>Close</Text>
              </TouchableOpacity>
            </View>

            <View style={styles.searchContainer}>
              <SearchBar
                value={searchQuery}
                onChangeText={setSearchQuery}
                placeholder="Search countries..."
                icon={<Search size={20} color="#6B7280" />}
              />
            </View>
            
            <FlatList
              data={filteredCountries}
              keyExtractor={(item) => item.id.toString()}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={[
                    styles.countryOption,
                    selectedCountry?.id === item.id && styles.selectedCountry
                  ]}
                  onPress={() => handleCountrySelect(item)}
                >
                  <Text style={styles.countryPrefix}>{item.phone_prefix}</Text>
                  <Text style={styles.countryName}>{item.name}</Text>
                </TouchableOpacity>
              )}
              style={styles.countryList}
              contentContainerStyle={{ flexGrow: 1 }}
              ListEmptyComponent={() => (
                <View style={styles.noCountriesContainer}>
                  <Text style={styles.noCountriesText}>No countries found</Text>
                </View>
              )}
            />
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 8,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    overflow: 'hidden',
  },
  prefixButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 12,
    borderRightWidth: 1,
    borderRightColor: '#E5E7EB',
    backgroundColor: '#fff',
  },
  prefixText: {
    fontSize: 16,
    color: '#111827',
    marginRight: 4,
  },
  input: {
    flex: 1,
    padding: 12,
    fontSize: 16,
    color: '#111827',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  modalContainer: {
    backgroundColor: '#fff',
    borderRadius: 12,
    width: '90%',
    height: '70%',
    maxWidth: 500,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
  },
  closeButton: {
    fontSize: 16,
    color: '#6B7280',
  },
  countryList: {
    flex: 1,
    backgroundColor: '#fff', // Ensure background is white
  },
  countryOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  selectedCountry: {
    backgroundColor: '#F3F4F6',
  },
  countryPrefix: {
    fontSize: 16,
    color: '#111827',
    width: 70,
    marginRight: 12,
  },
  countryName: {
    flex: 1,
    fontSize: 16,
    color: '#111827',
  },
  noCountriesContainer: {
    padding: 16,
    alignItems: 'center',
  },
  noCountriesText: {
    fontSize: 16,
    color: '#6B7280',
  },
  searchContainer: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
}); 
