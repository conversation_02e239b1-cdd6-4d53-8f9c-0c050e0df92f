import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { UserPlus, Package, DollarSign, Globe, ListTodo, Bell } from 'lucide-react-native';
import { Notification, NotificationType } from '@/types/notification';
import { formatDistanceToNow } from '@/lib/date';

const getNotificationIcon = (type: NotificationType) => {
  switch (type) {
    case 'lead':
      return <UserPlus size={20} color="#3B82F6" />;
    case 'inventory':
      return <Package size={20} color="#10B981" />;
    case 'deal':
      return <DollarSign size={20} color="#6366F1" />;
    case 'task':
      return <ListTodo size={20} color="#8B5CF6" />;
    case 'global':
      return <Globe size={20} color="#F59E0B" />;
    default:
      return <Bell size={20} color="#6B7280" />;
  }
};

interface NotificationItemProps {
  notification: Notification;
  onPress?: (notification: Notification) => void;
  compact?: boolean;
}

export default function NotificationItem({ 
  notification,
  onPress,
  compact = false
}: NotificationItemProps) {
  return (
    <TouchableOpacity
      style={[
        styles.container,
        !notification.read_at && styles.unread,
        compact && styles.compactContainer
      ]}
      onPress={() => onPress?.(notification)}
    >
      <View style={[
        styles.iconContainer,
        !notification.read_at && styles.unreadIconContainer,
        compact && styles.compactIconContainer
      ]}>
        {getNotificationIcon(notification.type)}
      </View>

      <View style={styles.content}>
        <Text 
          style={[
            styles.title,
            !notification.read_at && styles.unreadTitle,
            compact && styles.compactTitle
          ]} 
          numberOfLines={1}
        >
          {notification.title}
        </Text>
        <Text 
          style={[
            styles.message,
            compact && styles.compactMessage
          ]} 
          numberOfLines={compact ? 1 : 2}
        >
          {notification.body}
        </Text>
        <Text style={styles.timestamp}>
          {formatDistanceToNow(new Date(notification.sent_at))}
        </Text>
      </View>

      {!notification.read_at && <View style={styles.unreadDot} />}
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    padding: 16,
    backgroundColor: '#fff',
    borderRadius: 12,
    marginBottom: 8,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  compactContainer: {
    padding: 12,
    marginBottom: 6,
  },
  unread: {
    backgroundColor: '#F8FAFC',
    borderColor: '#B89C4C',
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  compactIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    marginRight: 10,
  },
  unreadIconContainer: {
    backgroundColor: '#F8FAFC',
  },
  content: {
    flex: 1,
  },
  title: {
    fontSize: 15,
    fontWeight: '500',
    color: '#1F2937',
    marginBottom: 4,
  },
  compactTitle: {
    fontSize: 14,
    marginBottom: 2,
  },
  unreadTitle: {
    fontWeight: '600',
  },
  message: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 20,
    marginBottom: 4,
  },
  compactMessage: {
    fontSize: 13,
    lineHeight: 18,
    marginBottom: 2,
  },
  timestamp: {
    fontSize: 12,
    color: '#9CA3AF',
  },
  unreadDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#B89C4C',
    marginLeft: 8,
  },
});
