import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { TaskViewType } from '@/types/task';
import { RotateCcw } from 'lucide-react-native';

interface TaskFiltersProps {
  selectedViewType: TaskViewType;
  onViewTypeChange: (type: TaskViewType) => void;
}

const viewTypes: { label: string; value: TaskViewType }[] = [
  { label: 'All', value: 'all' },
  { label: 'Open', value: 'open' },
  { label: 'Overdue', value: 'overdue' },
  { label: 'Today', value: 'today' },
  { label: 'Completed', value: 'completed' },
  { label: 'Upcoming', value: 'upcoming' },
];

export default function TaskFilters({
  selectedViewType,
  onViewTypeChange,
}: TaskFiltersProps) {
  const hasActiveFilters = selectedViewType !== 'all';

  const handleReset = () => {
    onViewTypeChange('all');
  };

  return (
    <View style={styles.container}>
      {hasActiveFilters && (
        <TouchableOpacity
          style={styles.resetButton}
          onPress={handleReset}
        >
          <RotateCcw size={16} color="#6B7280" />
          <Text style={styles.resetButtonText}>Reset filter</Text>
        </TouchableOpacity>
      )}

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>View</Text>
        <View style={styles.optionsGrid}>
          {viewTypes.map((type) => (
            <TouchableOpacity
              key={type.value}
              style={[
                styles.optionButton,
                selectedViewType === type.value && styles.optionButtonActive,
              ]}
              onPress={() => onViewTypeChange(type.value)}
            >
              <Text
                style={[
                  styles.optionButtonText,
                  selectedViewType === type.value && styles.optionButtonTextActive,
                ]}
              >
                {type.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingBottom: 20,
  },
  resetButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
    marginBottom: 24,
    alignSelf: 'flex-start',
  },
  resetButtonText: {
    color: '#6B7280',
    fontSize: 14,
    fontWeight: '500',
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 12,
  },
  optionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  optionButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
  },
  optionButtonActive: {
    backgroundColor: '#B89C4C',
  },
  optionButtonText: {
    fontSize: 14,
    color: '#4B5563',
    fontWeight: '500',
  },
  optionButtonTextActive: {
    color: '#fff',
  },
});