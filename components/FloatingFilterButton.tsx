import React from 'react';
import { TouchableOpacity, StyleSheet, Platform } from 'react-native';
import { Filter } from 'lucide-react-native';

interface FloatingFilterButtonProps {
  onPress: () => void;
}

export default function FloatingFilterButton({ onPress }: FloatingFilterButtonProps) {
  return (
    <TouchableOpacity
      style={styles.button}
      onPress={onPress}
      activeOpacity={0.8}
    >
      <Filter size={24} color="#fff" />
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  button: {
    position: 'absolute',
    right: 20,
    bottom: Platform.select({
      ios: 34, // Account for safe area
      android: 20,
      web: 20,
    }),
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#B89C4C',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
});