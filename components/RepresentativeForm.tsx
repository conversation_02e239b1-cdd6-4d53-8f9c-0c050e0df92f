import React from 'react';
import { View, Text, TextInput, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { User, Mail, Phone, FileText, Flag } from 'lucide-react-native';
import { KeyValue } from '@/types/global';
import PhoneInput from '@/components/PhoneInput';
interface Representative {
  fullname: string;
  email: string;
  prefix_mobile_no: string;
  mobile_no: string;
  qatar_id_no: string;
  nationality_id: number;
}

interface Country {
  id: number;
  name: string;
  code: string;
  phone_prefix: string;
}

interface RepresentativeFormProps {
  value: Representative;
  countries: Country[];
  nationalities: KeyValue[];
  selectedCountry: Country | null;
  onChange: (value: Representative) => void;
}

export default function RepresentativeForm({ value, countries, nationalities, selectedCountry, onChange }: RepresentativeFormProps) {
  const handleChange = (field: keyof Representative, text: string) => {
    onChange({
      ...value,
      [field]: text,
    });
  };

  const sortedCountries = React.useMemo(() => {
    return [...countries].sort((a, b) => {
      // Put Qatar first
      if (a.name === 'Qatar') return -1;
      if (b.name === 'Qatar') return 1;
      // Sort the rest alphabetically
      return a.name.localeCompare(b.name);
    });
  }, [countries]);

  return (
    <View style={styles.container}>
      <View style={styles.inputGroup}>
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Full Name</Text>
          <TextInput
            style={styles.input}
            value={value.fullname}
            onChangeText={(text) => handleChange('fullname', text)}
            placeholder="Enter full name"
            placeholderTextColor="#9CA3AF"
          />
        </View>
      </View>

      <View style={styles.inputGroup}>
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Email</Text>
          <TextInput
            style={styles.input}
            value={value.email}
            onChangeText={(text) => handleChange('email', text)}
            placeholder="Enter email address"
            placeholderTextColor="#9CA3AF"
            keyboardType="email-address"
            autoCapitalize="none"
          />
        </View>
      </View>
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Flag size={20} color="#6B7280" />
          <Text style={styles.label}>Country</Text>
        </View>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.optionsContainer}
        >
          {sortedCountries.map((country) => (
            <TouchableOpacity
              key={country.id}
              style={[
                styles.option,
                selectedCountry?.id === country.id && styles.optionSelected,
              ]}
              onPress={() => handleChange('prefix_mobile_no', country.phone_prefix)}
            >
              <Text
                style={[
                  styles.optionText,
                  selectedCountry?.id === country.id && styles.optionTextSelected,
                ]}
              >
                {`(${country.phone_prefix}) ${country.name}`}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
      <View style={styles.inputGroup}>
        <PhoneInput
          label="Mobile"
          value={value.mobile_no}
          onChangeText={(text) => handleChange('mobile_no', text)}
          onPrefixChange={(prefix) => handleChange('prefix_mobile_no', prefix)}
          placeholder="Enter mobile number"
        />
      </View>

      <View style={styles.inputGroup}>
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Qatar ID No</Text>
          <TextInput
            style={styles.input}
            value={value.qatar_id_no}
            onChangeText={(text) => handleChange('qatar_id_no', text)}
            placeholder="Enter QID number"
            placeholderTextColor="#9CA3AF"
          />
        </View>
      </View>

      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Flag size={20} color="#6B7280" />
          <Text style={styles.label}>Nationality</Text>
        </View>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.optionsContainer}
        >
          {nationalities.map((kv) => (
            <TouchableOpacity
              key={kv.key}
              style={[
                styles.option,
                value?.nationality_id?.toString() === kv.key && styles.optionSelected,
              ]}
              onPress={() => handleChange('nationality_id', kv.key)}
            >
              <Text
                style={[
                  styles.optionText,
                  value?.nationality_id?.toString() === kv.key && styles.optionTextSelected,
                ]}
              >
                {kv.value}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
      {/* <View style={styles.inputGroup}>
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Nationality</Text>
          <TextInput
            style={styles.input}
            value={value.nationality_id}
            onChangeText={(text) => handleChange('nationality_id', text)}
            placeholder="Enter nationality"
            placeholderTextColor="#9CA3AF"
          />
        </View>
      </View> */}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    gap: 16,
  },
  section: {
    gap: 12,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  optionsContainer: {
    gap: 8,
    paddingRight: 20,
  },
  option: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
  },
  optionSelected: {
    backgroundColor: '#B89C4C',
  },
  optionText: {
    fontSize: 14,
    color: '#4B5563',
    fontWeight: '500',
  },
  optionTextSelected: {
    color: '#fff',
  },
  inputGroup: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 12,
  },
  inputIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
  },
  inputContainer: {
    flex: 1,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 4,
  },
  input: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: '#1F2937',
  },
});
