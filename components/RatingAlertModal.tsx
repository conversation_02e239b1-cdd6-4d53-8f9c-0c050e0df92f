import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Modal, TouchableOpacity, ScrollView } from 'react-native';
import { X } from 'lucide-react-native';

interface RatingOption {
  id: string;
  label: string;
  color: string;
}

interface RatingAlertModalProps {
  visible: boolean;
  onClose: () => void;
  selectedRating: string;
  onSelectRating: (ratingId: string) => void;
}

const ratingOptions: RatingOption[] = [
  { id: 'A', label: 'A - Excellent', color: '#22c55e' },
  { id: 'B', label: 'B - Good', color: '#3b82f6' },
  { id: 'C', label: 'C - Average', color: '#f59e0b' },
  { id: 'D', label: 'D - Below Average', color: '#fb923c' },
  { id: 'E', label: 'E - Poor', color: '#ef4444' },
  { id: 'F', label: 'F - Very Poor', color: '#991b1b' },
];

export default function RatingAlertModal({
  visible,
  onClose,
  selectedRating,
  onSelectRating,
}: RatingAlertModalProps) {
  const [tempSelectedRating, setTempSelectedRating] = useState<string>('');

  useEffect(() => {
    if (visible) {
      setTempSelectedRating(selectedRating);
    }
  }, [visible, selectedRating]);

  const handleRatingToggle = (ratingId: string) => {
    if (tempSelectedRating === ratingId) {
      setTempSelectedRating(''); // Deselect if already selected
    } else {
      setTempSelectedRating(ratingId); // Select new rating
    }
  };

  const handleSave = () => {
    if (tempSelectedRating) {
      onSelectRating(tempSelectedRating);
      onClose();
    }
  };

  const handleCancel = () => {
    setTempSelectedRating(selectedRating); // Reset to original selection
    onClose();
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={handleCancel}
    >
      <View style={styles.overlay}>
        <View style={styles.modal}>
          <View style={styles.header}>
            <Text style={styles.title}>Select Rating</Text>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={handleCancel}
            >
              <X size={24} color="#6B7280" />
            </TouchableOpacity>
          </View>

          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.ratingsContainer}
          >
            {ratingOptions.map((rating) => (
              <TouchableOpacity
                key={rating.id}
                style={[
                  styles.ratingPill,
                  tempSelectedRating === rating.id && styles.selectedRatingPill
                ]}
                onPress={() => handleRatingToggle(rating.id)}
              >
                <Text
                  style={[
                    styles.ratingPillText,
                    tempSelectedRating === rating.id && styles.selectedRatingPillText
                  ]}
                >
                  {rating.label}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>

          <View style={styles.actions}>
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={handleCancel}
            >
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.saveButton,
                !tempSelectedRating && styles.saveButtonDisabled
              ]}
              onPress={handleSave}
              disabled={!tempSelectedRating}
            >
              <Text style={[
                styles.saveButtonText,
                !tempSelectedRating && styles.saveButtonTextDisabled
              ]}>
                Save
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  modal: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 24,
    width: '100%',
    maxWidth: 400,
    maxHeight: '70%',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
  },
  closeButton: {
    padding: 4,
  },
  ratingsContainer: {
    paddingVertical: 16,
    gap: 12,
  },
  ratingPill: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
  },
  selectedRatingPill: {
    backgroundColor: '#B89C4C',
  },
  ratingPillText: {
    fontSize: 14,
    color: '#4B5563',
    fontWeight: '500',
    textTransform: 'capitalize',
  },
  selectedRatingPillText: {
    color: '#fff',
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 24,
    gap: 12,
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#D1D5DB',
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#6B7280',
  },
  saveButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    backgroundColor: '#B89C4C',
    alignItems: 'center',
  },
  saveButtonDisabled: {
    backgroundColor: '#D1D5DB',
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#fff',
  },
  saveButtonTextDisabled: {
    color: '#9CA3AF',
  },
});
