import React, { useState } from 'react';
import { View, Image, StyleSheet, Dimensions, TouchableOpacity, Text } from 'react-native';
import Animated, { FadeIn } from 'react-native-reanimated';
import { ChevronLeft, ChevronRight } from 'lucide-react-native';

interface ImageGalleryProps {
  images: { img_url: string }[];
}

const { width: SCREEN_WIDTH } = Dimensions.get('window');

export default function ImageGallery({ images }: ImageGalleryProps) {
  const [currentIndex, setCurrentIndex] = useState(0);

  const handlePrevious = () => {
    setCurrentIndex(prev => (prev > 0 ? prev - 1 : images.length - 1));
  };

  const handleNext = () => {
    setCurrentIndex(prev => (prev < images.length - 1 ? prev + 1 : 0));
  };

  if (!images.length) return null;

  return (
    <View style={styles.container}>
      <Animated.Image
        key={currentIndex}
        entering={FadeIn}
        source={{ uri: images[currentIndex].img_url }}
        style={styles.image}
        resizeMode="cover"
      />
      
      <View style={styles.controls}>
        <TouchableOpacity 
          style={styles.controlButton} 
          onPress={handlePrevious}
        >
          <ChevronLeft size={24} color="#fff" />
        </TouchableOpacity>
        
        <View style={styles.pagination}>
          <Text style={styles.paginationText}>
            {currentIndex + 1} / {images.length}
          </Text>
        </View>
        
        <TouchableOpacity 
          style={styles.controlButton} 
          onPress={handleNext}
        >
          <ChevronRight size={24} color="#fff" />
        </TouchableOpacity>
      </View>

      <View style={styles.thumbnails}>
        {images.map((image, index) => (
          <TouchableOpacity
            key={index}
            onPress={() => setCurrentIndex(index)}
            style={[
              styles.thumbnail,
              currentIndex === index && styles.thumbnailActive
            ]}
          >
            <Image
              source={{ uri: image.img_url }}
              style={styles.thumbnailImage}
            />
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: '100%',
    backgroundColor: '#000',
  },
  image: {
    width: SCREEN_WIDTH,
    height: SCREEN_WIDTH * 0.75,
  },
  controls: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  controlButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  pagination: {
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  paginationText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  thumbnails: {
    flexDirection: 'row',
    padding: 8,
    backgroundColor: '#000',
    gap: 8,
  },
  thumbnail: {
    width: 60,
    height: 60,
    borderRadius: 4,
    overflow: 'hidden',
    opacity: 0.6,
  },
  thumbnailActive: {
    opacity: 1,
    borderWidth: 2,
    borderColor: '#fff',
  },
  thumbnailImage: {
    width: '100%',
    height: '100%',
  },
});