import React from 'react';
import { View } from 'react-native';
import Svg, { Path } from 'react-native-svg';

type Props = {
  width?: number;
  height?: number;
  color?: string;
};

export default function BlinktechLogo({ width = 100, height = 16, color = '#000000' }: Props) {
  return (
    <View>
      <Svg width={width} height={height} viewBox="0 0 100 16" fill="none">
        <Path d="M27.9926 13.9088C27.0012 13.9088 26.1965 13.6809 25.5783 13.2251C24.9601 12.7577 24.5577 12.0915 24.3711 11.2267H23.9512L24.0737 9.47367H24.4061C24.4061 10.0697 24.5227 10.5722 24.756 10.9813C25.0009 11.3903 25.345 11.6942 25.7882 11.8928C26.2431 12.0798 26.7796 12.1733 27.3978 12.1733C28.0393 12.1733 28.5758 12.0857 29.0074 11.9104C29.4506 11.7351 29.783 11.4429 30.0046 11.0339C30.2379 10.6248 30.3545 10.0755 30.3545 9.38602C30.3545 8.68481 30.2379 8.12969 30.0046 7.72065C29.7713 7.31162 29.4389 7.01945 29.0074 6.84414C28.5758 6.65716 28.051 6.56366 27.4328 6.56366C26.4531 6.56366 25.7008 6.7974 25.1759 7.26487C24.6627 7.72065 24.4061 8.41017 24.4061 9.33343H24.0737V7.38758H24.5111C24.686 6.65131 25.0651 6.0436 25.6483 5.56444C26.2431 5.08528 27.0596 4.8457 28.0976 4.8457C28.9957 4.8457 29.748 5.03853 30.3545 5.4242C30.9727 5.79818 31.4392 6.32408 31.7541 7.00192C32.0807 7.67975 32.244 8.47445 32.244 9.38602C32.244 10.2742 32.0807 11.0631 31.7541 11.7526C31.4392 12.4304 30.961 12.9622 30.3195 13.3478C29.6897 13.7218 28.9141 13.9088 27.9926 13.9088ZM24.2486 13.7335H22.5166V1.98828H24.4061V10.7709L24.2486 10.9988V13.7335Z" fill="black" />
        <Path d="M35.7931 13.7335H33.9036V1.98828H35.7931V13.7335Z" fill="black" />
        <Path d="M39.7822 13.7335H37.8927V5.021H39.7822V13.7335ZM39.7822 4.00425H37.8927V1.98828H39.7822V4.00425Z" fill="black" />
        <Path d="M50.9618 13.7335H49.0723V9.00036C49.0723 8.18228 48.8741 7.56873 48.4775 7.15969C48.0926 6.75065 47.4453 6.54613 46.5355 6.54613C45.5908 6.54613 44.891 6.79155 44.4361 7.2824C43.9929 7.76156 43.7713 8.47445 43.7713 9.42108L43.4388 9.43861L43.3514 7.72065H43.7363C43.8296 7.22981 44.0162 6.76818 44.2961 6.33577C44.5877 5.89167 44.9843 5.53522 45.4858 5.26643C45.999 4.98594 46.623 4.8457 47.3578 4.8457C48.1626 4.8457 48.8332 5.00932 49.3698 5.33655C49.9063 5.66378 50.3028 6.10203 50.5594 6.65131C50.8277 7.20059 50.9618 7.81415 50.9618 8.49198V13.7335ZM43.7713 13.7335H41.8818V5.021H43.6138V7.75571L43.7713 7.84337V13.7335Z" fill="black" />
        <Path d="M62.163 13.7335H59.941L57.3692 10.1749H54.0451V8.47445H57.3692L59.976 5.021H62.1805L58.9088 9.21072L62.163 13.7335ZM54.8849 13.7335H52.9954V1.98828H54.8849V13.7335Z" fill="black" />
        <Path d="M69.4057 13.7335H67.6386C66.8455 13.7335 66.204 13.5465 65.7141 13.1725C65.2359 12.7869 64.9968 12.1032 64.9968 11.1215V2.91738H65.7841V11.3143C65.7841 11.9805 65.9532 12.4304 66.2915 12.6642C66.6414 12.8979 67.1546 13.0148 67.8311 13.0148H69.4057V13.7335ZM69.4057 5.80986H63.4047V5.12619H69.4057V5.80986Z" fill="black" />
        <Path d="M75.2153 13.9088C74.2939 13.9088 73.5008 13.716 72.836 13.3303C72.1711 12.9447 71.6579 12.4129 71.2964 11.7351C70.9465 11.0572 70.7715 10.2859 70.7715 9.42108C70.7715 8.56795 70.9465 7.80246 71.2964 7.12463C71.6579 6.44679 72.1711 5.91504 72.836 5.52938C73.5008 5.14372 74.2939 4.95088 75.2153 4.95088C76.0084 4.95088 76.7199 5.10866 77.3498 5.4242C77.9796 5.72806 78.4753 6.178 78.8369 6.77402C79.1984 7.35836 79.3792 8.07126 79.3792 8.91271C79.3792 9.06464 79.3676 9.21072 79.3442 9.35096C79.3326 9.4912 79.3151 9.60807 79.2917 9.70157H71.2439V9.1406H78.9243L78.6269 9.47367C78.6386 9.35681 78.6444 9.23994 78.6444 9.12307C78.6561 8.99451 78.6619 8.86596 78.6619 8.73741C78.6619 7.73234 78.3528 6.96101 77.7347 6.42342C77.1282 5.88583 76.2825 5.61703 75.1978 5.61703C73.9265 5.61703 72.9934 5.95595 72.3986 6.63378C71.8154 7.31162 71.5238 8.2115 71.5238 9.33343V9.4912C71.5238 10.6248 71.8154 11.5305 72.3986 12.2084C72.9818 12.8862 73.9148 13.2251 75.1978 13.2251C76.3059 13.2251 77.1282 13.0206 77.6647 12.6116C78.2129 12.2025 78.487 11.6416 78.487 10.9287V10.8235H79.2568V10.9287C79.2568 11.513 79.076 12.0331 78.7144 12.4889C78.3645 12.933 77.8805 13.2836 77.2623 13.5407C76.6558 13.7861 75.9735 13.9088 75.2153 13.9088Z" fill="black" />
        <Path d="M85.5089 13.9088C84.6224 13.9088 83.841 13.716 83.1645 13.3303C82.4997 12.933 81.9807 12.3954 81.6074 11.7175C81.2459 11.0397 81.0651 10.2742 81.0651 9.42108C81.0651 8.56795 81.2459 7.80246 81.6074 7.12463C81.9807 6.44679 82.4997 5.91504 83.1645 5.52938C83.841 5.14372 84.6224 4.95088 85.5089 4.95088C86.267 4.95088 86.961 5.09697 87.5908 5.38914C88.2207 5.66962 88.7222 6.06697 89.0954 6.58119C89.4803 7.09541 89.6728 7.69144 89.6728 8.36927V8.47445H88.903V8.3868C88.903 7.53367 88.6056 6.86752 88.0107 6.38836C87.4275 5.89751 86.5936 5.65209 85.5089 5.65209C84.2259 5.65209 83.2928 5.99685 82.7096 6.68637C82.1264 7.36421 81.8349 8.27578 81.8349 9.42108C81.8349 10.5664 82.1264 11.4838 82.7096 12.1733C83.2928 12.8512 84.2259 13.1901 85.5089 13.1901C86.5936 13.1901 87.4275 12.9505 88.0107 12.4713C88.6056 11.9805 88.903 11.3143 88.903 10.4729V10.3677H89.6728V10.4729C89.6728 11.139 89.4803 11.7351 89.0954 12.261C88.7222 12.7752 88.2207 13.1784 87.5908 13.4706C86.961 13.7627 86.267 13.9088 85.5089 13.9088Z" fill="black" />
        <Path d="M100.001 13.7335H99.2311V8.77247C99.2311 7.69728 98.9745 6.90842 98.4613 6.40589C97.9481 5.90336 97.1783 5.65209 96.1519 5.65209C95.3238 5.65209 94.6473 5.83324 94.1225 6.19553C93.6093 6.54613 93.236 7.03113 93.0028 7.65053C92.7695 8.26993 92.6528 8.98283 92.6528 9.78922H92.3029L92.4604 7.75571H92.7053C92.8103 7.27656 93.0086 6.82661 93.3002 6.40589C93.5918 5.97348 93.9883 5.62287 94.4899 5.35408C94.9914 5.08528 95.6096 4.95088 96.3444 4.95088C97.1492 4.95088 97.8256 5.12034 98.3738 5.45926C98.922 5.78649 99.3302 6.24227 99.5985 6.82661C99.8668 7.41095 100.001 8.0771 100.001 8.82506V13.7335ZM92.6528 13.7335H91.8656V1.98828H92.6528V13.7335Z" fill="black" />
        <Path d="M15.8942 7.94702C11.5051 7.94702 7.94711 4.38901 7.94711 0H9.27163C12.9292 0 15.8942 2.96502 15.8942 6.62252V7.94702Z" fill="black" />
        <Path d="M7.94711 15.894C7.94711 11.505 11.5051 7.94702 15.8942 7.94702V9.27152C15.8942 12.929 12.9292 15.894 9.27163 15.894H7.94711Z" fill="black" />
        <Path d="M0 7.94702C4.38908 7.94702 7.94711 4.38901 7.94711 0H6.62259C2.96505 0 0 2.96502 0 6.62252V7.94702Z" fill="black" />
        <Path d="M0 7.94702V9.27152C0 12.929 2.96505 15.894 6.62259 15.894H7.94711C7.94711 11.505 4.38908 7.94702 0 7.94702Z" fill="black" />
      </Svg>
    </View>
  );
}
