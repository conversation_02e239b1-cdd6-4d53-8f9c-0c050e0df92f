import React, { useState, useMemo, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Modal, FlatList, TextInput } from 'react-native';
import { ChevronDown, Check, Search } from 'lucide-react-native';

interface DropdownOption {
  id: string | number;
  label: string;
  value?: any;
}

interface MultiSelectDropdownProps {
  label?: string;
  placeholder?: string;
  options: DropdownOption[];
  selectedValues?: (string | number)[];
  onSelect: (selectedIds: (string | number)[]) => void;
  error?: string;
  searchable?: boolean;
  searchPlaceholder?: string;
  showConfirmButton?: boolean;
  confirmButtonText?: string;
}

export default function MultiSelectDropdown({
  label,
  placeholder = "Please select",
  options,
  selectedValues = [],
  onSelect,
  error,
  searchable = false,
  searchPlaceholder = "Search...",
  showConfirmButton = false,
  confirmButtonText = "OK",
}: MultiSelectDropdownProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [tempSelectedValues, setTempSelectedValues] = useState<(string | number)[]>(selectedValues);

  const selectedOptions = options.filter(option => selectedValues.includes(option.id));

  // Sync temp values when selectedValues change
  useEffect(() => {
    setTempSelectedValues(selectedValues);
  }, [selectedValues]);

  // Filter options based on search query
  const filteredOptions = useMemo(() => {
    if (!searchable || !searchQuery.trim()) {
      return options;
    }
    return options.filter(option =>
      option.label.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [options, searchQuery, searchable]);

  const handleSelect = (option: DropdownOption) => {
    const currentValues = showConfirmButton ? tempSelectedValues : selectedValues;
    const isSelected = currentValues.includes(option.id);
    let newSelectedValues: (string | number)[];

    if (isSelected) {
      // Remove from selection
      newSelectedValues = currentValues.filter(id => id !== option.id);
    } else {
      // Add to selection
      newSelectedValues = [...currentValues, option.id];
    }

    if (showConfirmButton) {
      setTempSelectedValues(newSelectedValues);
    } else {
      onSelect(newSelectedValues);
    }
  };

  const handleModalClose = () => {
    setIsVisible(false);
    setSearchQuery(''); // Reset search when closing
    if (showConfirmButton) {
      setTempSelectedValues(selectedValues); // Reset temp values
    }
  };

  const handleConfirm = () => {
    if (showConfirmButton) {
      onSelect(tempSelectedValues);
    }
    setIsVisible(false);
    setSearchQuery('');
  };

  const handleCancel = () => {
    setTempSelectedValues(selectedValues); // Reset to original values
    setIsVisible(false);
    setSearchQuery('');
  };

  const getDisplayText = () => {
    if (selectedOptions.length === 0) {
      return placeholder;
    } else if (selectedOptions.length === 1) {
      return selectedOptions[0].label;
    } else {
      return `${selectedOptions.length} selected`;
    }
  };

  const renderOption = ({ item }: { item: DropdownOption }) => {
    const currentValues = showConfirmButton ? tempSelectedValues : selectedValues;
    const isSelected = currentValues.includes(item.id);
    
    return (
      <TouchableOpacity
        style={[
          styles.option,
          isSelected && styles.optionSelected,
        ]}
        onPress={() => handleSelect(item)}
      >
        <Text style={[
          styles.optionText,
          isSelected && styles.optionTextSelected,
        ]}>
          {item.label}
        </Text>
        {isSelected && (
          <Check size={16} color="#B89C4C" />
        )}
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      {label && <Text style={styles.label}>{label}</Text>}
      
      <TouchableOpacity
        style={[
          styles.selector,
          error && styles.selectorError,
        ]}
        onPress={() => setIsVisible(true)}
      >
        <Text style={[
          styles.selectorText,
          selectedOptions.length === 0 && styles.placeholderText,
        ]}>
          {getDisplayText()}
        </Text>
        <ChevronDown size={20} color="#6B7280" />
      </TouchableOpacity>

      {error && <Text style={styles.errorText}>{error}</Text>}

      <Modal
        visible={isVisible}
        transparent
        animationType="fade"
        onRequestClose={handleModalClose}
      >
        <TouchableOpacity
          style={styles.overlay}
          activeOpacity={1}
          onPress={handleModalClose}
        >
          <View style={styles.modal}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>{label || 'Select Options'}</Text>
              {searchable && (
                <View style={styles.searchContainer}>
                  <Search size={16} color="#6B7280" style={styles.searchIcon} />
                  <TextInput
                    style={styles.searchInput}
                    placeholder={searchPlaceholder}
                    value={searchQuery}
                    onChangeText={setSearchQuery}
                    autoCapitalize="none"
                    autoCorrect={false}
                  />
                </View>
              )}
            </View>
            <FlatList
              data={filteredOptions}
              renderItem={renderOption}
              keyExtractor={(item) => item.id.toString()}
              style={styles.optionsList}
              showsVerticalScrollIndicator={false}
              ListEmptyComponent={
                searchQuery.trim() ? (
                  <View style={styles.emptyContainer}>
                    <Text style={styles.emptyText}>No results found</Text>
                  </View>
                ) : null
              }
            />
            {showConfirmButton && (
              <View style={styles.buttonContainer}>
                <TouchableOpacity
                  style={[styles.button, styles.cancelButton]}
                  onPress={handleCancel}
                >
                  <Text style={styles.cancelButtonText}>Cancel</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.button, styles.confirmButton]}
                  onPress={handleConfirm}
                >
                  <Text style={styles.confirmButtonText}>{confirmButtonText}</Text>
                </TouchableOpacity>
              </View>
            )}
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 8,
  },
  selector: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#F9FAFB',
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    minHeight: 48,
  },
  selectorError: {
    borderColor: '#EF4444',
  },
  selectorText: {
    fontSize: 16,
    color: '#111827',
    flex: 1,
  },
  placeholderText: {
    color: '#9CA3AF',
  },
  errorText: {
    fontSize: 12,
    color: '#EF4444',
    marginTop: 4,
  },
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modal: {
    backgroundColor: 'white',
    borderRadius: 12,
    width: '100%',
    maxWidth: 400,
    maxHeight: '80%',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalHeader: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    textAlign: 'center',
    marginBottom: 12,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#111827',
  },
  optionsList: {
    maxHeight: 300,
  },
  option: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  optionSelected: {
    backgroundColor: '#FEF3C7',
  },
  optionText: {
    fontSize: 16,
    color: '#374151',
    flex: 1,
  },
  optionTextSelected: {
    color: '#92400E',
    fontWeight: '500',
  },
  emptyContainer: {
    padding: 20,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    color: '#6B7280',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    gap: 12,
  },
  button: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: '#F3F4F6',
    borderWidth: 1,
    borderColor: '#D1D5DB',
  },
  confirmButton: {
    backgroundColor: '#B89C4C',
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#374151',
  },
  confirmButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#FFFFFF',
  },
});
