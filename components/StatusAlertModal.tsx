import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Dimensions,
  TextInput,
  Switch,
  Platform,
  FlatList,
} from 'react-native';
import DateTimePicker from '@react-native-community/datetimepicker';
import { X, Calendar } from 'lucide-react-native';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import {
  fetchListingTypes,
  fetchGeographies,
  fetchBedrooms,
  fetchListings,
  api
} from '@/lib/api';
import Dropdown from '@/components/Dropdown';
import MultiSelectDropdown from '@/components/MultiSelectDropdown';
import ListingCard from '@/components/ListingCard';
import Pagination from '@/components/Pagination';
import {
  RENT_SALE_OPTIONS,
  MIN_AREA_OPTIONS,
  MAX_AREA_OPTIONS,
  PRICE_MIN_OPTIONS,
  PRICE_MAX_OPTIONS,
  transformApiDataToDropdownOptions
} from '@/constants/dropdownOptions';

interface StatusOption {
  id: number;
  name: string;
  background_color: string;
  is_disabled: number;
}

interface MeetingConfig {
  title: string;
  content: string;
  dueDate: string;
  priority: string;
  sendEmail: boolean;
  remarks: string;
}

interface FollowUpConfig {
  title: string;
  content: string;
  dueDate: string;
  priority: string;
  sendEmail: boolean;
  remarks: string;
}

interface OfferNegotiationConfig {
  search: string;
  rentSale: string;
  towerBuilding: string;
  bedrooms: string[];
  minArea: string;
  maxArea: string;
  priceMin: string;
  priceMax: string;
}

interface ViewingConfig {
  search: string;
  rentSale: string;
  towerBuilding: string;
  bedrooms: string[];
  minArea: string;
  maxArea: string;
  priceMin: string;
  priceMax: string;
  selectedProperties: string[]; // Array of listing IDs
}

interface StatusAlertModalProps {
  visible: boolean;
  onClose: () => void;
  statuses: StatusOption[];
  selectedStatus: string;
  onSelectStatus: (statusId: string, meetingConfig?: MeetingConfig, viewingConfig?: ViewingConfig, followUpConfig?: FollowUpConfig, offerNegotiationConfig?: OfferNegotiationConfig) => void;
}

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

export default function StatusAlertModal({
  visible,
  onClose,
  statuses,
  selectedStatus,
  onSelectStatus,
}: StatusAlertModalProps) {
  const [tempSelectedStatus, setTempSelectedStatus] = useState(selectedStatus);
  const [showMeetingConfig, setShowMeetingConfig] = useState(false);
  const [showViewingConfig, setShowViewingConfig] = useState(false);
  const [showFollowUpConfig, setShowFollowUpConfig] = useState(false);
  const [showOfferNegotiationConfig, setShowOfferNegotiationConfig] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [datePickerMode, setDatePickerMode] = useState<'date' | 'time'>('date');
  const [currentConfigType, setCurrentConfigType] = useState<'meeting' | 'followUp'>('meeting');

  // Fetch dropdown data for viewing configuration
  const { data: geographies = [] } = useQuery({
    queryKey: ['geographies'],
    queryFn: fetchGeographies,
  });

  const { data: listingTypes = [] } = useQuery({
    queryKey: ['listingTypes'],
    queryFn: fetchListingTypes,
  });

  const { data: bedrooms = [] } = useQuery({
    queryKey: ['bedrooms'],
    queryFn: fetchBedrooms,
  });

  // Transform API data to dropdown format - only using available endpoints
  const locationOptions = transformApiDataToDropdownOptions(geographies);
  const bedroomOptions = transformApiDataToDropdownOptions(bedrooms);

  // All dropdown options are now imported from constants

  const [meetingConfig, setMeetingConfig] = useState<MeetingConfig>({
    title: 'Meeting appointment',
    content: 'Meeting appointment reminder.',
    dueDate: '',
    priority: 'Low',
    sendEmail: false,
    remarks: '',
  });
  const [viewingConfig, setViewingConfig] = useState<ViewingConfig>({
    search: '',
    rentSale: '',
    towerBuilding: '',
    bedrooms: [],
    minArea: '',
    maxArea: '',
    priceMin: '',
    priceMax: '',
    selectedProperties: [],
  });

  // Pagination constants and state
  const ITEMS_PER_PAGE = 10;
  const queryClient = useQueryClient();
  const [currentViewingPage, setCurrentViewingPage] = useState(1);
  const [currentOfferPage, setCurrentOfferPage] = useState(1);
  const [followUpConfig, setFollowUpConfig] = useState<FollowUpConfig>({
    title: 'Follow up',
    content: 'Follow up reminder.',
    dueDate: '',
    priority: 'Low',
    sendEmail: false,
    remarks: '',
  });
  const [offerNegotiationConfig, setOfferNegotiationConfig] = useState<OfferNegotiationConfig>({
    search: '',
    rentSale: '',
    towerBuilding: '',
    bedrooms: [],
    minArea: '',
    maxArea: '',
    priceMin: '',
    priceMax: '',
  });
  const enabledStatuses = statuses.filter(status => status.is_disabled === 0);

  // Get selected location object for towers query
  const selectedLocation = geographies.find((geo: any) => geo.id?.toString() === viewingConfig.search);

  const { data: towers = [{ id: 'any', name: 'Any' }] } = useQuery({
    queryKey: ['towers', selectedLocation?.id],
    queryFn: async () => {
      if (!selectedLocation?.id) {
        return [{ id: 'any', name: 'Any' }];
      }
      try {
        const { data } = await api.get(`/geography/${selectedLocation.id}/towers`);
        return [{ id: 'any', name: 'Any' }, ...data];
      } catch (error) {
        console.error('Error fetching towers:', error);
        return [{ id: 'any', name: 'Any' }];
      }
    },
    enabled: true, // Always enabled, but returns default when no location
  });

  // Transform towers to dropdown format
  const towerBuildingOptions = transformApiDataToDropdownOptions(towers);

  // Fallback to static options if no towers
  const finalTowerOptions = towerBuildingOptions.length > 0 ? towerBuildingOptions : [
    { id: 'any', label: 'Any' },
    { id: 'test', label: 'Test Tower' }
  ];

  // Create filters object for listings API
  const createListingFilters = (config: ViewingConfig | OfferNegotiationConfig) => {
    const filters: any = {};

    // Map location
    if (config.search) {
      filters.location = config.search;
    }

    // Map rent/sale to adType
    if (config.rentSale) {
      const rentSaleMap: any = {
        'rent': 'rent',
        'sale': 'sale',
        'rent_sale': 'All'
      };
      filters.adType = rentSaleMap[config.rentSale] || 'All';
    }

    return filters;
  };

  // Check if any filters are applied
  const hasFilters = viewingConfig?.search || viewingConfig?.rentSale || viewingConfig?.towerBuilding ||
                    (viewingConfig?.bedrooms && viewingConfig.bedrooms.length > 0) || viewingConfig?.minArea || viewingConfig?.maxArea ||
                    viewingConfig?.priceMin || viewingConfig?.priceMax;

  // Create query key without selectedProperties to avoid re-fetching on selection changes
  const viewingConfigForQuery = {
    search: viewingConfig.search,
    rentSale: viewingConfig.rentSale,
    towerBuilding: viewingConfig.towerBuilding,
    bedrooms: viewingConfig.bedrooms,
    minArea: viewingConfig.minArea,
    maxArea: viewingConfig.maxArea,
    priceMin: viewingConfig.priceMin,
    priceMax: viewingConfig.priceMax,
    // Exclude selectedProperties from query key
  };

  // Fetch listings with pagination
  const { data: listingsData, isLoading: isLoadingListings } = useQuery({
    queryKey: ['modal-listings', viewingConfigForQuery, currentViewingPage],
    queryFn: () => {
      // If no filters applied, fetch all listings with default inventory filters
      if (!hasFilters) {
        const params = {
          propertyType: undefined as number | undefined,
          adType: 'All' as string,
          vt: 'master' as string
        };
        console.log('🔍 Fetching listings page', currentViewingPage, 'with default params:', params);
        return fetchListings(currentViewingPage, params);
      }
      // If filters applied, use them with vt filter
      const filters = createListingFilters(viewingConfig);
      const params = {
        ...filters,
        vt: 'master',
        adType: filters.adType || 'All'
      };
      console.log('🔍 Fetching listings page', currentViewingPage, 'with filtered params:', params);
      return fetchListings(currentViewingPage, params);
    },
    enabled: showViewingConfig, // Only fetch when viewing config is shown
  });

  // Check if any offer negotiation filters are applied
  const hasOfferFilters = offerNegotiationConfig?.search || offerNegotiationConfig?.rentSale ||
                         offerNegotiationConfig?.towerBuilding || (offerNegotiationConfig?.bedrooms && offerNegotiationConfig.bedrooms.length > 0) ||
                         offerNegotiationConfig?.minArea || offerNegotiationConfig?.maxArea ||
                         offerNegotiationConfig?.priceMin || offerNegotiationConfig?.priceMax;

  // Fetch offer negotiation listings with pagination
  const { data: offerListingsData, isLoading: isLoadingOfferListings } = useQuery({
    queryKey: ['modal-offer-listings', offerNegotiationConfig, currentOfferPage],
    queryFn: () => {
      // If no filters applied, fetch all listings with default inventory filters
      if (!hasOfferFilters) {
        const params = {
          propertyType: undefined as number | undefined,
          adType: 'All' as string,
          vt: 'master' as string
        };
        console.log('Fetching offer listings page', currentOfferPage, 'with default params:', params);
        return fetchListings(currentOfferPage, params);
      }
      // If filters applied, use them with vt filter
      const filters = createListingFilters(offerNegotiationConfig);
      const params = {
        ...filters,
        vt: 'master',
        adType: filters.adType || 'All'
      };
      console.log('Fetching offer listings page', currentOfferPage, 'with filtered params:', params);
      return fetchListings(currentOfferPage, params);
    },
    enabled: showOfferNegotiationConfig, // Only fetch when offer config is shown
  });

  // Extract listings from normal query response
  const listings = listingsData?.data || [];
  const totalListings = listingsData?.total || 0;
  const totalViewingPages = Math.ceil(totalListings / ITEMS_PER_PAGE);

  const offerListings = offerListingsData?.data || [];
  const totalOfferListings = offerListingsData?.total || 0;
  const totalOfferPages = Math.ceil(totalOfferListings / ITEMS_PER_PAGE);

  // Debug logs (only when viewing config is shown)
  if (showViewingConfig) {
    console.log('=== VIEWING CONFIG DEBUG ===');
    console.log('Viewing config:', viewingConfig);
    console.log('Has filters:', hasFilters);
    console.log('Current page:', currentViewingPage);
    console.log('Listings count (loaded):', listings.length);
    console.log('Total listings (from API):', totalListings);
    console.log('Total pages:', totalViewingPages);
    console.log('Is loading listings:', isLoadingListings);
    console.log('============================');
  }

  // Debug logs for offer negotiation
  if (showOfferNegotiationConfig) {
    console.log('=== OFFER NEGOTIATION DEBUG ===');
    console.log('Offer config:', offerNegotiationConfig);
    console.log('Has offer filters:', hasOfferFilters);
    console.log('Current offer page:', currentOfferPage);
    console.log('Offer listings count (loaded):', offerListings.length);
    console.log('Total offer listings (from API):', totalOfferListings);
    console.log('Total offer pages:', totalOfferPages);
    console.log('Is loading offer listings:', isLoadingOfferListings);
    console.log('================================');
  }

  useEffect(() => {
    setTempSelectedStatus(selectedStatus);
  }, [selectedStatus, visible]);

  const formatStatusName = (name: string) => {
    return name.replace(/_/g, ' ');
  };

  const handleStatusToggle = (statusId: string) => {
    // Always select the new status (no deselection)
    setTempSelectedStatus(statusId);

    // Reset all filters and queries when switching status
    console.log('🔄 Status changed - resetting all filters and queries...');
    resetAllFiltersAndQueries();

    // Check if MEETING_SCHEDULED, VIEWING_SCHEDULED, FOLLOW_UP, or OFFER_NEGOTIATION is selected
    const selectedStatusObj = statuses.find(s => s.id.toString() === statusId);
    if (selectedStatusObj && selectedStatusObj.name === 'MEETING_SCHEDULED') {
      setShowMeetingConfig(true);
      setShowViewingConfig(false);
      setShowFollowUpConfig(false);
      setShowOfferNegotiationConfig(false);
    } else if (selectedStatusObj && selectedStatusObj.name === 'VIEWING_SCHEDULED') {
      setShowViewingConfig(true);
      setShowMeetingConfig(false);
      setShowFollowUpConfig(false);
      setShowOfferNegotiationConfig(false);
    } else if (selectedStatusObj && selectedStatusObj.name === 'FOLLOW_UP') {
      setShowFollowUpConfig(true);
      setShowMeetingConfig(false);
      setShowViewingConfig(false);
      setShowOfferNegotiationConfig(false);
    } else if (selectedStatusObj && selectedStatusObj.name === 'OFFER_NEGOTIATION') {
      setShowOfferNegotiationConfig(true);
      setShowMeetingConfig(false);
      setShowViewingConfig(false);
      setShowFollowUpConfig(false);
    } else {
      setShowMeetingConfig(false);
      setShowViewingConfig(false);
      setShowFollowUpConfig(false);
      setShowOfferNegotiationConfig(false);
    }
  };

  const handleSave = () => {
    if (tempSelectedStatus) {
      const selectedStatusObj = statuses.find(s => s.id.toString() === tempSelectedStatus);
      if (selectedStatusObj && selectedStatusObj.name === 'MEETING_SCHEDULED') {
        onSelectStatus(tempSelectedStatus, meetingConfig);
      } else if (selectedStatusObj && selectedStatusObj.name === 'VIEWING_SCHEDULED') {
        onSelectStatus(tempSelectedStatus, undefined, viewingConfig);
      } else if (selectedStatusObj && selectedStatusObj.name === 'FOLLOW_UP') {
        onSelectStatus(tempSelectedStatus, undefined, undefined, followUpConfig);
      } else if (selectedStatusObj && selectedStatusObj.name === 'OFFER_NEGOTIATION') {
        onSelectStatus(tempSelectedStatus, undefined, undefined, undefined, offerNegotiationConfig);
      } else {
        onSelectStatus(tempSelectedStatus);
      }
    }
    onClose();
  };

  const updateMeetingConfig = (field: keyof MeetingConfig, value: string | boolean) => {
    setMeetingConfig(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const updateViewingConfig = (field: keyof ViewingConfig, value: string | string[] | (string | number)[]) => {
    setViewingConfig(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const updateFollowUpConfig = (field: keyof FollowUpConfig, value: string | boolean) => {
    setFollowUpConfig(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const updateOfferNegotiationConfig = (field: keyof OfferNegotiationConfig, value: string | string[] | (string | number)[]) => {
    setOfferNegotiationConfig(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle listing selection - optimized with useCallback
  const toggleListingSelection = useCallback((listingId: string) => {
    console.log('✅ Checkbox toggle for listing:', listingId);
    setViewingConfig(prev => ({
      ...prev,
      selectedProperties: prev.selectedProperties.includes(listingId)
        ? prev.selectedProperties.filter(id => id !== listingId)
        : [...prev.selectedProperties, listingId]
    }));
  }, []);

  // Optimized render item for FlatList
  const renderListingItem = useCallback(({ item }: { item: any }) => (
    <TouchableOpacity
      style={styles.listingItem}
      onPress={() => toggleListingSelection(item.id.toString())}
      activeOpacity={0.7}
    >
      <View style={styles.listingCheckboxContainer}>
        <View
          style={[
            styles.listingCheckbox,
            viewingConfig.selectedProperties.includes(item.id.toString()) && styles.listingCheckboxSelected
          ]}
        >
          {viewingConfig.selectedProperties.includes(item.id.toString()) && (
            <Text style={styles.listingCheckmark}>✓</Text>
          )}
        </View>
      </View>
      <View style={styles.listingCardContainer}>
        <ListingCard
          listing={item}
          disableNavigation={true}
        />
      </View>
    </TouchableOpacity>
  ), [viewingConfig.selectedProperties, toggleListingSelection]);

  // Optimized getItemLayout for better performance
  const getItemLayout = useCallback((_: any, index: number) => ({
    length: 120, // Approximate height of each listing item
    offset: 120 * index,
    index,
  }), []);

  // Reset viewing filters and restart from beginning
  const resetViewingFilters = () => {
    console.log('🔄 Resetting viewing filters and restarting from beginning...');
    setViewingConfig({
      search: '',
      rentSale: '',
      towerBuilding: '',
      bedrooms: [],
      minArea: '',
      maxArea: '',
      priceMin: '',
      priceMax: '',
      selectedProperties: [],
    });
    // Reset to first page and remove cached data
    setCurrentViewingPage(1);
    queryClient.removeQueries({ queryKey: ['modal-listings'] });
  };

  // Reset offer negotiation filters and restart from beginning
  const resetOfferNegotiationFilters = () => {
    console.log('🔄 Resetting offer negotiation filters and restarting from beginning...');
    setOfferNegotiationConfig({
      search: '',
      rentSale: '',
      towerBuilding: '',
      bedrooms: [],
      minArea: '',
      maxArea: '',
      priceMin: '',
      priceMax: '',
    });
    // Reset to first page and remove cached data
    setCurrentOfferPage(1);
    queryClient.removeQueries({ queryKey: ['modal-offer-listings'] });
  };

  // Handle page changes
  const handleViewingPageChange = (page: number) => {
    setCurrentViewingPage(page);
  };

  const handleOfferPageChange = (page: number) => {
    setCurrentOfferPage(page);
  };

  // Reset all filters and queries when modal closes or status changes
  const resetAllFiltersAndQueries = () => {
    console.log('🔄 Resetting all filters and queries...');

    // Reset viewing config
    setViewingConfig({
      search: '',
      rentSale: '',
      towerBuilding: '',
      bedrooms: [],
      minArea: '',
      maxArea: '',
      priceMin: '',
      priceMax: '',
      selectedProperties: [],
    });

    // Reset offer negotiation config
    setOfferNegotiationConfig({
      search: '',
      rentSale: '',
      towerBuilding: '',
      bedrooms: [],
      minArea: '',
      maxArea: '',
      priceMin: '',
      priceMax: '',
    });

    // Reset pagination to first page
    setCurrentViewingPage(1);
    setCurrentOfferPage(1);

    // Remove all cached queries to start fresh
    queryClient.removeQueries({ queryKey: ['modal-listings'] });
    queryClient.removeQueries({ queryKey: ['modal-offer-listings'] });
  };

  const handleDatePress = (configType: 'meeting' | 'followUp') => {
    console.log('Date picker pressed for:', configType);
    setCurrentConfigType(configType);
    setDatePickerMode('date');
    setShowDatePicker(true);
  };

  const handleDateChange = (_event: any, selectedDate?: Date) => {
    // Închide date picker-ul automat după selecție
    setShowDatePicker(false);

    if (selectedDate) {
      const formattedDate = selectedDate.toLocaleDateString('en-GB', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
      }) + ', ' + selectedDate.toLocaleTimeString('en-GB', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false,
      });

      if (currentConfigType === 'meeting') {
        updateMeetingConfig('dueDate', formattedDate);
      } else if (currentConfigType === 'followUp') {
        updateFollowUpConfig('dueDate', formattedDate);
      }
    }
  };

  const handleClose = () => {
    console.log('🚪 Closing modal - resetting all filters and queries...');
    setTempSelectedStatus(selectedStatus); // Reset to original selection
    setShowMeetingConfig(false);
    setShowViewingConfig(false);
    setShowFollowUpConfig(false);
    setShowOfferNegotiationConfig(false);
    setMeetingConfig({
      title: 'Meeting appointment',
      content: 'Meeting appointment reminder.',
      dueDate: '',
      priority: 'Low',
      sendEmail: false,
      remarks: '',
    });
    setViewingConfig({
      search: '',
      rentSale: '',
      towerBuilding: '',
      bedrooms: [],
      minArea: '',
      maxArea: '',
      priceMin: '',
      priceMax: '',
      selectedProperties: [],
    });
    setFollowUpConfig({
      title: 'Follow up',
      content: 'Follow up reminder.',
      dueDate: '',
      priority: 'Low',
      sendEmail: false,
      remarks: '',
    });
    setOfferNegotiationConfig({
      search: '',
      rentSale: '',
      towerBuilding: '',
      bedrooms: [],
      minArea: '',
      maxArea: '',
      priceMin: '',
      priceMax: '',
    });

    // Remove all cached queries to start fresh next time
    queryClient.removeQueries({ queryKey: ['modal-listings'] });
    queryClient.removeQueries({ queryKey: ['modal-offer-listings'] });

    onClose();
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={handleClose}
    >
      <View style={styles.overlay}>
        <View style={[
          styles.modalContainer,
          (showMeetingConfig || showViewingConfig || showFollowUpConfig || showOfferNegotiationConfig) && styles.modalContainerExpanded
        ]}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.title}>Select Status</Text>
            <TouchableOpacity onPress={handleClose} style={styles.closeButton}>
              <X size={20} color="#6B7280" />
            </TouchableOpacity>
          </View>

          {/* Status Pills */}
          <ScrollView
            horizontal
            style={styles.statusScrollContainer}
            contentContainerStyle={styles.statusScrollContent}
            showsHorizontalScrollIndicator={false}
          >
            {enabledStatuses.map((status) => (
              <TouchableOpacity
                key={status.id}
                style={[
                  styles.statusPill,
                  tempSelectedStatus === status.id.toString() && styles.selectedStatusPill
                ]}
                onPress={() => handleStatusToggle(status.id.toString())}
              >
                <Text
                  style={[
                    styles.statusPillText,
                    tempSelectedStatus === status.id.toString() && styles.selectedStatusPillText
                  ]}
                >
                  {formatStatusName(status.name)}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>

          {/* Meeting Configuration */}
          {showMeetingConfig && (
            <ScrollView style={styles.meetingConfigContainer}>
              <Text style={styles.meetingConfigTitle}>Lead configuration</Text>

              {/* Reminder Section */}
              <Text style={styles.sectionTitle}>Reminder</Text>

              {/* Title */}
              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>Title</Text>
                <TextInput
                  style={styles.textInput}
                  value={meetingConfig.title}
                  onChangeText={(text) => updateMeetingConfig('title', text)}
                  placeholder="Meeting appointment"
                />
              </View>

              {/* Content */}
              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>Content</Text>
                <TextInput
                  style={[styles.textInput, styles.textArea]}
                  value={meetingConfig.content}
                  onChangeText={(text) => updateMeetingConfig('content', text)}
                  placeholder="Meeting appointment reminder."
                  multiline
                  numberOfLines={3}
                />
              </View>

              {/* Due Date */}
              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>Due Date</Text>
                <View style={styles.dateInputContainer}>
                  <TextInput
                    style={[styles.textInput, styles.dateInput]}
                    value={meetingConfig.dueDate}
                    onChangeText={(text) => updateMeetingConfig('dueDate', text)}
                    placeholder="dd.mm.yyyy, --:--"
                  />
                  <TouchableOpacity
                    style={styles.calendarButton}
                    onPress={() => handleDatePress('meeting')}
                  >
                    <Calendar size={20} color="#6B7280" />
                  </TouchableOpacity>
                </View>
                {/* Inline Date Picker for Meeting */}
                {showDatePicker && currentConfigType === 'meeting' && (
                  <View style={styles.inlineDatePicker}>
                    <DateTimePicker
                      value={new Date()}
                      mode="date"
                      display="default"
                      onChange={handleDateChange}
                    />
                  </View>
                )}
                {!meetingConfig.dueDate && (
                  <Text style={styles.errorText}>Due Date is required.</Text>
                )}
              </View>

              {/* Priority */}
              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>Priority</Text>
                <TouchableOpacity style={styles.dropdownButton}>
                  <Text style={styles.dropdownText}>{meetingConfig.priority}</Text>
                  <Text style={styles.dropdownArrow}>▼</Text>
                </TouchableOpacity>
              </View>

              {/* Send Email */}
              <View style={styles.checkboxContainer}>
                <Switch
                  value={meetingConfig.sendEmail}
                  onValueChange={(value) => updateMeetingConfig('sendEmail', value)}
                  trackColor={{ false: '#E5E7EB', true: '#B89C4C' }}
                  thumbColor={meetingConfig.sendEmail ? '#fff' : '#fff'}
                />
                <Text style={styles.checkboxLabel}>Send Email</Text>
              </View>

              {/* Remarks Section */}
              <Text style={styles.sectionTitle}>Remarks</Text>
              <TextInput
                style={[styles.textInput, styles.textArea]}
                value={meetingConfig.remarks}
                onChangeText={(text) => updateMeetingConfig('remarks', text)}
                placeholder="Remarks"
                multiline
                numberOfLines={3}
              />
            </ScrollView>
          )}

          {/* Viewing Configuration */}
          {showViewingConfig && (
            <ScrollView style={styles.meetingConfigContainer}>
              <Text style={styles.meetingConfigTitle}>Lead configuration</Text>

              {/* Location Row */}
              <View style={styles.fullWidthContainer}>
                <Text style={styles.inputLabel}>Location</Text>
                <ScrollView
                  horizontal
                  style={styles.pillScrollContainer}
                  contentContainerStyle={styles.pillScrollContent}
                  showsHorizontalScrollIndicator={false}
                >
                  {locationOptions.map((option) => (
                    <TouchableOpacity
                      key={option.id}
                      style={[
                        styles.filterPill,
                        viewingConfig.search === option.id.toString() && styles.selectedFilterPill
                      ]}
                      onPress={() => {
                        // Allow deselection - if already selected, deselect it
                        const newValue = viewingConfig.search === option.id.toString() ? '' : option.id.toString();
                        updateViewingConfig('search', newValue);
                        // Reset tower/building when location changes or is deselected
                        updateViewingConfig('towerBuilding', '');
                      }}
                    >
                      <Text
                        style={[
                          styles.filterPillText,
                          viewingConfig.search === option.id.toString() && styles.selectedFilterPillText
                        ]}
                      >
                        {option.label}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </ScrollView>
              </View>

              {/* Tower/Building Row - Only show when location is selected */}
              {viewingConfig.search && (
                <View style={styles.fullWidthContainer}>
                  <Text style={styles.inputLabel}>Tower/Building</Text>
                  <ScrollView
                    horizontal
                    style={styles.pillScrollContainer}
                    contentContainerStyle={styles.pillScrollContent}
                    showsHorizontalScrollIndicator={false}
                  >
                    {finalTowerOptions.map((option) => {
                      const isSelected = viewingConfig.towerBuilding === option.id.toString();
                      return (
                        <TouchableOpacity
                          key={option.id}
                          style={[
                            styles.filterPill,
                            isSelected && styles.selectedFilterPill
                          ]}
                          onPress={() => {
                            // Allow deselection - if already selected, deselect it
                            const newValue = isSelected ? '' : option.id.toString();
                            updateViewingConfig('towerBuilding', newValue);
                          }}
                        >
                          <Text
                            style={[
                              styles.filterPillText,
                              isSelected && styles.selectedFilterPillText
                            ]}
                          >
                            {option.label}
                          </Text>
                        </TouchableOpacity>
                      );
                    })}
                  </ScrollView>
                </View>
              )}

              {/* Rent/Sale Row */}
              <View style={styles.fullWidthContainer}>
                <Text style={styles.inputLabel}>Rent/Sale</Text>
                <ScrollView
                  horizontal
                  style={styles.pillScrollContainer}
                  contentContainerStyle={styles.pillScrollContent}
                  showsHorizontalScrollIndicator={false}
                >
                  {RENT_SALE_OPTIONS.map((option) => (
                    <TouchableOpacity
                      key={option.id}
                      style={[
                        styles.filterPill,
                        viewingConfig.rentSale === option.id.toString() && styles.selectedFilterPill
                      ]}
                      onPress={() => {
                        // Allow deselection - if already selected, deselect it
                        const newValue = viewingConfig.rentSale === option.id.toString() ? '' : option.id.toString();
                        updateViewingConfig('rentSale', newValue);
                      }}
                    >
                      <Text
                        style={[
                          styles.filterPillText,
                          viewingConfig.rentSale === option.id.toString() && styles.selectedFilterPillText
                        ]}
                      >
                        {option.label}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </ScrollView>
              </View>

              {/* Min Area Row */}
              <View style={styles.fullWidthContainer}>
                <Text style={styles.inputLabel}>Min Area</Text>
                <ScrollView
                  horizontal
                  style={styles.pillScrollContainer}
                  contentContainerStyle={styles.pillScrollContent}
                  showsHorizontalScrollIndicator={false}
                >
                  {MIN_AREA_OPTIONS.map((option) => (
                    <TouchableOpacity
                      key={option.id}
                      style={[
                        styles.filterPill,
                        viewingConfig.minArea === option.id.toString() && styles.selectedFilterPill
                      ]}
                      onPress={() => {
                        // Allow deselection - if already selected, deselect it
                        const newValue = viewingConfig.minArea === option.id.toString() ? '' : option.id.toString();
                        updateViewingConfig('minArea', newValue);
                      }}
                    >
                      <Text
                        style={[
                          styles.filterPillText,
                          viewingConfig.minArea === option.id.toString() && styles.selectedFilterPillText
                        ]}
                      >
                        {option.label}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </ScrollView>
              </View>

              {/* Max Area Row */}
              <View style={styles.fullWidthContainer}>
                <Text style={styles.inputLabel}>Max Area</Text>
                <ScrollView
                  horizontal
                  style={styles.pillScrollContainer}
                  contentContainerStyle={styles.pillScrollContent}
                  showsHorizontalScrollIndicator={false}
                >
                  {MAX_AREA_OPTIONS.map((option) => (
                    <TouchableOpacity
                      key={option.id}
                      style={[
                        styles.filterPill,
                        viewingConfig.maxArea === option.id.toString() && styles.selectedFilterPill
                      ]}
                      onPress={() => {
                        // Allow deselection - if already selected, deselect it
                        const newValue = viewingConfig.maxArea === option.id.toString() ? '' : option.id.toString();
                        updateViewingConfig('maxArea', newValue);
                      }}
                    >
                      <Text
                        style={[
                          styles.filterPillText,
                          viewingConfig.maxArea === option.id.toString() && styles.selectedFilterPillText
                        ]}
                      >
                        {option.label}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </ScrollView>
              </View>

              {/* Price Min Row */}
              <View style={styles.fullWidthContainer}>
                <Text style={styles.inputLabel}>Price Min</Text>
                <ScrollView
                  horizontal
                  style={styles.pillScrollContainer}
                  contentContainerStyle={styles.pillScrollContent}
                  showsHorizontalScrollIndicator={false}
                >
                  {PRICE_MIN_OPTIONS.map((option) => (
                    <TouchableOpacity
                      key={option.id}
                      style={[
                        styles.filterPill,
                        viewingConfig.priceMin === option.id.toString() && styles.selectedFilterPill
                      ]}
                      onPress={() => {
                        // Allow deselection - if already selected, deselect it
                        const newValue = viewingConfig.priceMin === option.id.toString() ? '' : option.id.toString();
                        updateViewingConfig('priceMin', newValue);
                      }}
                    >
                      <Text
                        style={[
                          styles.filterPillText,
                          viewingConfig.priceMin === option.id.toString() && styles.selectedFilterPillText
                        ]}
                      >
                        {option.label}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </ScrollView>
              </View>

              {/* Price Max Row */}
              <View style={styles.fullWidthContainer}>
                <Text style={styles.inputLabel}>Price Max</Text>
                <ScrollView
                  horizontal
                  style={styles.pillScrollContainer}
                  contentContainerStyle={styles.pillScrollContent}
                  showsHorizontalScrollIndicator={false}
                >
                  {PRICE_MAX_OPTIONS.map((option) => (
                    <TouchableOpacity
                      key={option.id}
                      style={[
                        styles.filterPill,
                        viewingConfig.priceMax === option.id.toString() && styles.selectedFilterPill
                      ]}
                      onPress={() => {
                        // Allow deselection - if already selected, deselect it
                        const newValue = viewingConfig.priceMax === option.id.toString() ? '' : option.id.toString();
                        updateViewingConfig('priceMax', newValue);
                      }}
                    >
                      <Text
                        style={[
                          styles.filterPillText,
                          viewingConfig.priceMax === option.id.toString() && styles.selectedFilterPillText
                        ]}
                      >
                        {option.label}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </ScrollView>
              </View>

              {/* Bedrooms Row */}
              <View style={styles.fullWidthContainer}>
                <Text style={styles.inputLabel}>Bedrooms</Text>
                <ScrollView
                  horizontal
                  style={styles.pillScrollContainer}
                  contentContainerStyle={styles.pillScrollContent}
                  showsHorizontalScrollIndicator={false}
                >
                  {bedroomOptions.map((option) => (
                    <TouchableOpacity
                      key={option.id}
                      style={[
                        styles.filterPill,
                        viewingConfig.bedrooms.includes(option.id.toString()) && styles.selectedFilterPill
                      ]}
                      onPress={() => {
                        const currentBedrooms = viewingConfig.bedrooms;
                        const bedroomId = option.id.toString();
                        const newBedrooms = currentBedrooms.includes(bedroomId)
                          ? currentBedrooms.filter(id => id !== bedroomId)
                          : [...currentBedrooms, bedroomId];
                        updateViewingConfig('bedrooms', newBedrooms);
                      }}
                    >
                      <Text
                        style={[
                          styles.filterPillText,
                          viewingConfig.bedrooms.includes(option.id.toString()) && styles.selectedFilterPillText
                        ]}
                      >
                        {option.label}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </ScrollView>
              </View>

               <View style={styles.viewingButtonsContainer}>
                <TouchableOpacity
                  style={styles.resetButton}
                  onPress={resetViewingFilters}
                >
                  <Text style={styles.resetButtonText}>Reset filters</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.scheduleButton,
                    viewingConfig.selectedProperties.length === 0 && styles.scheduleButtonDisabled
                  ]}
                  disabled={viewingConfig.selectedProperties.length === 0}
                  onPress={() => {
                    if (viewingConfig.selectedProperties.length > 0) {
                      console.log('Scheduling view for properties:', viewingConfig.selectedProperties);
                      // Here you can handle the scheduling logic
                      handleClose();
                    }
                  }}
                >
                  <Text style={[
                    styles.scheduleButtonText,
                    viewingConfig.selectedProperties.length === 0 && styles.scheduleButtonTextDisabled
                  ]}>
                    Schedule view
                    {viewingConfig.selectedProperties.length > 0 && ` (${viewingConfig.selectedProperties.length})`}
                  </Text>
                </TouchableOpacity>
              </View>
              {/* Listings Results */}
              {isLoadingListings ? (
                <View style={styles.listingsContainer}>
                  <Text style={styles.listingsTitle}>Loading properties...</Text>
                </View>
              ) : listings.length > 0 ? (
                <View style={styles.listingsContainer}>
                  <Text style={styles.listingsTitle}>
                    Properties ({listings.length} din {totalListings})
                  </Text>
                  <FlatList
                    data={listings}
                    keyExtractor={(item) => item.id.toString()}
                    renderItem={renderListingItem}
                    getItemLayout={getItemLayout}
                    style={styles.listingsList}
                    showsVerticalScrollIndicator={false}
                    nestedScrollEnabled={true}
                    removeClippedSubviews={true}
                    maxToRenderPerBatch={10}
                    windowSize={10}
                    ListFooterComponent={() =>
                      totalViewingPages > 1 ? (
                        <View style={styles.paginationContainer}>
                          <Pagination
                            currentPage={currentViewingPage}
                            totalPages={totalViewingPages}
                            onPageChange={handleViewingPageChange}
                          />
                        </View>
                      ) : null
                    }
                  />
                </View>
              ) : (
                <View style={styles.listingsContainer}>
                  <Text style={styles.listingsTitle}>No properties found</Text>
                </View>
              )}

              
            </ScrollView>
          )}

          {/* Follow Up Configuration */}
          {showFollowUpConfig && (
            <ScrollView style={styles.meetingConfigContainer}>
              <Text style={styles.meetingConfigTitle}>Lead configuration</Text>

              {/* Reminder Section */}
              <Text style={styles.sectionTitle}>Reminder</Text>

              {/* Title */}
              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>Title</Text>
                <TextInput
                  style={styles.textInput}
                  value={followUpConfig.title}
                  onChangeText={(text) => updateFollowUpConfig('title', text)}
                  placeholder="Follow up"
                />
              </View>

              {/* Content */}
              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>Content</Text>
                <TextInput
                  style={[styles.textInput, styles.textArea]}
                  value={followUpConfig.content}
                  onChangeText={(text) => updateFollowUpConfig('content', text)}
                  placeholder="Follow up reminder."
                  multiline
                  numberOfLines={3}
                />
              </View>

              {/* Due Date */}
              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>Due Date</Text>
                <View style={styles.dateInputContainer}>
                  <TextInput
                    style={[styles.textInput, styles.dateInput]}
                    value={followUpConfig.dueDate}
                    onChangeText={(text) => updateFollowUpConfig('dueDate', text)}
                    placeholder="dd.mm.yyyy, --:--"
                  />
                  <TouchableOpacity
                    style={styles.calendarButton}
                    onPress={() => handleDatePress('followUp')}
                  >
                    <Calendar size={20} color="#6B7280" />
                  </TouchableOpacity>
                </View>
                {/* Inline Date Picker for Follow Up */}
                {showDatePicker && currentConfigType === 'followUp' && (
                  <View style={styles.inlineDatePicker}>
                    <DateTimePicker
                      value={new Date()}
                      mode="date"
                      display="default"
                      onChange={handleDateChange}
                    />
                  </View>
                )}
                {!followUpConfig.dueDate && (
                  <Text style={styles.errorText}>Due Date is required</Text>
                )}
              </View>

              {/* Priority */}
              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>Priority</Text>
                <TouchableOpacity style={styles.dropdownButton}>
                  <Text style={styles.dropdownText}>{followUpConfig.priority}</Text>
                  <Text style={styles.dropdownArrow}>▼</Text>
                </TouchableOpacity>
              </View>

              {/* Send Email */}
              <View style={styles.checkboxContainer}>
                <TouchableOpacity
                  style={styles.checkbox}
                  onPress={() => updateFollowUpConfig('sendEmail', !followUpConfig.sendEmail)}
                >
                  {followUpConfig.sendEmail && <Text style={styles.checkmark}>✓</Text>}
                </TouchableOpacity>
                <Text style={styles.checkboxLabel}>Send Email</Text>
              </View>

              {/* Remarks Section */}
              <Text style={styles.sectionTitle}>Remarks</Text>

              {/* Remarks */}
              <View style={styles.inputContainer}>
                <TextInput
                  style={[styles.textInput, styles.textArea]}
                  value={followUpConfig.remarks}
                  onChangeText={(text) => updateFollowUpConfig('remarks', text)}
                  placeholder="Remarks"
                  multiline
                  numberOfLines={3}
                />
              </View>
            </ScrollView>
          )}

          {/* Offer Negotiation Configuration */}
          {showOfferNegotiationConfig && (
            <ScrollView style={styles.meetingConfigContainer}>
              <Text style={styles.meetingConfigTitle}>Lead configuration</Text>

              {/* First Row: Location and Tower/Building */}
              <View style={styles.rowContainer}>
                <View style={styles.halfWidth}>
                  <Dropdown
                    label="Location"
                    options={locationOptions}
                    selectedValue={offerNegotiationConfig.search}
                    onSelect={(option) => updateOfferNegotiationConfig('search', option.id.toString())}
                    placeholder="Search location"
                  />
                </View>
                <View style={styles.halfWidth}>
                  <Dropdown
                    label="Tower/Building"
                    options={finalTowerOptions}
                    selectedValue={offerNegotiationConfig.towerBuilding}
                    onSelect={(option) => updateOfferNegotiationConfig('towerBuilding', option.id.toString())}
                    placeholder="Tower/Building"
                    searchable={true}
                    searchPlaceholder="Search towers..."
                  />
                </View>
              </View>

              {/* Second Row: Rent/Sale and Min Area */}
              <View style={styles.rowContainer}>
                <View style={styles.halfWidth}>
                  <Dropdown
                    label="Rent/Sale"
                    options={RENT_SALE_OPTIONS}
                    selectedValue={offerNegotiationConfig.rentSale}
                    onSelect={(option) => updateOfferNegotiationConfig('rentSale', option.id.toString())}
                    placeholder="Select Rent/Sale"
                  />
                </View>
                <View style={styles.halfWidth}>
                  <Dropdown
                    label="Min Area"
                    options={MIN_AREA_OPTIONS}
                    selectedValue={offerNegotiationConfig.minArea}
                    onSelect={(option) => updateOfferNegotiationConfig('minArea', option.id.toString())}
                    placeholder="Select Min Area"
                  />
                </View>
              </View>

              {/* Second Row: Max Area and Price Min */}
              <View style={styles.rowContainer}>
                <View style={styles.halfWidth}>
                  <Dropdown
                    label="Max Area"
                    options={MAX_AREA_OPTIONS}
                    selectedValue={offerNegotiationConfig.maxArea}
                    onSelect={(option) => updateOfferNegotiationConfig('maxArea', option.id.toString())}
                    placeholder="Select Max Area"
                  />
                </View>
                <View style={styles.halfWidth}>
                  <Dropdown
                    label="Price Min"
                    options={PRICE_MIN_OPTIONS}
                    selectedValue={offerNegotiationConfig.priceMin}
                    onSelect={(option) => updateOfferNegotiationConfig('priceMin', option.id.toString())}
                    placeholder="Select Price Min"
                  />
                </View>
              </View>

              {/* Third Row: Price Max */}
              <View style={styles.rowContainer}>
                <View style={styles.halfWidth}>
                  <Dropdown
                    label="Price Max"
                    options={PRICE_MAX_OPTIONS}
                    selectedValue={offerNegotiationConfig.priceMax}
                    onSelect={(option) => updateOfferNegotiationConfig('priceMax', option.id.toString())}
                    placeholder="Select Price Max"
                  />
                </View>
                <View style={styles.halfWidth}>
                  <MultiSelectDropdown
                    label="Bedrooms"
                    options={bedroomOptions}
                    selectedValues={offerNegotiationConfig.bedrooms}
                    onSelect={(selectedIds) => updateOfferNegotiationConfig('bedrooms', selectedIds)}
                    placeholder="Select Bedrooms"
                    showConfirmButton={true}
                    confirmButtonText="OK"
                  />
                </View>
              </View>

              {/* Offer Negotiation Listings Results */}
              {isLoadingOfferListings ? (
                <View style={styles.listingsContainer}>
                  <Text style={styles.listingsTitle}>Loading properties...</Text>
                </View>
              ) : offerListings.length > 0 ? (
                <View style={styles.listingsContainer}>
                  <Text style={styles.listingsTitle}>
                    Properties ({offerListings.length} din {totalOfferListings})
                  </Text>
                  <FlatList
                    data={offerListings}
                    keyExtractor={(item) => item.id.toString()}
                    renderItem={({ item }: { item: any }) => (
                      <View style={styles.listingItem}>
                        <View style={styles.listingCardContainer}>
                          <ListingCard
                            listing={item}
                            disableNavigation={true}
                            onPress={() => {
                              // For offer negotiation, we don't need selection - just show the listings
                              console.log('Selected listing for offer negotiation:', item.id);
                            }}
                          />
                        </View>
                      </View>
                    )}
                    style={styles.listingsList}
                    showsVerticalScrollIndicator={false}
                    nestedScrollEnabled={true}
                    ListFooterComponent={() =>
                      totalOfferPages > 1 ? (
                        <View style={styles.paginationContainer}>
                          <Pagination
                            currentPage={currentOfferPage}
                            totalPages={totalOfferPages}
                            onPageChange={handleOfferPageChange}
                          />
                        </View>
                      ) : null
                    }
                  />
                </View>
              ) : (
                <View style={styles.listingsContainer}>
                  <Text style={styles.listingsTitle}>No properties found</Text>
                </View>
              )}

              {/* Reset filters button (without Schedule view) */}
              <View style={styles.offerNegotiationButtonsContainer}>
                <TouchableOpacity
                  style={styles.resetButton}
                  onPress={resetOfferNegotiationFilters}
                >
                  <Text style={styles.resetButtonText}>Reset filters</Text>
                </TouchableOpacity>
              </View>

              {/* Properties table placeholder */}
              <View style={styles.propertiesContainer}>
                <Text style={styles.noItemsText}>No items selected</Text>
              </View>
            </ScrollView>
          )}

          {/* Action Buttons */}
          <View style={styles.buttonContainer}>
            <TouchableOpacity style={styles.cancelButton} onPress={handleClose}>
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.saveButton,
                !tempSelectedStatus && styles.saveButtonDisabled
              ]}
              onPress={handleSave}
              disabled={!tempSelectedStatus}
            >
              <Text style={[
                styles.saveButtonText,
                !tempSelectedStatus && styles.saveButtonTextDisabled
              ]}>
                Save
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContainer: {
    backgroundColor: 'white',
    borderRadius: 12,
    width: Math.min(screenWidth - 40, 400),
    maxHeight: screenHeight * 0.5,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalContainerExpanded: {
    maxHeight: screenHeight * 0.85,
    width: Math.min(screenWidth - 20, 600),
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
  },
  closeButton: {
    padding: 4,
  },
  statusScrollContainer: {
    marginVertical: 20,
  },
  statusScrollContent: {
    paddingHorizontal: 16,
    gap: 12,
  },
  statusPill: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
  },
  selectedStatusPill: {
    backgroundColor: '#B89C4C',
  },
  statusPillText: {
    fontSize: 14,
    color: '#4B5563',
    fontWeight: '500',
    textTransform: 'capitalize',
  },
  selectedStatusPillText: {
    color: '#fff',
  },
  buttonContainer: {
    flexDirection: 'row',
    padding: 16,
    gap: 12,
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 24,
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 16,
    color: '#6B7280',
    fontWeight: '500',
  },
  saveButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 24,
    backgroundColor: '#B89C4C',
    borderRadius: 8,
    alignItems: 'center',
  },
  saveButtonDisabled: {
    backgroundColor: '#E5E7EB',
  },
  saveButtonText: {
    fontSize: 16,
    color: 'white',
    fontWeight: '600',
  },
  saveButtonTextDisabled: {
    color: '#9CA3AF',
  },
  // Meeting Configuration Styles
  meetingConfigContainer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    maxHeight: screenHeight * 0.4,
  },
  meetingConfigTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 12,
    marginTop: 8,
  },
  inputContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 8,
  },
  textInput: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: '#1F2937',
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  dateInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    position: 'relative',
  },
  dateInput: {
    flex: 1,
    paddingRight: 40,
  },
  calendarIcon: {
    position: 'absolute',
    right: 12,
  },
  errorText: {
    fontSize: 12,
    color: '#EF4444',
    marginTop: 4,
  },
  dropdownButton: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    padding: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dropdownText: {
    fontSize: 16,
    color: '#1F2937',
  },
  dropdownArrow: {
    fontSize: 12,
    color: '#6B7280',
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  checkboxLabel: {
    fontSize: 16,
    color: '#1F2937',
    marginLeft: 12,
  },
  // Viewing Configuration Styles
  rowContainer: {
    flexDirection: 'row',
    marginBottom: 16,
    gap: 12,
  },
  halfWidth: {
    flex: 1,
  },
  viewingButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: 16,
  },
  resetButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    backgroundColor: '#F3F4F6',
    borderRadius: 6,
  },
  resetButtonText: {
    fontSize: 14,
    color: '#6B7280',
    fontWeight: '500',
  },
  scheduleButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    backgroundColor: '#B89C4C',
    borderRadius: 6,
  },
  scheduleButtonDisabled: {
    backgroundColor: '#D1D5DB',
    opacity: 0.6,
  },
  scheduleButtonText: {
    fontSize: 14,
    color: '#fff',
    fontWeight: '500',
  },
  scheduleButtonTextDisabled: {
    color: '#9CA3AF',
  },
  propertiesContainer: {
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 80,
  },
  noItemsText: {
    fontSize: 14,
    color: '#6B7280',
  },
  // Checkbox styles
  checkbox: {
    width: 20,
    height: 20,
    borderWidth: 2,
    borderColor: '#D1D5DB',
    borderRadius: 4,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#fff',
  },
  checkmark: {
    fontSize: 14,
    color: '#B89C4C',
    fontWeight: 'bold',
  },
  // Offer Negotiation specific styles
  offerNegotiationButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    marginBottom: 16,
  },
  // Calendar button styles
  calendarButton: {
    position: 'absolute',
    right: 8,
    top: 0,
    bottom: 0,
    width: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  // Inline date picker styles
  inlineDatePicker: {
    marginTop: 8,
    backgroundColor: 'transparent',
    padding: 0,
  },
  // Listings styles
  listingsContainer: {
    marginTop: 16,
    maxHeight: 400,
  },
  listingsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 12,
  },
  listingsList: {
    maxHeight: 500,
  },
  listingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    padding: 8,
  },
  listingCheckboxContainer: {
    marginRight: 12,
  },
  listingCheckbox: {
    width: 20,
    height: 20,
    borderWidth: 2,
    borderColor: '#D1D5DB',
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
  },
  listingCheckboxSelected: {
    backgroundColor: '#B89C4C',
    borderColor: '#B89C4C',
  },
  listingCheckmark: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  listingCardContainer: {
    flex: 1,
  },
  loadingFooter: {
    padding: 20,
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 14,
    color: '#6B7280',
  },
  paginationContainer: {
    marginTop: 16,
    alignItems: 'center',
  },
  // Filter pill styles
  pillScrollContainer: {
    marginTop: 8,
  },
  pillScrollContent: {
    gap: 8,
    paddingHorizontal: 4,
  },
  filterPill: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    backgroundColor: '#F3F4F6',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  selectedFilterPill: {
    backgroundColor: '#B89C4C',
    borderColor: '#B89C4C',
  },
  filterPillText: {
    fontSize: 13,
    color: '#6B7280',
    fontWeight: '500',
  },
  selectedFilterPillText: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
  fullWidthContainer: {
    marginBottom: 16,
  },
});
