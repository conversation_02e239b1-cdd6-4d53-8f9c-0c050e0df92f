import React, { memo } from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity } from 'react-native';
import { MapPin, Tag, Eye, EyeOff } from 'lucide-react-native';
import { router } from 'expo-router';
import { Listing } from '@/types/inventory';

interface ListingCardProps {
  listing?: Listing;
  isLoading?: boolean;
  disableNavigation?: boolean;
  onPress?: () => void;
}

function ListingCardSkeleton() {
  return (
    <View style={[styles.card, styles.skeletonCard]}>
      <View style={styles.skeletonImage} />
      <View style={styles.content}>
        <View style={styles.skeletonTitle} />
        <View style={styles.skeletonLocation} />
        <View style={styles.skeletonPrice} />
      </View>
    </View>
  );
}

function ListingCardComponent({ listing, isLoading, disableNavigation, onPress }: ListingCardProps) {
  if (isLoading || !listing) {
    return <ListingCardSkeleton />;
  }

  const primaryImage = listing.images.find(img => img.is_primary)?.img_url || listing.images[0]?.img_url;

  const formatPrice = (price: string) => {
    const numericPrice = parseInt(price.replace(/[^0-9]/g, ''), 10);
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'QAR',
      maximumFractionDigits: 0,
    }).format(numericPrice);
  };

  const getPublishingStatusColor = (status: string | null) => {
    if (!status) return '#6B7280';
    
    switch (status.toLowerCase()) {
      case 'published':
        return '#10B981';
      case 'draft':
        return '#6B7280';
      case 'pending':
        return '#F59E0B';
      case 'rejected':
        return '#EF4444';
      default:
        return '#6B7280';
    }
  };

  const handlePress = () => {
    if (disableNavigation && onPress) {
      onPress();
    } else if (!disableNavigation) {
      router.push(`/inventory/${listing.id}`);
    }
  };

  const CardWrapper = disableNavigation ? View : TouchableOpacity;
  const cardProps = disableNavigation ? { style: styles.card } : { style: styles.card, onPress: handlePress };

  return (
    <CardWrapper {...cardProps}>
      <View style={styles.imageContainer}>
        <Image
          source={{ uri: primaryImage }}
          style={styles.image}
          resizeMode="cover"
        />
        <View style={[
          styles.publishingStatus,
          { backgroundColor: getPublishingStatusColor(listing.publishing_status) }
        ]}>
          {(listing.publishing_status?.toLowerCase() === 'published') ? (
            <Eye size={12} color="#fff" />
          ) : (
            <EyeOff size={12} color="#fff" />
          )}
          <Text style={styles.publishingStatusText}>
            {listing.publishing_status || 'Draft'}
          </Text>
        </View>
      </View>

      <View style={styles.content}>
        <Text style={styles.title} numberOfLines={2}>
          {listing.title || '[Untitled Property]'}
        </Text>

        <View style={styles.infoRow}>
          <MapPin size={16} color="#6B7280" />
          <Text style={styles.location} numberOfLines={1}>
            {listing.location} {listing.tower?.name ? `• ${listing.tower.name}` : ''}
          </Text>
        </View>

        <View style={styles.footer}>
          <Text style={styles.price}>{formatPrice(listing.price)}</Text>
          <View style={styles.adTypeBadge}>
            <Tag size={14} color="#6B7280" />
            <Text style={styles.adTypeText}>
              {listing.ad_type ? listing.ad_type.charAt(0).toUpperCase() + listing.ad_type.slice(1) : 'Sale'}
            </Text>
          </View>
        </View>
      </View>
    </CardWrapper>
  );
}

const styles = StyleSheet.create({
  card: {
    backgroundColor: '#fff',
    borderRadius: 12,
    marginBottom: 16,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
    flexDirection: 'row',
    height: 140,
  },
  imageContainer: {
    width: 140,
    backgroundColor: '#F3F4F6',
    position: 'relative',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  publishingStatus: {
    position: 'absolute',
    top: 8,
    left: 8,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  publishingStatusText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: '600',
    textTransform: 'capitalize',
  },
  content: {
    flex: 1,
    padding: 12,
    justifyContent: 'space-between',
  },
  title: {
    fontSize: 15,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 4,
    lineHeight: 20,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  location: {
    fontSize: 13,
    color: '#6B7280',
    flex: 1,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  price: {
    fontSize: 16,
    fontWeight: '600',
    color: '#B89C4C',
  },
  adTypeBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    backgroundColor: '#F3F4F6',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  adTypeText: {
    fontSize: 11,
    color: '#6B7280',
    fontWeight: '500',
  },
  // Skeleton styles
  skeletonCard: {
    backgroundColor: '#F9FAFB',
  },
  skeletonImage: {
    width: 140,
    backgroundColor: '#E5E7EB',
  },
  skeletonTitle: {
    height: 20,
    backgroundColor: '#E5E7EB',
    borderRadius: 4,
    marginBottom: 8,
    width: '80%',
  },
  skeletonLocation: {
    height: 16,
    backgroundColor: '#E5E7EB',
    borderRadius: 4,
    marginBottom: 16,
    width: '60%',
  },
  skeletonPrice: {
    height: 20,
    backgroundColor: '#E5E7EB',
    borderRadius: 4,
    width: '30%',
  },
});

export default memo(ListingCardComponent);
