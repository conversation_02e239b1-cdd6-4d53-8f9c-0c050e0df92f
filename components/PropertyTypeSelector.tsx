import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ActivityIndicator } from 'react-native';
import { Building2, Chrome as Home, Building, Warehouse } from 'lucide-react-native';
import { useListingTypes } from '@/hooks/useConfiguration';

interface PropertyTypeSelectorProps {
  selectedType: number;
  onSelectType: (type: number) => void;
}

export default function PropertyTypeSelector({
  selectedType,
  onSelectType,
}: PropertyTypeSelectorProps) {
  const { data: propertyTypes = [], isLoading, error } = useListingTypes();

  const groupedTypes = React.useMemo(() => {
    const residential = propertyTypes.filter(type => !type.is_commercial);
    const commercial = propertyTypes.filter(type => type.is_commercial);
    return { residential, commercial };
  }, [propertyTypes]);

  const getIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'apartment':
        return Building2;
      case 'villa':
        return Home;
      case 'office':
        return Building;
      case 'retail':
        return Warehouse;
      default:
        return Building2;
    }
  };

  if (isLoading) {
    return (
      <View style={styles.container}>
        <Text style={styles.title}>Property Type</Text>
        <Text style={styles.description}>
          Select the type of property you want to list
        </Text>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="small" color="#B89C4C" />
          <Text style={styles.loadingText}>Loading property types...</Text>
        </View>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.container}>
        <Text style={styles.title}>Property Type</Text>
        <Text style={styles.errorText}>Failed to load property types</Text>
      </View>
    );
  }

  const PropertyTypeGroup = ({ title, types }: { title: string; types: any[] }) => (
    <View style={styles.group}>
      <Text style={styles.groupTitle}>{title}</Text>
      <View style={styles.grid}>
        {types.map((type) => {
          const Icon = getIcon(type.url_value);
          const isSelected = selectedType === type.id;

          return (
            <TouchableOpacity
              key={type.id}
              style={[
                styles.option,
                isSelected && styles.optionSelected,
              ]}
              onPress={() => onSelectType(type.id)}
            >
              <View style={[
                styles.iconContainer,
                isSelected && styles.iconContainerSelected,
              ]}>
                <Icon
                  size={24}
                  color={isSelected ? '#B89C4C' : '#6B7280'}
                />
              </View>
              <Text style={[
                styles.optionTitle,
                isSelected && styles.optionTitleSelected,
              ]}>
                {type.label}
              </Text>
            </TouchableOpacity>
          );
        })}
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Property Type</Text>
      <Text style={styles.description}>
        Select the type of property you want to list
      </Text>

      {groupedTypes.residential.length > 0 && (
        <PropertyTypeGroup title="Residential" types={groupedTypes.residential} />
      )}

      {groupedTypes.commercial.length > 0 && (
        <PropertyTypeGroup title="Commercial" types={groupedTypes.commercial} />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    gap: 16,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
  },
  description: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 8,
  },
  group: {
    gap: 12,
  },
  groupTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#4B5563',
    paddingLeft: 4,
  },
  grid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  option: {
    flex: 1,
    minWidth: '48%',
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  optionSelected: {
    borderColor: '#B89C4C',
    backgroundColor: '#FFFBEB',
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  iconContainerSelected: {
    backgroundColor: '#FEF3C7',
  },
  optionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 4,
  },
  optionTitleSelected: {
    color: '#B89C4C',
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    padding: 16,
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
  },
  loadingText: {
    fontSize: 14,
    color: '#6B7280',
  },
  errorText: {
    fontSize: 14,
    color: '#EF4444',
    textAlign: 'center',
  },
});