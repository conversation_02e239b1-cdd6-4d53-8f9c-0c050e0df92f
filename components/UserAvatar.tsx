import React from 'react';
import { View, Image, Text, StyleSheet } from 'react-native';

interface UserAvatarProps {
  imageUrl?: string | null;
  name: string;
  size?: number;
  fontSize?: number;
}

export default function UserAvatar({ imageUrl, name, size = 48, fontSize = 20 }: UserAvatarProps) {
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const dynamicStyles = {
    container: {
      width: size,
      height: size,
      borderRadius: size / 2,
    },
    image: {
      width: size,
      height: size,
      borderRadius: size / 2,
    },
    initialsText: {
      fontSize,
    },
  };

  if (imageUrl) {
    return (
      <Image
        source={{ uri: imageUrl }}
        style={[styles.image, dynamicStyles.image]}
      />
    );
  }

  return (
    <View style={[styles.container, dynamicStyles.container]}>
      <Text style={[styles.initialsText, dynamicStyles.initialsText]}>
        {getInitials(name)}
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#B89C4C',
    justifyContent: 'center',
    alignItems: 'center',
  },
  image: {
    backgroundColor: '#E5E7EB',
  },
  initialsText: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
});