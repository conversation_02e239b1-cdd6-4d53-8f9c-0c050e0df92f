import React from 'react';
import { View, Text, StyleSheet, useWindowDimensions } from 'react-native';
import { Check } from 'lucide-react-native';
import Animated, { useAnimatedStyle, withSpring } from 'react-native-reanimated';

interface StepperProps {
  steps: string[];
  currentStep: number;
}

export default function Stepper({ steps, currentStep }: StepperProps) {
  const { width } = useWindowDimensions();
  const containerWidth = width - 40; // Account for container padding
  const stepWidth = 32; // Width of step circle
  const connectorWidth = (containerWidth - (stepWidth * steps.length)) / (steps.length - 1);
  const progressWidth = currentStep === 0 ? 0 : 
    (stepWidth + (connectorWidth * currentStep));

  const progressStyle = useAnimatedStyle(() => ({
    width: withSpring(progressWidth, {
      damping: 20,
      stiffness: 90,
    }),
  }));

  return (
    <View style={styles.container}>
      <View style={styles.stepsContainer}>
        {steps.map((step, index) => (
          <React.Fragment key={step}>
            <View style={styles.stepWrapper}>
              <View style={[
                styles.stepCircle,
                index <= currentStep && styles.stepCircleActive,
                index < currentStep && styles.stepCircleCompleted,
              ]}>
                {index < currentStep ? (
                  <Check size={16} color="#fff" strokeWidth={3} />
                ) : (
                  <View style={[
                    styles.stepDot,
                    index === currentStep && styles.stepDotActive
                  ]} />
                )}
              </View>
              <Text 
                style={[
                  styles.stepLabel,
                  index <= currentStep && styles.stepLabelActive,
                ]}
                numberOfLines={2}
              >
                {step}
              </Text>
            </View>
            {index < steps.length - 1 && (
              <View style={[styles.connector, { width: connectorWidth }]} />
            )}
          </React.Fragment>
        ))}
      </View>

      <View style={styles.progressContainer}>
        <Animated.View style={[styles.progressBar, progressStyle]} />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingVertical: 24,
    paddingHorizontal: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  stepsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
  },
  stepWrapper: {
    alignItems: 'center',
    width: 32,
  },
  stepCircle: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#E5E7EB',
  },
  stepCircleActive: {
    borderColor: '#B89C4C',
    backgroundColor: '#fff',
  },
  stepCircleCompleted: {
    borderColor: '#B89C4C',
    backgroundColor: '#B89C4C',
  },
  stepDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#E5E7EB',
  },
  stepDotActive: {
    backgroundColor: '#B89C4C',
  },
  stepLabel: {
    fontSize: 12,
    color: '#6B7280',
    textAlign: 'center',
    position: 'absolute',
    top: 40,
    width: 64,
    left: -16, // (64 - 32) / 2
  },
  stepLabelActive: {
    color: '#111827',
    fontWeight: '500',
  },
  connector: {
    height: 2,
    backgroundColor: '#E5E7EB',
  },
  progressContainer: {
    height: 4,
    backgroundColor: '#E5E7EB',
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    backgroundColor: '#B89C4C',
    borderRadius: 2,
  },
});