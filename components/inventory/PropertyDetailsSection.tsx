import React from 'react';
import { View, Text, StyleSheet, Switch, TextInput, TouchableOpacity } from 'react-native';

interface PropertyDetailsProps {
  formData: {
    basicDetails: {
      operationType: 'rent' | 'sale' | null;
    }
    propertyDetails: {
      isExclusive: boolean;
      isHomepagePromoted: boolean;
      isInvestmentOpportunity: boolean;
      developerName: string;
      unitNo: string;
      keyAccess: string;
      status: string;
      completionYear: string;
      titleDeed?: string;
      referral: {
        id: number;
        name: string;
      } | null;
    };
  };
  onFormDataChange: (updates: Partial<any>) => void;
  onSelectReferral: () => void;
}

export default function PropertyDetailsSection({
  formData,
  onFormDataChange,
  onSelectReferral,
}: PropertyDetailsProps) {
  const statusOptions = formData.basicDetails.operationType === 'rent'
    ? ['available', 'to-be-available', 'rented']
    : ['available', 'occupied', 'sold'];

  const updatePropertyDetails = (updates: Partial<typeof formData.propertyDetails>) => {
    onFormDataChange({
      propertyDetails: {
        ...formData.propertyDetails,
        ...updates
      }
    });
  };

  return (
    <View style={styles.stepContent}>
      <Text style={styles.sectionTitle}>Property Details</Text>
      <Text style={styles.sectionDescription}>
        Enter the property's basic information
      </Text>

      <View style={styles.section}>
        <View style={styles.togglesContainer}>
          <View style={styles.toggleRow}>
            <Text style={styles.label}>Exclusive Property</Text>
            <Switch
              value={formData.propertyDetails.isExclusive}
              onValueChange={(value) => updatePropertyDetails({ isExclusive: value })}
            />
          </View>

          <View style={styles.toggleRow}>
            <Text style={styles.label}>Homepage Promoted</Text>
            <Switch
              value={formData.propertyDetails.isHomepagePromoted}
              onValueChange={(value) => updatePropertyDetails({ isHomepagePromoted: value })}
            />
          </View>

          <View style={styles.toggleRow}>
            <Text style={styles.label}>Investment Opportunity</Text>
            <Switch
              value={formData.propertyDetails.isInvestmentOpportunity}
              onValueChange={(value) => updatePropertyDetails({ isInvestmentOpportunity: value })}
            />
          </View>
        </View>
      </View>

      <View style={styles.section}>
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Developer Name</Text>
          <TextInput
            style={styles.input}
            value={formData.propertyDetails.developerName}
            onChangeText={(text) => updatePropertyDetails({ developerName: text })}
            placeholder="Enter developer name"
            placeholderTextColor="#9CA3AF"
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Unit No</Text>
          <TextInput
            style={styles.input}
            value={formData.propertyDetails.unitNo}
            onChangeText={(text) => updatePropertyDetails({ unitNo: text })}
            placeholder="Enter unit number"
            placeholderTextColor="#9CA3AF"
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Key Access</Text>
          <TextInput
            style={styles.input}
            value={formData.propertyDetails.keyAccess}
            onChangeText={(text) => updatePropertyDetails({ keyAccess: text })}
            placeholder="Enter key access details"
            placeholderTextColor="#9CA3AF"
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Status</Text>
          <View style={styles.optionsGrid}>
            {statusOptions.map((status) => (
              <TouchableOpacity
                key={status}
                style={[
                  styles.optionButton,
                  formData.propertyDetails.status === status && styles.optionButtonActive,
                ]}
                onPress={() => updatePropertyDetails({ status })}
              >
                <Text
                  style={[
                    styles.optionButtonText,
                    formData.propertyDetails.status === status && styles.optionButtonTextActive,
                  ]}
                >
                  {status.charAt(0).toUpperCase() + status.slice(1)}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Completion Year</Text>
          <TextInput
            style={styles.input}
            value={formData.propertyDetails.completionYear}
            onChangeText={(text) => updatePropertyDetails({ completionYear: text })}
            placeholder="Enter completion year"
            placeholderTextColor="#9CA3AF"
            keyboardType="numeric"
          />
        </View>

        {formData.basicDetails.operationType === 'sale' && (
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Title Deed</Text>
            <TextInput
              style={styles.input}
              value={formData.propertyDetails.titleDeed}
              onChangeText={(text) => updatePropertyDetails({ titleDeed: text })}
              placeholder="Enter title deed details"
              placeholderTextColor="#9CA3AF"
            />
          </View>
        )}

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Referred By</Text>
          {formData.propertyDetails.referral ? (
            <View style={styles.selectedReferral}>
              <View style={styles.referralInfo}>
                <Text style={styles.referralName}>
                  {formData.propertyDetails.referral.name}
                </Text>
              </View>
              <TouchableOpacity
                style={styles.changeButton}
                onPress={onSelectReferral}
              >
                <Text style={styles.changeButtonText}>Change</Text>
              </TouchableOpacity>
            </View>
          ) : (
            <TouchableOpacity
              style={styles.selectButton}
              onPress={onSelectReferral}
            >
              <Text style={styles.selectButtonText}>Select Referral</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  stepContent: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 16,
  },
  section: {
    marginBottom: 32,
  },
  togglesContainer: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    gap: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  toggleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 8,
  },
  input: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: '#1F2937',
  },
  optionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  optionButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
  },
  optionButtonActive: {
    backgroundColor: '#B89C4C',
  },
  optionButtonText: {
    fontSize: 14,
    color: '#4B5563',
    fontWeight: '500',
  },
  optionButtonTextActive: {
    color: '#fff',
  },
  selectedReferral: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  referralInfo: {
    flex: 1,
  },
  referralName: {
    fontSize: 16,
    color: '#111827',
  },
  changeButton: {
    backgroundColor: '#F3F4F6',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
  changeButtonText: {
    fontSize: 14,
    color: '#4B5563',
    fontWeight: '500',
  },
  selectButton: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
  },
  selectButtonText: {
    fontSize: 16,
    color: '#4B5563',
    fontWeight: '500',
  },
});