import React, { useRef, useCallback, useState } from 'react';
import { View, Text, StyleSheet, TextInput, ScrollView, Platform, Pressable, Image } from 'react-native';
import { Globe, ImagePlus, Type } from 'lucide-react-native';
import { RichEditor, RichToolbar, actions } from 'react-native-pell-rich-editor';
import { router, useLocalSearchParams } from 'expo-router';
import { FormData, Image as ImageModel } from '@/types/inventory';

interface Props {
  formData: FormData;
  onFormDataChange: (updates: Partial<FormData>) => void;
  onImagePress: () => void;
}

export default function DescriptionSection({ formData, onFormDataChange, onImagePress }: Props) {
  const { id } = useLocalSearchParams<{ id: string }>();
  console.log('id', id)
  const richTextEnRef = useRef<RichEditor>(null);
  const richTextArRef = useRef<RichEditor>(null);

  const updateDescription = (updates: Partial<typeof formData.description>) => {
    onFormDataChange({
      description: {
        ...formData.description,
        ...updates,
      },
    });
  };

  const handleEnEditorChange = useCallback((text: string) => {
    updateDescription({
      description: { ...formData.description.description, en: text }
    });
  }, [formData.description.description]);

  const handleArEditorChange = useCallback((text: string) => {
    updateDescription({
      description: { ...formData.description.description, ar: text }
    });
  }, [formData.description.description]);

  // Custom toolbar actions for iOS rich editor
  const editorActions = [
    actions.setBold,
    actions.setItalic,
    actions.setUnderline,
    actions.heading1,
    actions.heading2,
    actions.heading3,
    actions.insertBulletsList,
    actions.insertOrderedList,
    actions.insertLink,
    actions.keyboard,
    actions.setStrikethrough,
    actions.removeFormat,
    actions.undo,
    actions.redo,
  ];

  const renderDescriptionEditor = (language: 'en' | 'ar') => {
    const isArabic = language === 'ar';
    const value = formData.description.description[language];
    const handleChange = isArabic ? handleArEditorChange : handleEnEditorChange;
    const ref = isArabic ? richTextArRef : richTextEnRef;
    const placeholder = isArabic
      ? "أدخل وصف مفصل للعقار باللغة العربية"
      : "Enter detailed property description in English";

    if (Platform.OS === 'ios') {
      return (
        <View style={styles.richEditorContainer}>
          <RichToolbar
            editor={ref}
            actions={editorActions}
            iconMap={{
              [actions.heading1]: () => <Text style={styles.toolbarButton}>H1</Text>,
              [actions.heading2]: () => <Text style={styles.toolbarButton}>H2</Text>,
              [actions.heading3]: () => <Text style={styles.toolbarButton}>H3</Text>,
            }}
            style={styles.richToolbar}
          />
          <RichEditor
            ref={ref}
            initialContentHTML={value}
            onChange={handleChange}
            placeholder={placeholder}
            initialHeight={200}
            style={[styles.richEditor, isArabic && styles.rtlEditor]}
            editorStyle={{
              backgroundColor: '#F9FAFB',
              color: '#1F2937',
              placeholderColor: '#9CA3AF',
              contentCSSText: isArabic
                ? 'font-family: system-ui, -apple-system, sans-serif; direction: rtl; text-align: right;'
                : 'font-family: system-ui, -apple-system, sans-serif;'
            }}
          />
        </View>
      );
    }

    return (
      <TextInput
        style={[
          styles.textArea,
          isArabic && styles.rtlInput
        ]}
        value={value}
        onChangeText={handleChange}
        placeholder={placeholder}
        placeholderTextColor="#9CA3AF"
        multiline
        numberOfLines={8}
        textAlignVertical="top"
        textAlign={isArabic ? 'right' : 'left'}
      />
    );
  };

  return (
    <ScrollView style={styles.container} keyboardShouldPersistTaps="handled">
      <Pressable
        style={styles.imageUploadButton}
        onPress={onImagePress}>
        {(formData.description.images ?? []).length > 0 ? (
          <View style={styles.imageUploadContainer}>
            <Text style={styles.imageUploadTitle}>Property Images</Text>
            <Text style={styles.imageUploadSubtitle}>
              {formData.description.images.length} photo{formData.description.images.length !== 1 ? 's' : ''} uploaded
            </Text>

            {(formData.description.images ?? [])
              .slice(0, 4)
              .reverse() // Reverse the array to invert the order
              .map((image, index) => (
                <View 
                  key={image.img_url} 
                  style={[
                    styles.imageWrapper,
                    { 
                      right: index * 10,
                      zIndex: 4 - index // Higher z-index for later images (which are now first)
                    }
                  ]}
                >
                  <Image
                    source={{ uri: image.img_url }}
                    style={styles.previewImage}
                    resizeMode="cover"
                  />
                </View>
              ))}
          </View>
        ) : (
          <>
            <ImagePlus size={24} color={COLORS.accent} />
            <View style={styles.imageUploadContent}>
              <Text style={styles.imageUploadTitle}>Property Images</Text>
              <Text style={styles.imageUploadSubtitle}>
                Add photos of the property
              </Text>
            </View>
          </>
        )}
      </Pressable>
      <Text style={styles.sectionTitle}>Property Description</Text>
      <Text style={styles.sectionDescription}>
        Enter the property details in both English and Arabic
      </Text>

      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Type size={20} color="#6B7280" />
          <Text style={styles.groupTitle}>Property Title</Text>
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Title (English)</Text>
          <TextInput
            style={styles.input}
            value={formData.description.title.en}
            onChangeText={(text) =>
              updateDescription({
                title: { ...formData.description.title, en: text }
              })
            }
            placeholder="Enter property title in English"
            placeholderTextColor="#9CA3AF"
            maxLength={100}
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Title (Arabic)</Text>
          <TextInput
            style={[styles.input, styles.rtlInput]}
            value={formData.description.title.ar}
            onChangeText={(text) =>
              updateDescription({
                title: { ...formData.description.title, ar: text }
              })
            }
            placeholder="أدخل عنوان العقار باللغة العربية"
            placeholderTextColor="#9CA3AF"
            maxLength={100}
            textAlign="right"
          />
        </View>
      </View>

      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Globe size={20} color="#6B7280" />
          <Text style={styles.groupTitle}>Property Finder Title</Text>
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Property Finder Title (English)</Text>
          <TextInput
            style={styles.input}
            value={formData.description.propertyFinderTitle.en}
            onChangeText={(text) =>
              updateDescription({
                propertyFinderTitle: { ...formData.description.propertyFinderTitle, en: text }
              })
            }
            placeholder="Enter Property Finder title in English"
            placeholderTextColor="#9CA3AF"
            maxLength={100}
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Property Finder Title (Arabic)</Text>
          <TextInput
            style={[styles.input, styles.rtlInput]}
            value={formData.description.propertyFinderTitle.ar}
            onChangeText={(text) =>
              updateDescription({
                propertyFinderTitle: { ...formData.description.propertyFinderTitle, ar: text }
              })
            }
            placeholder="أدخل عنوان بروبرتي فايندر باللغة العربية"
            placeholderTextColor="#9CA3AF"
            maxLength={100}
            textAlign="right"
          />
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.groupTitle}>Property Description</Text>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Description (English)</Text>
          {renderDescriptionEditor('en')}
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Description (Arabic)</Text>
          {renderDescriptionEditor('ar')}
        </View>
      </View>
    </ScrollView>
  );
}

const COLORS = {
  primary: '#F9FAFB',
  primaryLight: '#F3F4F6',
  accent: '#B89C4C',
  accentLight: '#D4B96D',
  success: '#10B981',
  warning: '#EF4444',
  background: '#FFFFFF',
  backgroundAlt: '#F8FAFC',
  text: '#111827',
  textMuted: '#6B7280',
  border: '#E5E7EB',
  gold: {
    light: '#D4B96D',
    medium: '#B89C4C',
    dark: '#8B7355',
    muted: 'rgba(184, 156, 76, 0.1)',
  },
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 24,
  },
  section: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 16,
  },
  groupTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 16,
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 8,
  },
  input: {
    backgroundColor: '#F9FAFB',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: '#1F2937',
  },
  rtlInput: {
    writingDirection: 'rtl',
  },
  textArea: {
    backgroundColor: '#F9FAFB',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: '#1F2937',
    minHeight: 200,
    textAlignVertical: 'top',
  },
  richEditorContainer: {
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    overflow: 'hidden',
  },
  richToolbar: {
    backgroundColor: '#F3F4F6',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  toolbarButton: {
    color: '#4B5563',
    fontSize: 14,
    fontWeight: '600',
    paddingHorizontal: 8,
  },
  richEditor: {
    flex: 1,
    backgroundColor: '#F9FAFB',
    minHeight: 200,
  },
  rtlEditor: {
    direction: 'rtl',
  },
  imageUploadButton: {
    backgroundColor: COLORS.background,
    borderWidth: 2,
    borderColor: COLORS.border,
    borderStyle: 'dashed',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    minHeight: 120, // Increased minimum height for larger images
  },
  imageUploadContainer: {
    width: '100%',
    paddingRight: 30, // Reduced padding since offset is smaller
    height: 80,
  },
  imageWrapper: {
    position: 'absolute',
    width: 80,
    height: 80,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
      },
      android: {
        elevation: 5,
      },
      web: {
        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.25)',
      },
    }),
  },
  previewImage: {
    width: 80,
    height: 80,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: COLORS.border,
    backgroundColor: COLORS.background,
  },
  imageUploadContent: {
    marginTop: 12,
  },
  imageUploadTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: 4,
  },
  imageUploadSubtitle: {
    fontSize: 14,
    color: COLORS.textMuted,
    marginBottom: 16,
  },
});
