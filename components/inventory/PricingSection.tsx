import React from 'react';
import { View, Text, StyleSheet, TextInput, TouchableOpacity } from 'react-native';
import { DollarSign } from 'lucide-react-native';
import { FormData } from '@/types/inventory';

interface PricingSectionProps {
  formData: Partial<FormData>;
  onFormDataChange: (updates: Partial<any>) => void;
}

const paymentMethods = [
  { value: 'cash', label: 'Cash' },
  { value: 'cheque', label: 'Cheque' },
  { value: 'mortgage', label: 'Mortgage' },
  { value: 'developer_payment_plan', label: 'Developer Payment Plan' },
];

const contractPeriods = [
  { value: 0.01, label: '1 Week' },
  { value: 0.02, label: '2 Weeks' },
  { value: 0.03, label: '3 Weeks' },
  { value: 0.1, label: '1 Month' },
  { value: 0.6, label: '6 Month' },
  { value: 1, label: '1 Year' },
  { value: 2, label: '2 Years' },
  { value: 2, label: '3 Years' },
  { value: 4, label: '4 Years' },
];

export default function PricingSection({
  formData,
  onFormDataChange,
}: PricingSectionProps) {
  const updatePricing = (updates: Partial<typeof formData.pricing>) => {
    onFormDataChange({
      pricing: {
        ...formData.pricing,
        ...updates,
      },
    });
  };

  const formatPrice = (value: string) => {
    // Remove non-numeric characters
    const numericValue = value.replace(/[^0-9]/g, '');

    // Format with thousands separator
    return numericValue.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  };

  return (
    <View style={styles.container}>
      <Text style={styles.sectionTitle}>Pricing Details</Text>
      <Text style={styles.sectionDescription}>
        Enter the property's pricing information
      </Text>

      <View style={styles.section}>
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Listed Price (QAR)</Text>
          <View style={styles.priceInputContainer}>
            <DollarSign size={20} color="#6B7280" />
            <TextInput
              style={styles.priceInput}
              value={formData.pricing.listedPrice}
              onChangeText={(text) => updatePricing({ listedPrice: formatPrice(text) })}
              placeholder="Enter listed price"
              keyboardType="numeric"
              placeholderTextColor="#9CA3AF"
            />
          </View>
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Best Price (QAR)</Text>
          <View style={styles.priceInputContainer}>
            <DollarSign size={20} color="#6B7280" />
            <TextInput
              style={styles.priceInput}
              value={formData.pricing.bestPrice}
              onChangeText={(text) => updatePricing({ bestPrice: formatPrice(text) })}
              placeholder="Enter best price"
              keyboardType="numeric"
              placeholderTextColor="#9CA3AF"
            />
          </View>
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Offers</Text>
          <TextInput
            style={styles.input}
            value={formData.pricing.offers}
            onChangeText={(text) => updatePricing({ offers: text })}
            placeholder="Enter offers"
            multiline
            numberOfLines={3}
            placeholderTextColor="#9CA3AF"
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Payment Method</Text>
          <View style={styles.optionsGrid}>
            {paymentMethods.map((method) => (
              <TouchableOpacity
                key={method.value}
                style={[
                  styles.optionButton,
                  formData.pricing.paymentMethod === method.value && styles.optionButtonActive,
                ]}
                onPress={() => updatePricing({ paymentMethod: method.value })}
              >
                <Text
                  style={[
                    styles.optionButtonText,
                    formData.pricing.paymentMethod === method.value && styles.optionButtonTextActive,
                  ]}
                >
                  {method.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {formData.pricing.paymentMethod === 'cheque' && (
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Number of Cheques</Text>
            <TextInput
              style={styles.input}
              value={formData.pricing.numberOfCheques}
              onChangeText={(text) => updatePricing({ numberOfCheques: text })}
              placeholder="Enter number of cheques"
              keyboardType="numeric"
              placeholderTextColor="#9CA3AF"
            />
          </View>
        )}

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Prorated Amount (QAR)</Text>
          <View style={styles.priceInputContainer}>
            <DollarSign size={20} color="#6B7280" />
            <TextInput
              style={styles.priceInput}
              value={formData.pricing.proratedAmount}
              onChangeText={(text) => updatePricing({ proratedAmount: formatPrice(text) })}
              placeholder="Enter prorated amount"
              keyboardType="numeric"
              placeholderTextColor="#9CA3AF"
            />
          </View>
        </View>

        {formData.operationType === 'rent' && (
          <>
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Commission (Tenant) %</Text>
              <TextInput
                style={styles.input}
                value={formData.pricing.commissionTenant}
                onChangeText={(text) => updatePricing({ commissionTenant: text })}
                placeholder="Enter tenant commission"
                keyboardType="numeric"
                placeholderTextColor="#9CA3AF"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Commission (Landlord) %</Text>
              <TextInput
                style={styles.input}
                value={formData.pricing.commissionLandlord}
                onChangeText={(text) => updatePricing({ commissionLandlord: text })}
                placeholder="Enter landlord commission"
                keyboardType="numeric"
                placeholderTextColor="#9CA3AF"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Minimum Contract Period</Text>
              <View style={styles.optionsGrid}>
                {contractPeriods.map((period) => (
                  <TouchableOpacity
                    key={period.value}
                    style={[
                      styles.optionButton,
                      formData.pricing.minimumContract === period.value && styles.optionButtonActive,
                    ]}
                    onPress={() => updatePricing({ minimumContract: period.value })}
                  >
                    <Text
                      style={[
                        styles.optionButtonText,
                        formData.pricing.minimumContract === period.value && styles.optionButtonTextActive,
                      ]}
                    >
                      {period.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          </>
        )}

        {formData.operationType === 'sale' && (
          <>
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Commission (Buyer) %</Text>
              <TextInput
                style={styles.input}
                value={formData.pricing.commissionBuyer}
                onChangeText={(text) => updatePricing({ commissionBuyer: text })}
                placeholder="Enter buyer commission"
                keyboardType="numeric"
                placeholderTextColor="#9CA3AF"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Commission (Seller) %</Text>
              <TextInput
                style={styles.input}
                value={formData.pricing.commissionSeller}
                onChangeText={(text) => updatePricing({ commissionSeller: text })}
                placeholder="Enter seller commission"
                keyboardType="numeric"
                placeholderTextColor="#9CA3AF"
              />
            </View>
          </>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 16,
  },
  section: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    gap: 16,
  },
  inputGroup: {
    gap: 8,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
  },
  input: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: '#1F2937',
  },
  priceInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 12,
    padding: 16,
    gap: 12,
  },
  priceInput: {
    flex: 1,
    fontSize: 16,
    color: '#1F2937',
    padding: 0,
  },
  optionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  optionButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
  },
  optionButtonActive: {
    backgroundColor: '#B89C4C',
  },
  optionButtonText: {
    fontSize: 14,
    color: '#4B5563',
    fontWeight: '500',
  },
  optionButtonTextActive: {
    color: '#fff',
  },
});