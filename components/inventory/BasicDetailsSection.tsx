import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { User, Building, MapPin, Home } from 'lucide-react-native';

const COLORS = {
  white: '#FFFFFF',
  gray: {
    200: '#E5E7EB',
    600: '#6B7280',
    900: '#111827',
  },
  gold: {
    light: '#D4B96D',
    medium: '#B89C4C',
    dark: '#8B7355',
    muted: 'rgba(184, 156, 76, 0.1)',
  },
};

interface BasicDetailsSectionProps {
  formData: {
    basicDetails: {
      landlord: {
        id: number;
        name: string;
        company_name?: string;
        mobile_1?: string;
        prefix_mobile_1?: string;
      };
      representative: {
        fullname: string;
        email: string;
        prefix_mobile_no: string;
        mobile_no: string;
        qatar_id_no: string;
        nationality_id: number;
      };
      country: {
        id: number;
        name: string;
      };
      location: {
        id: number;
        name: string;
      };
      tower: {
        id: number;
        name: string;
      };
      operationType: 'rent' | 'sale' | null;
      propertyType: number;
    }
  };
}

export default function BasicDetailsSection({ formData }: BasicDetailsSectionProps) {
  const { basicDetails } = formData;

  const InfoRow = ({ label, value }: { label: string; value: string }) => (
    <View style={styles.infoRow}>
      <Text style={styles.label}>{label}:</Text>
      <Text style={styles.value}>{value || 'N/A'}</Text>
    </View>
  );

  return (
    <View style={styles.container}>
      {/* Landlord Information */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <User size={20} color={COLORS.gold.dark} />
          <Text style={styles.sectionTitle}>Landlord Information</Text>
        </View>
        <View style={styles.infoCard}>
          <InfoRow label="Name" value={basicDetails.landlord?.name} />
          {basicDetails.landlord?.company_name && (
            <InfoRow label="Company" value={basicDetails.landlord.company_name} />
          )}
          {basicDetails.landlord?.mobile_1 && (
            <InfoRow 
              label="Mobile" 
              value={`${basicDetails.landlord.prefix_mobile_1} ${basicDetails.landlord.mobile_1}`} 
            />
          )}
        </View>
      </View>

      {/* Representative Details */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <User size={20} color={COLORS.gold.dark} />
          <Text style={styles.sectionTitle}>Representative Details</Text>
        </View>
        <View style={styles.infoCard}>
          <InfoRow label="Full Name" value={basicDetails.representative.fullname} />
          <InfoRow label="Email" value={basicDetails.representative.email} />
          <InfoRow 
            label="Mobile" 
            value={`${basicDetails.representative.prefix_mobile_no} ${basicDetails.representative.mobile_no}`} 
          />
          <InfoRow label="Qatar ID" value={basicDetails.representative.qatar_id_no} />
        </View>
      </View>

      {/* Location Information */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <MapPin size={20} color={COLORS.gold.dark} />
          <Text style={styles.sectionTitle}>Property Location</Text>
        </View>
        <View style={styles.infoCard}>
          <InfoRow label="Country" value={basicDetails.country?.name} />
          <InfoRow label="Location" value={basicDetails.location?.name} />
          {basicDetails.tower && (
            <InfoRow label="Tower" value={basicDetails.tower.name} />
          )}
        </View>
      </View>

      {/* Property Type Information */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Home size={20} color={COLORS.gold.dark} />
          <Text style={styles.sectionTitle}>Property Type</Text>
        </View>
        <View style={styles.infoCard}>
          <InfoRow 
            label="Operation Type" 
            value={basicDetails.operationType ? basicDetails.operationType.toUpperCase() : ''} 
          />
          <InfoRow 
            label="Property Type ID" 
            value={basicDetails.propertyType?.toString()} 
          />
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 16,
    gap: 24,
  },
  section: {
    gap: 12,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.gray[900],
  },
  infoCard: {
    backgroundColor: COLORS.white,
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: COLORS.gray[200],
    gap: 8,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  label: {
    fontSize: 14,
    color: COLORS.gray[600],
    width: 100, // Fixed width for labels
  },
  value: {
    fontSize: 14,
    color: COLORS.gray[900],
    flex: 1,
  },
});
