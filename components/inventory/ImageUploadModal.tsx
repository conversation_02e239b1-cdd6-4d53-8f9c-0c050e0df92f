import { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  Pressable,
  Modal,
  ActivityIndicator,
} from 'react-native';
import { ImagePlus, X, Check, CircleDot as DragHandleDots2, CircleAlert as AlertCircle } from 'lucide-react-native';
import * as ImagePicker from 'expo-image-picker';
import { api } from '@/lib/api';
import { Image as ImageType } from '@/types/inventory';

interface Props {
  visible: boolean;
  onClose: () => void;
  onSuccess: () => void;
  listingId: string;
  images: ImageType[];
}

const COLORS = {
  primary: '#F9FAFB',
  primaryLight: '#F3F4F6',
  accent: '#B89C4C',
  accentLight: '#D4B96D',
  success: '#10B981',
  warning: '#EF4444',
  background: '#FFFFFF',
  backgroundAlt: '#F8FAFC',
  text: '#111827',
  textMuted: '#6B7280',
  border: '#E5E7EB',
  gold: {
    light: '#D4B96D',
    medium: '#B89C4C',
    dark: '#8B7355',
    muted: 'rgba(184, 156, 76, 0.1)',
  },
};

export default function ImageUploadModal({ visible, onClose, onSuccess, listingId, images }: Props) {
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleImageUpload = async () => {
    try {
      const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (!permissionResult.granted) {
        setError('Permission to access camera roll is required!');
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [16, 9],
        quality: 0.8,
      });

      if (!result.canceled) {
        setIsUploading(true);
        setError(null);

        const formData = new FormData();
        formData.append('file', {
          uri: result.assets[0].uri,
          type: 'image/jpeg',
          name: 'upload.jpg',
        } as any);

        try {
          await api.post(`/crm/listing/${listingId}/image`, formData, {
            headers: {
              'Content-Type': 'multipart/form-data',
            },
          });

          onSuccess();
        } catch (err) {
          setError('Failed to upload image. Please try again.');
          console.error('Upload error:', err);
        }

        setIsUploading(false);
      }
    } catch (err) {
      setError('Failed to select image. Please try again.');
      console.error('Image picker error:', err);
      setIsUploading(false);
    }
  };

  const handleRemoveImage = async (imageName: string) => {
    try {
      await api.delete(`/crm/listing/${listingId}/image/${imageName}`);
      onSuccess();
    } catch (err) {
      setError('Failed to remove image. Please try again.');
      console.error('Remove image error:', err);
    }
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Property Images</Text>
          <Pressable onPress={onClose} style={styles.closeButton}>
            <X size={24} color={COLORS.text} />
          </Pressable>
        </View>

        <ScrollView style={styles.content}>
          {error && (
            <View style={styles.errorContainer}>
              <AlertCircle size={20} color={COLORS.warning} />
              <Text style={styles.errorText}>{error}</Text>
            </View>
          )}

          <View style={styles.uploadSection}>
            <Pressable
              style={[
                styles.uploadButton,
                isUploading && styles.uploadButtonDisabled
              ]}
              onPress={handleImageUpload}
              disabled={isUploading}>
              {isUploading ? (
                <>
                  <ActivityIndicator color={COLORS.accent} />
                  <Text style={styles.uploadText}>Uploading...</Text>
                </>
              ) : (
                <>
                  <ImagePlus size={24} color={COLORS.accent} />
                  <Text style={styles.uploadText}>Upload Images</Text>
                  <Text style={styles.uploadSubtext}>
                    Tap to select images from your device
                  </Text>
                </>
              )}
            </Pressable>
          </View>

          <View style={styles.imagesSection}>
            <Text style={styles.sectionTitle}>Property Images</Text>
            <Text style={styles.sectionSubtitle}>
              {images?.length || 0} photo{(images?.length || 0) !== 1 ? 's' : ''} uploaded
            </Text>

            {images?.map((image) => (
              <View key={image.img_url} style={styles.imageCard}>
                <View style={styles.imageCardHeader}>
                  <DragHandleDots2 size={20} color={COLORS.textMuted} />
                  <Pressable
                    style={styles.actionButton}
                    onPress={() => handleRemoveImage(image.name)}>
                    <X size={18} color={COLORS.warning} />
                  </Pressable>
                </View>

                <View style={styles.imageContainer}>
                  <Image source={{ uri: image.img_url }} style={styles.image} />
                </View>
              </View>
            ))}
          </View>
        </ScrollView>

        <View style={styles.footer}>
          <Pressable style={styles.saveButton} onPress={onClose}>
            <Check size={20} color={COLORS.background} />
            <Text style={styles.saveButtonText}>Done</Text>
          </Pressable>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.backgroundAlt,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    backgroundColor: COLORS.background,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.text,
  },
  closeButton: {
    padding: 8,
  },
  content: {
    flex: 1,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    padding: 16,
    backgroundColor: COLORS.warning + '10',
    marginBottom: 16,
  },
  errorText: {
    color: COLORS.warning,
    fontSize: 14,
  },
  uploadSection: {
    padding: 16,
  },
  uploadButton: {
    backgroundColor: COLORS.background,
    borderWidth: 2,
    borderColor: COLORS.border,
    borderStyle: 'dashed',
    borderRadius: 12,
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  uploadButtonDisabled: {
    opacity: 0.5,
  },
  uploadText: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text,
    marginTop: 12,
  },
  uploadSubtext: {
    fontSize: 14,
    color: COLORS.textMuted,
    marginTop: 4,
  },
  imagesSection: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text,
  },
  sectionSubtitle: {
    fontSize: 14,
    color: COLORS.textMuted,
    marginTop: 4,
    marginBottom: 16,
  },
  imageCard: {
    backgroundColor: COLORS.background,
    borderRadius: 12,
    marginBottom: 16,
    overflow: 'hidden',
  },
  imageCardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  actionButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: COLORS.primaryLight,
    alignItems: 'center',
    justifyContent: 'center',
  },
  imageContainer: {
    width: '100%',
    height: 200,
  },
  image: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  footer: {
    padding: 16,
    backgroundColor: COLORS.background,
    borderTopWidth: 1,
    borderTopColor: COLORS.border,
  },
  saveButton: {
    backgroundColor: COLORS.accent,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 8,
  },
  saveButtonText: {
    color: COLORS.background,
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
});