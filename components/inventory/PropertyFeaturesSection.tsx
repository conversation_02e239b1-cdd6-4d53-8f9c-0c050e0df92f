import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, TextInput, TouchableOpacity, Switch } from 'react-native';
import { MapPin } from 'lucide-react-native';
import { useBedrooms, useBathrooms, useKitchens, useAmenities } from '@/hooks/useConfiguration';
import MapSelector from '@/components/MapSelector';
import { Selector } from '../Selector';
import { Furnishing } from '@/types/inventory';

interface PropertyFeaturesProps {
  formData: {
    propertyFeatures: {
      size: string;
      parkingPlaces: string;
      bedrooms: string;
      bathrooms: string;
      kitchen: string;
      hasBalcony: boolean;
      furnishing: Furnishing;
      selectedAmenities: number[];
      location: {
        latitude: number | null;
        longitude: number | null;
      };
    };
  };
  onFormDataChange: (updates: Partial<any>) => void;
  onOpenMap: () => void;
}

const furnishingOptions = [
  'unfurnished',
  'semi-furnished',
  'fully-furnished',
  'partially-furnished'
] as const;

export default function PropertyFeaturesSection({
  formData,
  onFormDataChange,
  onOpenMap,
}: PropertyFeaturesProps) {
  const [isMapVisible, setIsMapVisible] = useState(false);
  const { data: bedrooms = [] } = useBedrooms();
  const { data: bathrooms = [] } = useBathrooms();
  const { data: kitchens = [] } = useKitchens();
  const { data: amenities = [] } = useAmenities();

  const updateFeatures = (updates: Partial<typeof formData.propertyFeatures>) => {
    onFormDataChange({
      propertyFeatures: {
        ...formData.propertyFeatures,
        ...updates
      }
    });
  };

  const handleLocationSelect = (location: { latitude: number; longitude: number }) => {
    updateFeatures({
      location: {
        latitude: location.latitude || null,
        longitude: location.longitude || null
      }
    });
  };

  const formatNumber = (value: string | null): string => {
    if (!value) return '';
    return value.toString();
  };

  const [selectedBedroom, setSelectedBedroom] = useState(bedrooms.find((eachBedroom: any) => eachBedroom.key === formData.propertyFeatures.bedrooms))
  const [selectedBathroom, setSelectedBathroom] = useState(bathrooms.find((eachBathroom: any) => eachBathroom.key === formData.propertyFeatures.bathrooms))

  useEffect(() => {
    updateFeatures({ bedrooms: selectedBedroom?.key })
  }, [selectedBedroom?.key]);

  useEffect(() => {
    updateFeatures({ bathrooms: selectedBathroom?.key })
  }, [selectedBathroom?.key]);

  return (
    <>
      <View style={styles.stepContent}>
        <Text style={styles.sectionTitle}>Property Features</Text>
        <Text style={styles.sectionDescription}>
          Enter the property's features and specifications
        </Text>

        <View style={styles.section}>
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Size (sqm)</Text>
            <TextInput
              style={styles.input}
              value={formatNumber(formData.propertyFeatures.size)}
              onChangeText={(text) => updateFeatures({ size: text })}
              placeholder="Enter property size"
              keyboardType="numeric"
              placeholderTextColor="#9CA3AF"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Parking Places</Text>
            <TextInput
              style={styles.input}
              value={formatNumber(formData.propertyFeatures.parkingPlaces)}
              onChangeText={(text) => updateFeatures({ parkingPlaces: text })}
              placeholder="Number of parking places"
              keyboardType="numeric"
              placeholderTextColor="#9CA3AF"
            />
          </View>

          <View style={[styles.inputGroup, { flex: 1, flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }]}>
            <Text style={styles.label}>Bedrooms</Text>
            <Selector
              items={bedrooms}
              selectedItem={selectedBedroom}
              onSelectionChange={(item) => setSelectedBedroom(item)}
            />
          </View>

          <View style={[styles.inputGroup, { flex: 1, flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }]}>
            <Text style={styles.label}>Bathrooms</Text>
            <Selector
              items={bathrooms}
              selectedItem={selectedBathroom}
              onSelectionChange={(item) => setSelectedBathroom(item)}
            />
          </View>

          {/* <View style={styles.inputGroup}>
            <Text style={styles.label}>Bedrooms</Text>
            <View style={styles.optionsGrid}>
              {bedrooms.map((option) => (
                <TouchableOpacity
                  key={option.key}
                  style={[
                    styles.optionButton,
                    formData.propertyFeatures.bedrooms === option.key && styles.optionButtonActive,
                  ]}
                  onPress={() => updateFeatures({ bedrooms: option.key })}
                >
                  <Text
                    style={[
                      styles.optionButtonText,
                      formData.propertyFeatures.bedrooms === option.key && styles.optionButtonTextActive,
                    ]}
                  >
                    {option.value}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Bathrooms</Text>
            <View style={styles.optionsGrid}>
              {bathrooms.map((option) => (
                <TouchableOpacity
                  key={option.key}
                  style={[
                    styles.optionButton,
                    formData.propertyFeatures.bathrooms === option.key && styles.optionButtonActive,
                  ]}
                  onPress={() => updateFeatures({ bathrooms: option.key })}
                >
                  <Text
                    style={[
                      styles.optionButtonText,
                      formData.propertyFeatures.bathrooms === option.key && styles.optionButtonTextActive,
                    ]}
                  >
                    {option.value}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View> */}

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Kitchen</Text>
            <View style={styles.optionsGrid}>
              {kitchens.map((option: any) => (
                <TouchableOpacity
                  key={option.key}
                  style={[
                    styles.optionButton,
                    formData.propertyFeatures.kitchen === option.key && styles.optionButtonActive,
                  ]}
                  onPress={() => updateFeatures({ kitchen: option.key })}
                >
                  <Text
                    style={[
                      styles.optionButtonText,
                      formData.propertyFeatures.kitchen === option.key && styles.optionButtonTextActive,
                    ]}
                  >
                    {option.value}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          <View style={styles.toggleContainer}>
            <Text style={styles.label}>Balcony</Text>
            <Switch
              value={formData.propertyFeatures.hasBalcony}
              onValueChange={(value) => updateFeatures({ hasBalcony: value })}
            />
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Furnishing</Text>
          <View style={styles.optionsGrid}>
            {furnishingOptions.map((option) => (
              <TouchableOpacity
                key={option}
                style={[
                  styles.optionButton,
                  formData.propertyFeatures.furnishing === option && styles.optionButtonActive,
                ]}
                onPress={() => updateFeatures({ furnishing: option })}
              >
                <Text
                  style={[
                    styles.optionButtonText,
                    formData.propertyFeatures.furnishing === option && styles.optionButtonTextActive,
                  ]}
                >
                  {option.charAt(0).toUpperCase() + option.slice(1)}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Amenities</Text>
          <View style={styles.amenitiesGrid}>
            {amenities.map((amenity) => (
              <TouchableOpacity
                key={amenity.key}
                style={[
                  styles.amenityButton,
                  formData.propertyFeatures.selectedAmenities.includes(amenity.key) && styles.amenityButtonActive,
                ]}
                onPress={() => {
                  const selectedAmenities = formData.propertyFeatures.selectedAmenities.includes(amenity.key)
                    ? formData.propertyFeatures.selectedAmenities.filter(key => key !== amenity.key)
                    : [...formData.propertyFeatures.selectedAmenities, amenity.key];
                  updateFeatures({ selectedAmenities });
                }}
              >
                <Text
                  style={[
                    styles.amenityButtonText,
                    formData.propertyFeatures.selectedAmenities.includes(amenity.key) && styles.amenityButtonTextActive,
                  ]}
                >
                  {amenity.value}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Location</Text>
          <TouchableOpacity
            style={styles.mapButton}
            onPress={() => setIsMapVisible(true)}
          >
            <MapPin size={20} color="#4B5563" />
            <Text style={styles.mapButtonText}>Select Location on Map</Text>
          </TouchableOpacity>
          {formData.propertyFeatures.location && (
            <Text style={styles.coordinates}>
              Lat: {formData.propertyFeatures.location?.latitude?.toFixed(4)},
              Long: {formData.propertyFeatures.location?.longitude?.toFixed(4)}
            </Text>
          )}
        </View>
      </View>

      <MapSelector
        visible={isMapVisible}
        onClose={() => setIsMapVisible(false)}
        onSelectLocation={handleLocationSelect}
        initialLocation={formData.propertyFeatures?.location}
      />
    </>
  );
}

const styles = StyleSheet.create({
  stepContent: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 16,
  },
  section: {
    marginBottom: 32,
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 8,
  },
  input: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: '#1F2937',
  },
  optionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  optionButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
  },
  optionButtonActive: {
    backgroundColor: '#B89C4C',
  },
  optionButtonText: {
    fontSize: 14,
    color: '#4B5563',
    fontWeight: '500',
  },
  optionButtonTextActive: {
    color: '#fff',
  },
  toggleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  amenitiesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  amenityButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
  },
  amenityButtonActive: {
    backgroundColor: '#B89C4C',
  },
  amenityButtonText: {
    fontSize: 14,
    color: '#4B5563',
    fontWeight: '500',
  },
  amenityButtonTextActive: {
    color: '#fff',
  },
  mapButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  mapButtonText: {
    fontSize: 16,
    color: '#4B5563',
  },
  coordinates: {
    fontSize: 14,
    color: '#6B7280',
    marginTop: 8,
  },
});
