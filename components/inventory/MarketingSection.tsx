import React from 'react';
import { View, Text, StyleSheet, TextInput, Switch } from 'react-native';
import { Globe, Youtube } from 'lucide-react-native';

interface MarketingSectionProps {
  formData: {
    marketing: {
      propertyFinder: boolean;
      qatarLiving: boolean;
      propertyoryx: boolean;
      jamesEdition: boolean;
      tourUrl: string;
      youtubeUrl: string;
    };
  };
  onFormDataChange: (updates: Partial<any>) => void;
}

export default function MarketingSection({
  formData,
  onFormDataChange,
}: MarketingSectionProps) {
  const updateMarketing = (updates: Partial<typeof formData.marketing>) => {
    onFormDataChange({
      marketing: {
        ...formData.marketing,
        ...updates,
      },
    });
  };

  return (
    <View style={styles.container}>
      <Text style={styles.sectionTitle}>Marketing Details</Text>
      <Text style={styles.sectionDescription}>
        Configure marketing platforms and media content
      </Text>

      <View style={styles.section}>
        <Text style={styles.groupTitle}>Publishing Platforms</Text>
        <View style={styles.togglesContainer}>
          <View style={styles.toggleRow}>
            <Text style={styles.label}>Property Finder</Text>
            <Switch
              value={formData.marketing.propertyFinder}
              onValueChange={(value) => updateMarketing({ propertyFinder: value })}
            />
          </View>

          <View style={styles.toggleRow}>
            <Text style={styles.label}>Qatar Living</Text>
            <Switch
              value={formData.marketing.qatarLiving}
              onValueChange={(value) => updateMarketing({ qatarLiving: value })}
            />
          </View>

          <View style={styles.toggleRow}>
            <Text style={styles.label}>Propertyoryx</Text>
            <Switch
              value={formData.marketing.propertyoryx}
              onValueChange={(value) => updateMarketing({ propertyoryx: value })}
            />
          </View>

          <View style={styles.toggleRow}>
            <Text style={styles.label}>JamesEdition</Text>
            <Switch
              value={formData.marketing.jamesEdition}
              onValueChange={(value) => updateMarketing({ jamesEdition: value })}
            />
          </View>
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.groupTitle}>Media Content</Text>
        
        <View style={styles.inputGroup}>
          <Text style={styles.label}>360° Tour URL</Text>
          <View style={styles.inputContainer}>
            <Globe size={20} color="#6B7280" />
            <TextInput
              style={styles.input}
              value={formData.marketing.tourUrl}
              onChangeText={(text) => updateMarketing({ tourUrl: text })}
              placeholder="Enter 360° tour URL"
              placeholderTextColor="#9CA3AF"
              autoCapitalize="none"
              autoCorrect={false}
              keyboardType="url"
            />
          </View>
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>YouTube Video URL</Text>
          <View style={styles.inputContainer}>
            <Youtube size={20} color="#6B7280" />
            <TextInput
              style={styles.input}
              value={formData.marketing.youtubeUrl}
              onChangeText={(text) => updateMarketing({ youtubeUrl: text })}
              placeholder="Enter YouTube video URL"
              placeholderTextColor="#9CA3AF"
              autoCapitalize="none"
              autoCorrect={false}
              keyboardType="url"
            />
          </View>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 24,
  },
  section: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
  },
  groupTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 16,
  },
  togglesContainer: {
    gap: 16,
  },
  toggleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: '#4B5563',
  },
  inputGroup: {
    marginBottom: 16,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    paddingHorizontal: 12,
    marginTop: 8,
    gap: 12,
  },
  input: {
    flex: 1,
    paddingVertical: 12,
    fontSize: 16,
    color: '#1F2937',
  },
});