import { StyleSheet } from 'react-native';
import Toast, { BaseToast, ErrorToast } from 'react-native-toast-message';

export const toastConfig = {
  success: (props: any) => (
    <BaseToast
      {...props}
      style={styles.successToast}
      contentContainerStyle={styles.contentContainer}
      text1Style={styles.text1}
      text2Style={styles.text2}
    />
  ),
  error: (props: any) => (
    <ErrorToast
      {...props}
      style={styles.errorToast}
      contentContainerStyle={styles.contentContainer}
      text1Style={styles.text1}
      text2Style={styles.text2}
    />
  ),
};

const styles = StyleSheet.create({
  successToast: {
    borderLeftColor: '#10B981',
    backgroundColor: '#ECFDF5',
  },
  errorToast: {
    borderLeftColor: '#DC2626',
    backgroundColor: '#FEF2F2',
  },
  contentContainer: {
    paddingHorizontal: 16,
  },
  text1: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
  },
  text2: {
    fontSize: 14,
    color: '#4B5563',
  },
});