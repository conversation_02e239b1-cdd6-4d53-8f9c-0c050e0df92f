import React, { useState, useCallback } from 'react';
import { View, TextInput, StyleSheet, Platform, ActivityIndicator } from 'react-native';
import { useDebounce } from '@/hooks/useDebounce';

interface SearchBarProps {
  value: string;
  onChangeText: (text: string) => void;
  placeholder?: string;
  icon?: React.ReactNode;
  debounceDelay?: number;
  isLoading?: boolean;
  keyboardType?: 'default' | 'email-address' | 'numeric' | 'phone-pad' | 'number-pad';
  maxLength?: number;
}

export default function SearchBar({ 
  value, 
  onChangeText, 
  placeholder, 
  icon,
  debounceDelay = 500,
  isLoading = false,
  keyboardType = 'default',
  maxLength,
}: SearchBarProps) {
  const [localValue, setLocalValue] = useState(value);
  const debouncedValue = useDebounce(localValue, debounceDelay);

  const handleChangeText = useCallback((text: string) => {
    setLocalValue(text);
  }, []);

  // Update parent component with debounced value
  React.useEffect(() => {
    if (debouncedValue !== value) {
      onChangeText(debouncedValue);
    }
  }, [debouncedValue, onChangeText, value]);

  // Update local value when parent value changes
  React.useEffect(() => {
    if (value !== localValue) {
      setLocalValue(value);
    }
  }, [value]);

  return (
    <View style={styles.container}>
      {icon && <View style={styles.icon}>{icon}</View>}
      <TextInput
        style={styles.input}
        value={localValue}
        onChangeText={handleChangeText}
        placeholder={placeholder}
        placeholderTextColor="#9CA3AF"
        returnKeyType="search"
        clearButtonMode="while-editing"
        autoCorrect={false}
        autoCapitalize="none"
        keyboardType={keyboardType}
        maxLength={maxLength}
      />
      {isLoading && (
        <ActivityIndicator 
          size="small" 
          color="#B89C4C"
          style={styles.loader}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F3F4F6',
    borderRadius: 12,
    paddingHorizontal: 16,
    height: 44,
  },
  icon: {
    marginRight: 12,
  },
  input: {
    flex: 1,
    fontSize: 16,
    color: '#1F2937',
    ...Platform.select({
      web: {
        outlineStyle: 'none',
      },
    }),
  },
  loader: {
    marginLeft: 12,
  },
});