import React from 'react';
import { View, Text, StyleSheet, Modal, TouchableOpacity, Platform } from 'react-native';
import { X, ArrowRight } from 'lucide-react-native';
import { router } from 'expo-router';
import { NotificationData } from '@/types/global';

interface NotificationDetailsModalProps {
  visible: boolean;
  onClose: () => void;
  notification: {
    id: number;
    title: string;
    body: string;
    sent_at: string;
    read_at: string | null;
    type?: string;
    parsedData?: NotificationData;
  };
}

export default function NotificationDetailsModal({
  visible,
  onClose,
  notification,
}: NotificationDetailsModalProps) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const handleAction = () => {
    if (notification.type === 'LEAD_ASSIGNMENT' && notification.parsedData?.leadId) {
      onClose();
      router.push(`/leads/${notification.parsedData?.leadId}`);
    }
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <View style={styles.modalContent}>
          <View style={styles.header}>
            <Text style={styles.headerTitle}>Notification Details</Text>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={onClose}
              hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
            >
              <X size={24} color="#6B7280" />
            </TouchableOpacity>
          </View>

          <View style={styles.content}>
            <Text style={styles.title}>{notification.title}</Text>
            <Text style={styles.message}>{notification.body}</Text>

            <View style={styles.metadata}>
              <View style={styles.metadataItem}>
                <Text style={styles.metadataLabel}>Sent</Text>
                <Text style={styles.metadataValue}>
                  {formatDate(notification.sent_at)}
                </Text>
              </View>

              {notification.read_at && (
                <View style={styles.metadataItem}>
                  <Text style={styles.metadataLabel}>Read</Text>
                  <Text style={styles.metadataValue}>
                    {formatDate(notification.read_at)}
                  </Text>
                </View>
              )}
            </View>

            {notification.parsedData?.type === 'LEAD_ASSIGNMENT' && notification.parsedData?.leadId && (
              <TouchableOpacity
                style={styles.actionButton}
                onPress={handleAction}
              >
                <Text style={styles.actionButtonText}>Go to Lead</Text>
                <ArrowRight size={20} color="#fff" />
              </TouchableOpacity>
            )}
          </View>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContent: {
    backgroundColor: '#fff',
    borderRadius: 12,
    width: '100%',
    maxWidth: Platform.select({ web: 480, default: '100%' }),
    maxHeight: '80%',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
  },
  closeButton: {
    padding: 4,
  },
  content: {
    padding: 20,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 12,
  },
  message: {
    fontSize: 16,
    color: '#4B5563',
    lineHeight: 24,
    marginBottom: 24,
  },
  metadata: {
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
    padding: 16,
    gap: 12,
  },
  metadataItem: {
    gap: 4,
  },
  metadataLabel: {
    fontSize: 12,
    color: '#6B7280',
    fontWeight: '500',
    textTransform: 'uppercase',
  },
  metadataValue: {
    fontSize: 14,
    color: '#1F2937',
  },
  actionButton: {
    backgroundColor: '#B89C4C',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 8,
    marginTop: 24,
    gap: 8,
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});