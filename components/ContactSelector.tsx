import React, { useState, useCallback } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, FlatList } from 'react-native';
import { Search, Phone, Mail, User as User2 } from 'lucide-react-native';
import { useQuery } from '@tanstack/react-query';
import { api } from '@/lib/api';
import SearchBar from '@/components/SearchBar';

interface Contact {
  id: number;
  name: string;
  email_1: string | null;
  prefix_mobile_1: string | null;
  mobile_1: string | null;
  company_name: string | null;
}

interface ContactSelectorProps {
  selectedContact?: Contact | null;
  onSelectContact: (contact: Contact) => void;
}

export default function ContactSelector({ selectedContact, onSelectContact }: ContactSelectorProps) {
  const [searchQuery, setSearchQuery] = useState('');

  const { data, isLoading, error } = useQuery({
    queryKey: ['contacts', searchQuery],
    queryFn: async () => {
      const { data } = await api.get('/contacts', {
        params: {
          q: searchQuery,
          limit: 100,
        },
      });
      return data;
    },
    enabled: searchQuery.length >= 2,
  });

  const contacts = data?.data ?? [];

  const renderContact = useCallback(({ item }: { item: Contact }) => {
    const isSelected = selectedContact?.id === item.id;

    return (
      <TouchableOpacity
        style={[styles.contactCard, isSelected && styles.contactCardSelected]}
        onPress={() => onSelectContact(item)}
      >
        <View style={styles.contactHeader}>
          <Text style={styles.contactName}>{item.name}</Text>
          {item.company_name && (
            <Text style={styles.companyName}>{item.company_name}</Text>
          )}
        </View>

        <View style={styles.contactDetails}>
          {item.mobile_1 && (
            <View style={styles.detailRow}>
              <Phone size={16} color="#6B7280" />
              <Text style={styles.detailText}>
                {item.prefix_mobile_1} {item.mobile_1}
              </Text>
            </View>
          )}
          {item.email_1 && (
            <View style={styles.detailRow}>
              <Mail size={16} color="#6B7280" />
              <Text style={styles.detailText}>{item.email_1}</Text>
            </View>
          )}
        </View>
      </TouchableOpacity>
    );
  }, [selectedContact]);

  return (
    <View style={styles.container}>
      <SearchBar
        value={searchQuery}
        onChangeText={setSearchQuery}
        placeholder="Search contacts..."
        icon={<Search size={20} color="#6B7280" />}
        isLoading={isLoading}
      />

      {error ? (
        <View style={styles.messageContainer}>
          <Text style={styles.errorText}>Error loading contacts</Text>
        </View>
      ) : searchQuery.length < 2 ? (
        <View style={styles.messageContainer}>
          <User2 size={24} color="#9CA3AF" />
          <Text style={styles.messageText}>
            Type at least 2 characters to search contacts
          </Text>
        </View>
      ) : contacts.length === 0 ? (
        <View style={styles.messageContainer}>
          <User2 size={24} color="#9CA3AF" />
          <Text style={styles.messageText}>No contacts found</Text>
        </View>
      ) : (
        <FlatList
          data={contacts}
          renderItem={renderContact}
          keyExtractor={(item) => item.id.toString()}
          contentContainerStyle={styles.listContent}
          keyboardShouldPersistTaps="handled"
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginTop: 16,
  },
  listContent: {
    paddingTop: 16,
    gap: 12,
  },
  contactCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  contactCardSelected: {
    borderColor: '#B89C4C',
    backgroundColor: '#FFFBEB',
  },
  contactHeader: {
    marginBottom: 12,
  },
  contactName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 4,
  },
  companyName: {
    fontSize: 14,
    color: '#6B7280',
  },
  contactDetails: {
    gap: 8,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  detailText: {
    fontSize: 14,
    color: '#4B5563',
  },
  messageContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 32,
    gap: 12,
  },
  messageText: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
  },
  errorText: {
    fontSize: 14,
    color: '#EF4444',
    textAlign: 'center',
  },
});