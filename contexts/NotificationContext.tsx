import React, { createContext, useContext, useEffect, useRef, useState } from 'react';
import { Platform } from 'react-native';
import * as Notifications from 'expo-notifications';
import { router } from 'expo-router';
import { useAuth } from './AuthContext';
import { useProfile } from './ProfileContext';
import { registerForPushNotificationsAsync, configurePushNotifications, registerTokenWithBackend } from '@/lib/notifications';
import { log } from '@/lib/remote-logging-transport';

type NotificationContextType = {
  schedulePushNotification: (title: string, body: string, data?: any) => Promise<void>;
  hasPermission: boolean;
};

const NotificationContext = createContext<NotificationContextType>({
  schedulePushNotification: async () => {},
  hasPermission: false,
});

export function NotificationProvider({ children }: { children: React.ReactNode }) {
  const [hasPermission, setHasPermission] = useState(false);
  const notificationListener = useRef<any>();
  const responseListener = useRef<any>();
  const { session } = useAuth();
  const { profile, updateProfile } = useProfile();
  const tokenRegistrationAttempted = useRef(false);

  const checkPermission = async () => {
    if (Platform.OS === 'web') {
      setHasPermission(false);
      return;
    }

    const { status: existingStatus } = await Notifications.getPermissionsAsync();
    let finalStatus = existingStatus;
    if (existingStatus !== 'granted') {
      const { status } = await Notifications.requestPermissionsAsync();
      finalStatus = status;
    }
    setHasPermission(finalStatus === 'granted');
  };

  useEffect(() => {
    if (session?.token) {
      checkPermission();
    }
  }, [session]);

  useEffect(() => {
    log.info('Notification setup:', { hasPermission, session, profile });
    if (!session || !profile || tokenRegistrationAttempted.current || !hasPermission) {
      return;
    }

    const initializeNotifications = async () => {
      configurePushNotifications();
      try {
        const { data: fullToken } = await Notifications.getExpoPushTokenAsync();
        const token = fullToken.match(/\[(.*?)\]/)?.[1]; // extract token inside brackets
        log.info('Push token obtained:', token);
        console.log('Token sent to DB:', token);
        if (token) {
          const existingTokens = profile.notification_tokens || [];
          const registered = await registerTokenWithBackend(token, existingTokens);
          if (registered) {
            await updateProfile({
              ...profile,
              notification_tokens: [...existingTokens, token],
            });
          }
        }
      } catch (error) {
        log.error('Error during token registration:', error);
      }
      tokenRegistrationAttempted.current = true;

      notificationListener.current = Notifications.addNotificationReceivedListener(n => console.log('Received notification:', n));
      responseListener.current = Notifications.addNotificationResponseReceivedListener(response => {
        const data = response.notification.request.content.data;
        if (data.leadId) router.push(`/leads/${data.leadId}`);
        else if (data.taskId) router.push('/tasks');
        else if (data.type === 'notification') router.push('/notifications');
      });
    };

    initializeNotifications();

    return () => {
      if (notificationListener.current) Notifications.removeNotificationSubscription(notificationListener.current);
      if (responseListener.current) Notifications.removeNotificationSubscription(responseListener.current);
    };
  }, [session, profile, hasPermission]);

  const schedulePushNotification = async (title: string, body: string, data?: any) => {
    if (Platform.OS === 'web' || !session || !hasPermission) return;
    try {
      const notificationId = await Notifications.scheduleNotificationAsync({
        content: { title, body, data: data || {}, sound: true, priority: Notifications.AndroidNotificationPriority.MAX },
        trigger: null,
      });
      console.log('Scheduled notification ID:', notificationId);
    } catch (error) {
      console.error('Error scheduling notification:', error);
    }
  };

  return (
    <NotificationContext.Provider value={{ schedulePushNotification, hasPermission }}>
      {children}
    </NotificationContext.Provider>
  );
}

export const useNotifications = () => useContext(NotificationContext);
