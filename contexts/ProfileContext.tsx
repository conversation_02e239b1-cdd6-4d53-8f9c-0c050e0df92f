import React, { createContext, useContext } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { api } from '@/lib/api';
import { useAuth } from './AuthContext';

export interface Profile {
  id: number;
  email: string;
  name: string;
  phone: string | null;
  position: string | null;
  languages: string | null;
  short_bio: string | null;
  profile_image_url: string | null;
  rating: string | null;
  notification_tokens: string[],
  mobile_app_settings: {
    [key: string]: any;
  };
}

interface ProfileContextType {
  profile: Profile | null;
  loading: boolean;
  error: string | null;
  updateProfile: (data: Partial<Profile>) => Promise<void>;
  refreshProfile: () => Promise<void>;
}

const ProfileContext = createContext<ProfileContextType>({
  profile: null,
  loading: false,
  error: null,
  updateProfile: async () => {},
  refreshProfile: async () => {},
});

export function ProfileProvider({ children }: { children: React.ReactNode }) {
  const queryClient = useQueryClient();
  const { session } = useAuth();

  const { data: profile, isLoading: loading, error, refetch } = useQuery({
    queryKey: ['profile'],
    queryFn: async () => {
      const { data } = await api.get<Profile>('/profile');
      return data;
    },
    enabled: !!session?.token,
  });

  const updateProfile = async (data: Partial<Profile>) => {
    try {
      const response = await api.patch<Profile>('/profile', data);
      // Update the cache immediately
      queryClient.setQueryData(['profile'], response.data);
      return response.data;
    } catch (err: any) {
      throw err;
    }
  };

  return (
    <ProfileContext.Provider
      value={{
        profile: profile || null,
        loading,
        error: error ? (error as Error).message : null,
        updateProfile,
        refreshProfile: refetch,
      }}
    >
      {children}
    </ProfileContext.Provider>
  );
}

export const useProfile = () => useContext(ProfileContext);
