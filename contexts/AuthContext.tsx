import { createContext, useContext, useState, useEffect } from 'react';
import { Platform, DeviceEventEmitter, NativeEventEmitter } from 'react-native';
import { router } from 'expo-router';
import { login as apiLogin } from '@/lib/api';
import { storage } from '@/lib/storage';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useQueryClient } from '@tanstack/react-query';
import { User, Session, LoginCredentials } from '@/types/auth';

type AuthContextType = {
  session: Session | null;
  user: User | null;
  loading: boolean;
  error: string | null;
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => Promise<void>;
};

const AuthContext = createContext<AuthContextType>({
  session: null,
  user: null,
  loading: false,
  error: null,
  login: async () => {},
  logout: async () => {},
});

// Create a platform-specific event emitter
const eventEmitter = Platform.OS === 'web' 
  ? new NativeEventEmitter() 
  : DeviceEventEmitter;

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const queryClient = useQueryClient();

  const handleLogin = async (credentials: LoginCredentials) => {
    setLoading(true);
    setError(null);
    try {
      const response = await apiLogin(credentials);
      const newSession: Session = {
        user: response.user,
        token: response.token,
        refreshToken: response.refreshToken,
      };
      await storage.setSession(newSession);
      setSession(newSession);
      router.replace('/(app)/(tabs)/leads');
    } catch (err: any) {
      setError(err.response?.data?.message || 'Login failed');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = async () => {
    try {
      // Clear secure storage including credentials
      await storage.clearAll();

      // Clear AsyncStorage
      await AsyncStorage.clear();

      // Clear all React Query caches
      await queryClient.clear();

      // Clear session state
      setSession(null);

      // Navigate to login
      router.replace('/login');
    } catch (err) {
      console.error('Error during logout:', err);
      // Still navigate to login even if there's an error clearing storage
      router.replace('/login');
    }
  };

  useEffect(() => {
    const checkAuth = async () => {
      try {
        const savedSession = await storage.getSession();
        if (savedSession?.token) {
          setSession(savedSession);
          if (router.canGoBack()) {
            router.replace('/(app)/(tabs)/leads');
          }
        } else {
          if (router.canGoBack()) {
            router.replace('/login');
          }
        }
      } catch (err) {
        console.error('Error checking auth:', err);
      } finally {
        setLoading(false);
      }
    };
    
    checkAuth();

    // Listen for unauthorized events using the platform-specific emitter
    const subscription = eventEmitter.addListener('unauthorized', handleLogout);

    return () => {
      subscription.remove();
    };
  }, []);

  return (
    <AuthContext.Provider 
      value={{
        session,
        user: session?.user ?? null,
        loading,
        error,
        login: handleLogin,
        logout: handleLogout,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export const useAuth = () => useContext(AuthContext);
