export type TaskViewType = 'all' | 'open' | 'overdue' | 'today' | 'completed' | 'upcoming';

export interface Task {
  id: number;
  lead_id: number | null;
  subject: string;
  lead: {
    id: number | null;
    name: string | null;
  };
  due_date: string;
  status: string;
  created_at: string;
  ct_contact_name: string | null;
  object_type: 'lead' | 'contact';
  ct_contact_id: number | null;
}

export interface TasksResponse {
  data: Task[];
  meta: {
    total: number;
    current_page: number;
    per_page: string;
  };
}