export interface LeadMetadata {
  utm_source: string | null;
  utm_campaign: string | null;
  gad_source: string | null;
  network: string | null;
  fbclid: string | null;
  form_id: string | null;
  form_name: string | null;
}

export interface LeadUser {
  id: number;
  name: string;
  email: string;
  position: string;
  prefix_phone: string;
  phone: string;
  profile_image: string | null;
  rating: string | null;
  short_bio: string | null;
  languages: string | null;
}

export interface LeadAssignment {
  id: number;
  lead_id: number;
  user_id: number;
  created_at: string;
  updated_at: string;
  user: LeadUser;
}

export interface LeadContact {
  id: number;
  name: string;
  company_name: string | null;
  prefix_mobile_1: string | null;
  mobile_1: string | null;
  prefix_mobile_2: string | null;
  mobile_2: string | null;
  email_1: string | null;
  email_2: string | null;
  country: string | null;
  address: string | null;
}

export interface OperationHistory {
  id: number;
  model_id: number;
  model_type: string;
  content: string;
  created_at: string;
  created_by: number;
}

export interface Lead {
  id: number;
  contact_id: number;
  requirements: string | null;
  filter_bedrooms: number | null;
  filter_bathrooms: number | null;
  filter_property_type: number;
  filter_operation_type: string;
  filter_budget_min: number | null;
  filter_budget_max: number | null;
  created_at: string;
  updated_at: string;
  platform_from: string;
  lead_status_id: number;
  rating: string;
  lead_metadata: LeadMetadata | null;
  latest_assignment: LeadAssignment;
  contact: LeadContact;
  operation_history: OperationHistory[];
  inquired_ref_no?: string;
  resolved_platform?: string;
}