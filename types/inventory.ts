export interface Tower {
  id: number;
  name: string;
}

export interface Image {
  name: string;
  img_url: string;
  is_primary: boolean;
}

export interface Listing {
  id: number;
  ref_no: string;
  created_at: string;
  ad_type: string;
  created_by: number;
  status: string;
  asset_id: number;
  location: string;
  property_type: string;
  bedrooms_no: number | null;
  bathrooms_no: number | null;
  tower: Tower;
  title: string;
  contact_to_view: string | null;
  keys_place: string;
  property_name: string;
  views: any[];
  transfer_fee: string | null;
  commission_ll: string;
  commission_buyer: string | null;
  payment_method: string;
  commission_tenant: string;
  prorated_rata: number;
  title_deed: number;
  bills: string;
  offers: string;
  payment_booking_fee: string | null;
  payment_cheque_addressed_to: string | null;
  payment_closing_requiring_documents: string;
  completion_year: number;
  kitchen: number;
  amenities: string[];
  service_charge: number;
  unit_no: string;
  price: string;
  best_price: string;
  publishing_status: string;
  can_change_status: boolean;
  images: Image[];
}

export interface ListingsResponse {
  data: Listing[];
  total: number;
}

export interface Tower {
  id: number;
  name: string;
}

export interface View {
  id: number;
  name: string;
}

export interface Location {
  id: number;
  name: string;
  path: string;
}

export interface Agent {
  id: number;
  email: string;
  name: string;
  position: string;
}

export interface Representative {
  fullname: string;
  email: string;
  mobile_no: string;
  prefix_mobile_no: string;
  qatar_id_no: string;
  nationality_id: number;
}

export interface Amenity {
  id: number;
  name: string;
}

export interface Attribute {
  key: string;
  value: string | number | null;
}

export interface DescriptionBlock {
  tag_name: string;
  content: string;
}

export interface ListingDetails {
  asset_id: number;
  id: number;
  ref_no: string;
  title: string;
  name: string | null;
  ad_type: string;
  property_type: string;
  property_type_id: number;
  country_id: number;
  country_name: string;
  contact_id: number;
  contact_name: string;
  offplan: number;
  is_exclusive: number;
  is_homepage_promoted: number;
  is_investment_opportunity: number;
  developer: string | null;
  key_access: string;
  status: ListingStatus;
  unit_no: string;
  title_deed: number;
  reffered_by_id: number;
  reffered_by_name: string;
  publishing_status: string;
  available_when: string | null;
  price_on_request: number;
  price: number;
  best_price: number;
  offers: string;
  commission_tenant: string | null;
  commission_ll: number;
  commission_seller: string | null;
  commission_buyer: number;
  prorated_rata: string | null;
  minimum_contract: string | null;
  handover_date: string | null;
  propertyfinder_title: string;
  payment_booking_fee: string | null;
  payment_method: string;
  payment_cheques_no: number;
  payment_cheque_addressed_to: string | null;
  payment_closing_requiring_documents: string | null;
  payment_security_deposit_dated: string | null;
  location: Location;
  geo_lat: number;
  geo_lon: number;
  images: Image[];
  contact_to_view: string | null;
  attributes: Attribute[];
  description: DescriptionBlock[];
  description_raw: string;
  agent: Agent;
  representative: Representative;
  tower: Tower;
  views: View[];
  amenities: Amenity[];
  brochure: string | null;
  documents: any[];
  embeed_youtube: string | null;
  tour_360: string | null;
}

export interface FormData {
  basicDetails: {
    landlord: any;
    representative: {
      fullname: string;
      email: string;
      prefix_mobile_no: string;
      mobile_no: string;
      qatar_id_no: string;
      nationality_id: number;
    };
    country: any;
    location: any;
    tower: any;
    operationType: OperationType;
    propertyType: number;
  };
  propertyDetails: {
    isExclusive: boolean;
    isHomepagePromoted: boolean;
    isInvestmentOpportunity: boolean;
    developerName: string;
    unitNo: string;
    keyAccess: string;
    status: ListingStatus;
    completionYear: string;
    titleDeed?: string;
    referral: {
      id: number;
      name: string;
    } | null;
  };
  propertyFeatures: {
    size: string;
    parkingPlaces: string;
    bedrooms: string;
    bathrooms: string;
    kitchen: string;
    hasBalcony: boolean;
    furnishing: Furnishing;
    selectedAmenities: number[];
    location: {
      latitude: number;
      longitude: number;
    };
  };
  pricing: {
    listedPrice: string;
    bestPrice: string;
    offers: string;
    paymentMethod: PaymentMethod;
    proratedAmount: string;
    numberOfCheques: string;
    commissionTenant: string;
    commissionLandlord: string;
    minimumContract: number;
    commissionBuyer: string;
    commissionSeller: string;
  };
  marketing: {
    propertyFinder: boolean;
    qatarLiving: boolean;
    propertyoryx: boolean;
    jamesEdition: boolean;
    tourUrl: string;
    youtubeUrl: string;
  };
  description: {
    title: {
      en: string;
      ar: string;
    };
    propertyFinderTitle: {
      en: string;
      ar: string;
    };
    description: {
      en: string;
      ar: string;
    };
    images: Array<Image>;
  };
}

export interface PropertyPayload {
  representative?: {
    fullname?: string;
    email?: string;
    mobile_no?: string;
    prefix_mobile_no?: string;
    nationality_id?: number;
    qatar_id_no?: string;
  };
  contact_id: number;
  ad_type: OperationType;
  property_type_id: number;
  developer?: string;
  keys_place?: string;
  status?: ListingStatus;
  available_when?: string;
  unit_no?: string;
  is_exclusive?: 0 | 1;
  is_homepage_promoted?: 0 | 1;
  is_investment_opportunity?: 0 | 1;
  tower_id?: number;
  title?: string;
  title_deed?: 0 | 1;
  contact_to_view?: string;
  country_id: number;
  location_id: number;
  price?: number;
  best_price?: number;
  offers?: number;
  commission_ll?: number;
  commission_tenant?: number;
  commission_buyer?: number;
  prorated_rata?: string;
  payment_method?: PaymentMethod;
  payment_years?: number;
  payment_cheques_no?: number;
  payment_plan_info?: string;
  payment_cheque_addressed_to?: string;
  payment_closing_requiring_documents?: string;
  payment_booking_fee?: number;
  payment_security_deposit_dated?: 0 | 1;
  rented_for?: string;
  minimum_contract?: number;
  geo_lat?: string;
  geo_lon?: string;
  operation_history?: string;
  youtube_link?: string;
  brochure?: {
    content?: string;
  };
  documents?: any[];
  attributes?: {
    description?: string;
    construction_year?: number;
    build_up_area?: number;
    bedrooms?: string;
    bathrooms?: string;
    balcony?: 0 | 1;
    kitchen?: 'open' | 'closed' | 'semi-opened';
    pantry?: 'private' | 'common';
    service_charge?: number;
    transfer_fee?: number;
    parking_info?: string;
    views?: number[];
    amenities?: any[];
    bills?: Array<Bill>;
    furnishings?: Furnishing;
  };
  images?: Array<Image>;
  embeed_youtube?: string;
  tour_360?: string;
}

export interface ValidationErrors {
  [key: string]: string[];
}

export interface PropertyResponse {
  id: number;
  refNo: string;
  publishingStatus: string;
  agent: {
    id: number;
    name: string;
  };
}

export type PaymentMethod = 'cash' | 'cheque' | 'mortgage' | 'in_house_down_payment_plan' | 'developer_payment_plan';
export type OperationType = 'rent' | 'sale';
export type Bill = 'all-excluded' | 'all-included' | 'karamaa-included' | 'qatar-cool-included' | 'internet-included';
export type ListingStatus = 'available' | 'to-be-available' | 'rented' | 'occupied' | 'sold';
export type Furnishing = 'unfurnished' | 'semi-furnished' | 'fully-furnished' |
  'partially-furnished' | 'core-and-shell' | 'fitted' |
  'semi-fitted' | 'ceiling-only' | 'flooring-only'
