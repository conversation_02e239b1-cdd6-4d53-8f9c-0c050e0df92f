export interface KeyValue {
    key: string;
    value: string;
}

export interface ListingType {
    id: number;
    label: string;
    url_value: string;
}

export interface LeadWrite {
    contact_id: number;
    ad_type: string;
    agent_id: number;
    status_id: number;
    platform_from: number | undefined;
    remarks: string;
    request: {
        propertyTypes: Array<number>;
        amenities: Array<number>;
        balcony: boolean;
        bathrooms: string;
        bedrooms: Array<string>;
        kitchen: string;
        listingViews: Array<number>;
        maxArea: number;
        maxPrice: number;
        minArea: number;
        minPrice: number;
        pantry: string;
        parking: boolean;
        operationType: 'rent' | 'sale';
        requirements: string;
    }
}

export interface IdNamePack {
    id: number;
    name: string;
}

export interface NotificationData {
    leadId?: number;
    type?: string;
    [key: string]: any;
}

export interface Notification {
    id: number;
    title: string;
    body: string;
    sent_at: string;
    read_at: string | null;
    payload?: string;
    parsedData?: NotificationData;
}

// New interfaces moved from leads.tsx
export interface Lead {
    id: number;
    contact_name: string;
    contact_email_1: string;
    contact_mobile_1: string;
    contact_prefix_mobile_1: string;
    complete_phone_no: string;
    lead_status_name: string | null;
    lead_status_background_color: string;
    last_call_timing: string;
    assignment_user_name: string;
    rating: string;
    requirements: string;
    created_at: string;
    [key: string]: any;
}

export interface LeadFilters {
    status: string;
    sort: string;
    propertyType: number | null;
    listType: string;
    page: number;
}

export interface LeadFilterDialogProps {
    isVisible: boolean;
    onClose: () => void;
    filters: LeadFilters;
    onFilterChange: (updates: Partial<LeadFilters>) => void;
    propertyTypes: Array<ListingType>;
    isLoading: boolean;
}

export interface LeadsResponse {
    leads: Lead[];
    total: number;
}

export interface Country {
    id: number;
    code: string;
    name: string;
    phone_prefix: string;
}

export interface Task {
    id: number;
    subject: string;
    status: 'not_started' | 'in_progress' | 'completed' | 'overdue';
    due_date: string;
    created_at: string;
    object_type: 'lead' | 'contact';
    lead_id: number | null;
    ct_contact_id: number | null;
    ct_contact_name: string | null;
    automatization: any | null;
    lead: {
        id: number;
        name: string;
    } | null;
}

export interface TasksResponse {
    data: Task[];
    meta: {
        total: number;
        current_page: number;
        per_page: number;
    };
}
