// Dropdown options for property search and filtering
// These values match the exact options from the property search interface

export const RENT_SALE_OPTIONS = [
  { id: 'rent', label: 'Rent' },
  { id: 'sale', label: 'Sale' },
  { id: 'rent_sale', label: 'Rent/Sale' },
];

export const MIN_AREA_OPTIONS = [
  { id: '10', label: '10 sqm' },
  { id: '20', label: '20 sqm' },
  { id: '45', label: '45 sqm' },
  { id: '90', label: '90 sqm' },
  { id: '120', label: '120 sqm' },
  { id: '150', label: '150 sqm' },
  { id: '200', label: '200 sqm' },
  { id: '250', label: '250 sqm' },
  { id: '300', label: '300 sqm' },
  { id: '400', label: '400 sqm' },
  { id: '500', label: '500 sqm' },
  { id: '600', label: '600 sqm' },
  { id: '650', label: '650 sqm' },
  { id: '1000', label: '1000 sqm' },
  { id: '1500', label: '1500 sqm' },
  { id: '2000', label: '2000 sqm' },
  { id: '2500', label: '2500 sqm' },
  { id: '3000', label: '3000 sqm' },
  { id: '3500', label: '3500 sqm' },
  { id: '4000', label: '4000 sqm' },
  { id: '4500', label: '4500 sqm' },
  { id: '5000', label: '5000 sqm' },
];

export const MAX_AREA_OPTIONS = [
  { id: '150', label: '150 sqm' },
  { id: '200', label: '200 sqm' },
  { id: '350', label: '350 sqm' },
  { id: '450', label: '450 sqm' },
  { id: '500', label: '500 sqm' },
  { id: '1000', label: '1000 sqm' },
  { id: '2000', label: '2000 sqm' },
  { id: '3000', label: '3000 sqm' },
  { id: '4000', label: '4000 sqm' },
  { id: '5000', label: '5000 sqm' },
  { id: '6000', label: '6000 sqm' },
  { id: '7000', label: '7000 sqm' },
  { id: '8000', label: '8000 sqm' },
  { id: '9000', label: '9000 sqm' },
  { id: '10000', label: '10000 sqm' },
];

export const PRICE_MIN_OPTIONS = [
  { id: '4000', label: '4.000 QAR' },
  { id: '5000', label: '5.000 QAR' },
  { id: '6000', label: '6.000 QAR' },
  { id: '7000', label: '7.000 QAR' },
  { id: '8000', label: '8.000 QAR' },
  { id: '9000', label: '9.000 QAR' },
  { id: '10000', label: '10.000 QAR' },
  { id: '11000', label: '11.000 QAR' },
  { id: '12000', label: '12.000 QAR' },
  { id: '13000', label: '13.000 QAR' },
  { id: '14000', label: '14.000 QAR' },
  { id: '15000', label: '15.000 QAR' },
  { id: '16000', label: '16.000 QAR' },
  { id: '17000', label: '17.000 QAR' },
  { id: '18000', label: '18.000 QAR' },
  { id: '19000', label: '19.000 QAR' },
  { id: '20000', label: '20.000 QAR' },
];

export const PRICE_MAX_OPTIONS = [
  { id: '6000', label: '6.000 QAR' },
  { id: '7000', label: '7.000 QAR' },
  { id: '8000', label: '8.000 QAR' },
  { id: '9000', label: '9.000 QAR' },
  { id: '10000', label: '10.000 QAR' },
  { id: '12000', label: '12.000 QAR' },
  { id: '14000', label: '14.000 QAR' },
  { id: '16000', label: '16.000 QAR' },
  { id: '18000', label: '18.000 QAR' },
  { id: '20000', label: '20.000 QAR' },
  { id: '22000', label: '22.000 QAR' },
  { id: '24000', label: '24.000 QAR' },
  { id: '26000', label: '26.000 QAR' },
  { id: '28000', label: '28.000 QAR' },
  { id: '30000', label: '30.000 QAR' },
  { id: '40000', label: '40.000 QAR' },
  { id: '50000', label: '50.000 QAR' },
  { id: '60000', label: '60.000 QAR' },
  { id: '70000', label: '70.000 QAR' },
  { id: '80000', label: '80.000 QAR' },
  { id: '90000', label: '90.000 QAR' },
  { id: '100000', label: '100.000 QAR' },
  { id: '120000', label: '120.000 QAR' },
  { id: '150000', label: '150.000 QAR' },
  { id: '170000', label: '170.000 QAR' },
  { id: '200000', label: '200.000 QAR' },
];

// Helper function to transform API data to dropdown format
export const transformApiDataToDropdownOptions = (data: any[]) => {
  return data.map((item: any) => ({
    id: item.id?.toString() || item.value?.toString(),
    label: item.name || item.label || item.value,
  }));
};
