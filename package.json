{"name": "bolt-expo-starter", "main": "expo-router/entry", "version": "1.0.23", "private": true, "scripts": {"dev": "EXPO_NO_TELEMETRY=1 expo start", "build:web": "expo export --platform web", "lint": "expo lint", "start": "expo start --dev-client", "android": "expo run:android", "ios": "expo run:ios"}, "dependencies": {"@expo/vector-icons": "^14.0.2", "@lucide/lab": "^0.1.2", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/datetimepicker": "^8.4.2", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@tanstack/react-query": "^5.17.19", "axios": "^1.6.7", "date-fns": "^4.1.0", "expo": "~52.0.42", "expo-blur": "^14.0.3", "expo-constants": "^17.0.5", "expo-dev-client": "~5.0.19", "expo-device": "~7.0.3", "expo-font": "^13.0.3", "expo-haptics": "^14.0.1", "expo-image-picker": "~16.0.6", "expo-linear-gradient": "^14.0.2", "expo-linking": "^7.0.5", "expo-location": "~18.0.10", "expo-notifications": "~0.29.14", "expo-router": "~4.0.20", "expo-secure-store": "~14.0.1", "expo-splash-screen": "^0.29.21", "expo-status-bar": "^2.0.1", "expo-symbols": "^0.2.2", "expo-system-ui": "~4.0.9", "expo-web-browser": "^14.0.2", "lucide-react-native": "^0.475.0", "react": "18.3.1", "react-dom": "18.3.1", "react-native": "0.76.9", "react-native-chart-kit": "^6.12.0", "react-native-gesture-handler": "~2.20.2", "react-native-logs": "^5.3.0", "react-native-maps": "1.18.0", "react-native-pell-rich-editor": "^1.9.0", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-svg": "15.8.0", "react-native-toast-message": "^2.2.1", "react-native-url-polyfill": "^2.0.0", "react-native-web": "^0.19.13", "react-native-webview": "13.12.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~18.3.12", "typescript": "^5.3.3"}}